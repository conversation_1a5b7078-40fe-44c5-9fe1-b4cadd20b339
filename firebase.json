{"flutter": {"platforms": {"android": {"default": {"projectId": "interviewhammer1", "appId": "1:342275778166:android:f0c6d5ae3e1d0ba37da157", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "interviewhammer1", "appId": "1:342275778166:ios:392762741ad01c977da157", "uploadDebugSymbols": true, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "interviewhammer1", "appId": "1:342275778166:ios:82509e97d7bdde477da157", "uploadDebugSymbols": true, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "interviewhammer1", "configurations": {"ios": "1:342275778166:ios:392762741ad01c977da157"}}}}}, "hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "frameworksBackend": {"region": "us-central1"}}}
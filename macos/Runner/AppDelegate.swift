import Cocoa
import FlutterMacOS
import ApplicationServices

@main
class AppDelegate: FlutterAppDelegate {
  override func applicationDidFinishLaunching(_ notification: Notification) {
    guard let flutterWindow = NSApplication.shared.windows.first,
          let controller = flutterWindow.contentViewController as? FlutterViewController else {
      super.applicationDidFinishLaunching(notification)
      return
    }

#if !STORE_BUILD
    let channel = FlutterMethodChannel(name: "app_name_control", binaryMessenger: controller.engine.binaryMessenger)

    channel.setMethodCallHandler { call, result in
      if call.method == "setProcessDisplayName", let name = call.arguments as? String {
        ProcessNameUtil.setProcessDisplayName(name)
        result(nil)
      } else {
        result(FlutterMethodNotImplemented)
      }
    }
#endif

    super.applicationDidFinishLaunching(notification)
  }

  override func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
    return false
  }

  override func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
    return true
  }
  
  override func applicationShouldHandleReopen(_ sender: NSApplication, hasVisibleWindows flag: Bool) -> Bool {
    for window in sender.windows {
      window.makeKeyAndOrderFront(nil)
    }
    return true
  }
  
  override func applicationDidBecomeActive(_ notification: Notification) {
    let app = NSApplication.shared
    for window in app.windows {
      if !window.isVisible {
        window.makeKeyAndOrderFront(nil)
      }
    }
  }
}

#if !STORE_BUILD
// Private LaunchServices symbols types
private typealias LSGetCurrentApplicationASNType = @convention(c) () -> CFTypeRef?
private typealias LSSetApplicationInformationItemType = @convention(c) (Int32, CFTypeRef, CFString, CFString, UnsafeMutablePointer<CFDictionary?>?) -> OSStatus

@objc
class ProcessNameUtil: NSObject {
  static func setProcessDisplayName(_ name: String) {
    guard !name.isEmpty else { return }
    guard Thread.isMainThread else { return }

    let cfName = name as CFString
    guard let lsBundle = CFBundleGetBundleWithIdentifier("com.apple.LaunchServices" as CFString) else { return }

    guard let getASNPtr = CFBundleGetFunctionPointerForName(lsBundle, "_LSGetCurrentApplicationASN" as CFString),
          let setInfoPtr = CFBundleGetFunctionPointerForName(lsBundle, "_LSSetApplicationInformationItem" as CFString),
          let displayNameKeyPtr = CFBundleGetDataPointerForName(lsBundle, "_kLSDisplayNameKey" as CFString) else { return }

    let getASN = unsafeBitCast(getASNPtr, to: LSGetCurrentApplicationASNType.self)
    let setInfo = unsafeBitCast(setInfoPtr, to: LSSetApplicationInformationItemType.self)
    let displayNameKey = displayNameKeyPtr.load(as: CFString.self)

    // Carbon Process Manager init (optional, skip for modern macOS)
    
    guard let asn = getASN() else { return }
    _ = setInfo(-2, asn, displayNameKey, cfName, nil)
  }
}
#endif

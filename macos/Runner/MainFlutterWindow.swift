import Cocoa
import FlutterMacOS

class MainFlutterWindow: NSPanel {
    private var methodChannel: FlutterMethodChannel?

    override func awakeFromNib() {
        setupWindow()
        setupMethodChannel()
        registerPlugins()
        // Start with hiding from screen sharing
        setHideFromScreenSharing(true)
        super.awakeFromNib()
    }

    private func setupWindow() {
        let flutterViewController = FlutterViewController()
        let windowFrame = self.frame
        self.contentViewController = flutterViewController
        self.setFrame(windowFrame, display: true)
        self.collectionBehavior = [.fullScreenPrimary, .canJoinAllSpaces, .fullScreenAuxiliary]
    }

    private func setupMethodChannel() {
        guard let flutterViewController = contentViewController as? FlutterViewController else {
            return
        }

        methodChannel = FlutterMethodChannel(
            name: "window_controls",
            binaryMessenger: flutterViewController.engine.binaryMessenger
        )

        methodChannel?.setMethodCallHandler { [weak self] (call, result) in
            self?.handleMethodCall(call, result: result)
        }
    }

    private func handleMethodCall(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "setHideFromScreenSharing":
            handleHideFromScreenSharing(call, result: result)
        case "detectZoom":
            handleDetectZoom(result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }

    private func handleHideFromScreenSharing(
        _ call: FlutterMethodCall, result: @escaping FlutterResult
    ) {
        guard
            let args = call.arguments as? [String: Any],
            let hideFromScreenSharing = args["hideFromScreenSharing"] as? Bool
        else {
            result(
                FlutterError(
                    code: "INVALID_ARGUMENTS",
                    message: "Invalid arguments",
                    details: nil
                ))
            return
        }

        setHideFromScreenSharing(hideFromScreenSharing)
        result(true)
    }

    private func setHideFromScreenSharing(_ hideFromScreenSharing: Bool) {
        DispatchQueue.main.async {
            self.sharingType = hideFromScreenSharing ? .none : .readOnly
            self.level = hideFromScreenSharing ? .screenSaver + 1 : .normal
        }
    }

    private func registerPlugins() {
        guard let flutterViewController = contentViewController as? FlutterViewController else {
            return
        }
        RegisterGeneratedPlugins(registry: flutterViewController)
    }

    // region: - Zoom Detection
    private func handleDetectZoom(result: @escaping FlutterResult) {
        DispatchQueue.global(qos: .userInitiated).async {
            let zoomInfo = self.checkZoomInstallation()
            
            // Return a dictionary with the zoom detection information
            let responseData: [String: Any?] = [
                "installed": zoomInfo.installed,
                "version": zoomInfo.version,
                "installMethod": zoomInfo.installMethod
            ]
            
            DispatchQueue.main.async {
                result(responseData)
            }
        }
    }
    
    private func checkZoomInstallation() -> (installed: Bool, version: String?, installMethod: String?) {
        // 1. Check standard locations
        let fileManager = FileManager.default
        let applicationPaths = [
            "/Applications/zoom.us.app",
            "\(NSHomeDirectory())/Applications/zoom.us.app" // User Applications folder
        ]
        
        for path in applicationPaths {
            if fileManager.fileExists(atPath: path) {
                let infoPlistPath = "\(path)/Contents/Info.plist"
                if let infoDictionary = NSDictionary(contentsOfFile: infoPlistPath),
                   let version = infoDictionary["CFBundleShortVersionString"] as? String {
                    return (true, version, "standard")
                }
                return (true, nil, "standard") // Installed but version couldn't be determined
            }
        }
        
        // 2. Check via NSWorkspace for non-standard locations
        let workspace = NSWorkspace.shared
        let zoomBundleIdentifier = "us.zoom.xos"
        
        if let zoomURL = workspace.urlForApplication(withBundleIdentifier: zoomBundleIdentifier),
           let bundle = Bundle(url: zoomURL),
           let version = bundle.infoDictionary?["CFBundleShortVersionString"] as? String {
            return (true, version, "non-standard location")
        }
        
        // 3. Check Homebrew installation
        let brewResult = checkBrewInstalledZoom()
        if brewResult.installed {
            return (true, brewResult.version, "homebrew")
        }
        
        return (false, nil, nil) // Not installed
    }
    
    private func checkBrewInstalledZoom() -> (installed: Bool, version: String?) {
        guard let brewPath = getBrewPath() else {
            return (false, nil)
        }
        
        let process = Process()
        process.executableURL = URL(fileURLWithPath: brewPath)
        process.arguments = ["list", "--versions", "zoom"]
        
        let pipe = Pipe()
        process.standardOutput = pipe
        
        do {
            try process.run()
            process.waitUntilExit()
            
            if process.terminationStatus == 0 {
                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                if let output = String(data: data, encoding: .utf8) {
                    // Output format typically: "zoom 5.14.10.14302"
                    let components = output.trimmingCharacters(in: .whitespacesAndNewlines).components(separatedBy: " ")
                    if components.count >= 2 {
                        return (true, components[1])
                    }
                    return (true, nil) // Installed but couldn't parse version
                }
            }
        } catch {
            print("Error checking brew: \(error)")
        }
        
        return (false, nil)
    }
    
    private func getBrewPath() -> String? {
        let possiblePaths = [
            "/usr/local/bin/brew",      // Intel Macs
            "/opt/homebrew/bin/brew"    // Apple Silicon Macs
        ]
        
        for path in possiblePaths {
            if FileManager.default.fileExists(atPath: path) {
                return path
            }
        }
        
        return nil
    }
    // endregion: - Zoom Detection
}
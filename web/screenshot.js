// Global variable to store the MediaStream (primarily for web flow)
window.captureStream = null;
// Hidden video element for processing the stream
let videoElement = null;

// Maximum resolution for screenshots
const MAX_RESOLUTION = 2048;

// Helper function to resize a canvas if needed and return ArrayBuffer + dimensions
async function _resizeCanvasAndGetData(sourceCanvas, originalWidth, originalHeight) {
    // Calculate scaling factor
    let scaleFactor = 1.0;
    if (originalWidth > MAX_RESOLUTION) {
        scaleFactor = MAX_RESOLUTION / originalWidth;
    }
    if (originalHeight * scaleFactor > MAX_RESOLUTION) {
        scaleFactor = MAX_RESOLUTION / originalHeight;
    }

    const newWidth = Math.round(originalWidth * scaleFactor);
    const newHeight = Math.round(originalHeight * scaleFactor);

    // If no resize needed, get buffer from original canvas
    if (scaleFactor === 1.0) {
         const blob = await new Promise(resolve => sourceCanvas.toBlob(resolve, 'image/png'));
         if (!blob) {
             throw new Error('Failed to create blob from original canvas');
         }
         const buffer = await blob.arrayBuffer();
         return { buffer: buffer, width: originalWidth, height: originalHeight };
    }

    // Create new canvas with resized dimensions
    const resizedCanvas = document.createElement('canvas');
    resizedCanvas.width = newWidth;
    resizedCanvas.height = newHeight;

    // Draw the original image on the resized canvas
    const ctx = resizedCanvas.getContext('2d');
    ctx.drawImage(sourceCanvas, 0, 0, originalWidth, originalHeight, 0, 0, newWidth, newHeight);

    // Get ArrayBuffer from resized canvas
    const blob = await new Promise(resolve => resizedCanvas.toBlob(resolve, 'image/png'));
    if (!blob) {
        throw new Error('Failed to create blob from resized canvas');
    }
    const buffer = await blob.arrayBuffer();

    return { buffer: buffer, width: newWidth, height: newHeight };
}


async function takeDirectElectronScreenshot() {
    let tempStream = null;
    let tempVideoElement = null;

    try {
        // 1. Get sources via IPC
        const sources = await window.electronAPI.getSources();
        if (!sources || sources.length === 0) {
            throw new Error("No screen sources found via Electron API.");
        }

        // 2. Select source (e.g., first screen)
        let selectedSource = sources.find(source => source.id.startsWith('screen:'));
        if (!selectedSource) {
            selectedSource = sources[0];
        }

        // 3. Define constraints
        const constraints = {
            audio: false,
            video: {
                mandatory: {
                    chromeMediaSource: 'desktop',
                    chromeMediaSourceId: selectedSource.id,
                }
            }
        };

        // 4. Get temporary stream
        tempStream = await navigator.mediaDevices.getUserMedia(constraints);

        // 5. Create temporary video element
        tempVideoElement = document.createElement('video');
        tempVideoElement.style.position = 'absolute';
        tempVideoElement.style.top = '-9999px';
        tempVideoElement.style.left = '-9999px';
        tempVideoElement.style.width = '1px';
        tempVideoElement.style.height = '1px';
        tempVideoElement.srcObject = tempStream;
        document.body.appendChild(tempVideoElement);

        // 6. Wait for video metadata and play
        await new Promise((resolve, reject) => {
            tempVideoElement.onloadedmetadata = resolve;
            tempVideoElement.onerror = reject;
            tempVideoElement.play().catch(reject);
        });

        // 7. Capture frame to canvas
        const canvas = document.createElement('canvas');
        canvas.width = tempVideoElement.videoWidth;
        canvas.height = tempVideoElement.videoHeight;
        if (canvas.width === 0 || canvas.height === 0) {
            throw new Error('Temporary video dimensions are zero.');
        }
        const ctx = canvas.getContext('2d');
        ctx.drawImage(tempVideoElement, 0, 0, canvas.width, canvas.height);

        // 8. Resize canvas if needed and get data
        const result = await _resizeCanvasAndGetData(canvas, canvas.width, canvas.height);

        // 11. Clean up immediately
        tempStream.getTracks().forEach(track => track.stop());
        if (tempVideoElement.parentNode) {
            tempVideoElement.parentNode.removeChild(tempVideoElement);
        }

        // 9. Return ArrayBuffer and dimensions
        return result;

    } catch (error) {
        console.error('Error during direct Electron screenshot:', error);
        // Ensure cleanup even on error
        if (tempStream) {
            tempStream.getTracks().forEach(track => track.stop());
        }
        if (tempVideoElement && tempVideoElement.parentNode) {
            tempVideoElement.parentNode.removeChild(tempVideoElement);
        }
        throw error; // Propagate error
    }
}

// Function to initialize and start screen capture (NOW ONLY FOR WEB FLOW)
async function startScreenCapture() {
    if (window.captureStream) {
        return true; // Indicate already active
    }
    
    // Clean up previous elements if any
    stopScreenCapture();

    try {
        // --- Standard Web Path (getDisplayMedia) ---
        window.captureStream = await navigator.mediaDevices.getDisplayMedia({
            video: { cursor: "always" },
            audio: false
        });
        // --- End Standard Web Path ---

        // --- Common Stream Handling ---
        videoElement = document.createElement('video');
        videoElement.style.position = 'absolute';
        videoElement.style.top = '-9999px';
        videoElement.style.left = '-9999px';
        videoElement.style.width = '1px';
        videoElement.style.height = '1px';
        videoElement.srcObject = window.captureStream;
        document.body.appendChild(videoElement);

        await new Promise((resolve, reject) => {
            videoElement.onloadedmetadata = resolve;
            videoElement.onerror = reject;
            videoElement.play().catch(reject);
        });

        window.captureStream.getVideoTracks()[0].addEventListener('ended', () => {
            stopScreenCapture();
        });

        return true; // Indicate success
        // --- End Common Stream Handling ---

    } catch (error) {
        console.error('Error starting web screen capture:', error);
        stopScreenCapture(); // Ensure cleanup on error
        throw error; // Propagate the error to Dart
    }
}

// Function to take a screenshot from the active stream (USED BY WEB FLOW)
async function takeWebScreenshot() {
    if (!window.captureStream || !videoElement || videoElement.readyState < videoElement.HAVE_METADATA) {
        console.error('Web screen capture not active or video not ready.');
        throw new Error('Web screen capture not active or video not ready.');
    }

    try {
        const canvas = document.createElement('canvas');
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        if (canvas.width === 0 || canvas.height === 0) {
             throw new Error('Web video dimensions are zero, cannot take screenshot.');
        }
        const ctx = canvas.getContext('2d');
        ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

        // Resize canvas if needed and get data
        const result = await _resizeCanvasAndGetData(canvas, canvas.width, canvas.height);

        // Return ArrayBuffer and dimensions
        return result;

    } catch (error) {
        console.error('Error taking web screenshot:', error);
        throw error;
    }
}

// Function to stop the screen capture (USED BY WEB FLOW)
function stopScreenCapture() {
    let stopped = false;
    if (window.captureStream) {
        window.captureStream.getTracks().forEach(track => track.stop());
        window.captureStream = null;
        stopped = true;
    }
    if (videoElement) {
        videoElement.srcObject = null;
        if (videoElement.parentNode) {
            videoElement.parentNode.removeChild(videoElement);
        }
        videoElement = null;
        stopped = true;
    }
    return true; // Indicate cleanup attempt finished
}

// Function to check if screen capture is currently active (USED BY WEB FLOW)
function isScreenCaptureActive() {
    return !!window.captureStream && window.captureStream.active && window.captureStream.getVideoTracks()[0]?.readyState === 'live';
}
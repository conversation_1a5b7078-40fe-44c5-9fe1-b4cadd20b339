.container {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex: auto;
  flex-direction: column;
}
.indicator {
  width: 100px;
  height: 100px;
  margin-bottom: 15px;
}
.progress-bar {
  width: 100px;
  background-color: #f3f3f3;
  border-radius: 4px;
}
@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
.progress-bar-fill {
  display: block;
  height: 5px;
  background-color: #46494b;
  border-radius: 4px;
  transition: width 0.2s ease-in;
  animation: progress 15s linear forwards;
}
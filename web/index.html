<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Nail your job interview">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="InterviewHammer">
  <link rel="apple-touch-icon" href="icons/icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.ico"/>

  <!-- Facebook Meta Tags -->
  <meta property="og:url" content="https://interviewhammer.com">
  <meta property="og:type" content="website">
  <meta property="og:title" content="InterviewHammer">
  <meta property="og:description" content="Nail your job interview">
  <meta property="og:image" content="https://interviewhammer.com/social-card.png">

  <!-- Twitter Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta property="twitter:domain" content="interviewhammer.com">
  <meta property="twitter:url" content="https://interviewhammer.com">
  <meta name="twitter:title" content="InterviewHammer">
  <meta name="twitter:description" content="Nail your job interview">
  <meta name="twitter:image" content="https://interviewhammer.com/social-card.png">

  <title>InterviewHammer</title>
  <link rel="manifest" href="manifest.json">

  <link rel="stylesheet" href="styles.css">

  <!--Smartlook-->
  <script type='text/javascript'>
  window.smartlook||(function(d) {
    var o=smartlook=function(){ o.api.push(arguments)},h=d.getElementsByTagName('head')[0];
    var c=d.createElement('script');o.api=new Array();c.async=true;c.type='text/javascript';
    c.charset='utf-8';c.src='https://sl.meetingaitools.com/recorder.js';h.appendChild(c);
    })(document);
    smartlook('init', '393451d814f5c54819aa75b6a2d6aa70f96023be', { region: 'eu', host: 'https://sl.meetingaitools.com' });
  </script>

  <!--loading the sdk from the cdn sometimes gets blocked by ad blockers-->
  <!--  <script src="https://csspeechstorage.blob.core.windows.net/drop/1.40.0/RawJavaScriptSDK/microsoft.cognitiveservices.speech.sdk.bundle.js"></script>-->
  <!--const SPEECHSDK_CLIENTSDK_VERSION = "1.40.0";-->
  <script src="scripts/microsoft.cognitiveservices.speech.sdk.bundle.js"></script>
  <script src="scripts/enable-threads.js"></script>
</head>

<style>
</style>

<body data-sl="canvas-mq">
  <div id="loading_indicator" class="container">
    <img class="indicator" src="icons/icon-512.png"/>
    <div class="progress-bar">
      <div class="progress-bar-fill" style="width: 50%;"></div>
    </div>
  </div>

  <script>
    {{flutter_bootstrap_js}}
  </script>

  <script src="scripts/version_check.js"></script>
  <script src="scripts/silero_vad_init.js"></script>

  <!-- Script to remove Splash Screen after 15 seconds-->
  <script>
    setTimeout(function() {
      var loadingIndicator = document.getElementById('loading_indicator');
      if (loadingIndicator) loadingIndicator.remove();
    }, 15000);
  </script>
  <script src="screenshot.js" defer></script>
</body>
</html>
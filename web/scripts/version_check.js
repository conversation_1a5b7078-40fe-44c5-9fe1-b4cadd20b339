// This script tag was edited manually to force service worker cache reload.
// Thanks to https://github.com/flutter/flutter/issues/63500
function checkVersion() {
  if ('serviceWorker' in navigator) {
    var seconds = new Date().getTime();
    var xmlhttp = new XMLHttpRequest();
    xmlhttp.open("GET", '/version.json?v=' + seconds, true);
    xmlhttp.onload = function() {
      if (xmlhttp.status == 200) {
        var buildNumber = xmlhttp.responseText;
        console.log('remote version is ' + buildNumber);
        var currentBuildNumber = window.localStorage.getItem('buildNumber');

        console.log('local version is ' + currentBuildNumber);
        if (currentBuildNumber != buildNumber) {
          console.log('App update is necessary. Clearing service workers cache');
          caches.delete('flutter-app-manifest');
          caches.delete('flutter-temp-cache');
          caches.delete('flutter-app-cache');

          window.localStorage.setItem('buildNumber', buildNumber);
        } else {
          console.log('App is up to date');
        }
      }
    };

    xmlhttp.onerror = xmlhttp.onabort = xmlhttp.ontimeout = function() {
      console.log('Error checking version, proceeding with app load');
    };

    xmlhttp.send();
  } else {
    console.log('Service worker not found. Continue app loading.');
  }
}

checkVersion();

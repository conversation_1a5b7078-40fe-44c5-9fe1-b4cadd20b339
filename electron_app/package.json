{"name": "electron_app", "version": "3.1.3", "main": "electron.js", "scripts": {"fix-smartlook-win": "pushd .. && dart run fix_smartlook_windows.dart && popd", "apply-patch": "pushd .. && dart run patch_package apply && popd", "build:flutter": "npm run apply-patch && pushd .. && flutter build web --base-href / && popd", "build:flutter:debug": "npm run apply-patch && pushd .. && flutter build web --debug --base-href / && popd", "build:flutter:win": "npm run fix-smartlook-win && pushd .. && flutter build web --base-href / && popd", "build:flutter:win:debug": "npm run fix-smartlook-win && pushd .. && flutter build web --debug --base-href / && popd", "dev": "npm run build:flutter:debug && npm run start", "start": "cross-env NODE_ENV=development ELECTRON_ENABLE_LOGGING=true ELECTRON_DEBUG_LOGGING=true node_modules/.bin/electron . --enable-logging", "build_and_start": "npm run build:flutter && npm run start", "sync-version": "node sync-version.js", "package": "npm run sync-version && npm run build:flutter && electron-builder --dir", "package:mac": "npm run sync-version && npm run build:flutter && electron-builder --mac", "package:win": "npm run sync-version && npm run build:flutter:win && electron-builder --win --x64", "package:mac:intel": "npm run sync-version && npm run build:flutter && rm -rf node_modules package-lock.json && npm_config_arch=x64 npm install && npm install --ignore-scripts --no-save --force --platform=darwin --arch=x64 @yuuang/ffi-rs-darwin-x64 && electron-builder --mac --x64", "postinstall": "if not exist \"node_modules\\@yuuang\\ffi-rs-android-arm64\" mkdir \"node_modules\\@yuuang\\ffi-rs-android-arm64\""}, "author": "", "license": "ISC", "description": "", "devDependencies": {"cross-env": "^7.0.3", "electron": "^35.1.4", "electron-builder": "^26.0.12", "npm-run-all": "^4.1.5"}, "dependencies": {"express": "^5.1.0", "ffi-rs": "^1.2.12"}, "build": {"appId": "com.interviewhammer.electron", "productName": "<PERSON><PERSON><PERSON><PERSON>", "files": ["electron.js", "package.json", "preload.js"], "extraResources": [{"from": "../build/web", "to": "web", "filter": ["**/*"]}, {"from": "./assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": "nsis", "icon": "../web/icons/Icon-512.png"}, "mac": {"target": "dmg", "icon": "../web/icons/Icon-512.png"}, "nsis": {"installerIcon": "../web/favicon.ico", "uninstallerIcon": "../web/favicon.ico"}}}
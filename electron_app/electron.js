const { app, BrowserWindow, systemPreferences, ipcMain, desktopCapturer, screen, Tray, Menu } = require('electron');
const path = require('path');
const express = require('express');
const { load, open, close, DataType } = require('ffi-rs');

let mainWindow;
const PORT = 8096; // Port for the local server
let user32LibLoaded = false; // Track if user32.dll was loaded
let tray = null; // System tray instance

const appName = app.getName();

// Windows API Constants for SetWindowDisplayAffinity
const WDA_NONE = 0x00000000;
const WDA_EXCLUDEFROMCAPTURE = 0x00000011; // Excludes window from capture/sharing

// --- Local Web Server Setup ---
const expressApp = express();
let server; // Keep a reference to the server to close it later

// Define the directory containing the Flutter web build
// Adjust path based on whether the app is packaged or running in development
const buildPath = app.isPackaged
  ? path.join(process.resourcesPath, 'web')
  : path.join(__dirname, '../build/web');

// Serve static files from the build directory
expressApp.use(express.static(buildPath));

// Function to start the server
function startServer() {
  return new Promise((resolve, reject) => {
    server = expressApp.listen(PORT, () => {
      console.log(`Local server listening on http://localhost:${PORT}`);
      resolve();
    }).on('error', (err) => {
      console.error('Failed to start local server:', err);
      reject(err);
    });
  });
}
// --- End Server Setup ---

// Single instance lock - prevent multiple instances of the app
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  // Another instance is already running, quit this one
  app.quit();
} else {
  // Handle second instance attempt
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    // Someone tried to run a second instance, we should focus our window instead
    if (mainWindow) {
      if (mainWindow.isMinimized()) {
        mainWindow.restore();
      }
      if (!mainWindow.isVisible()) {
        mainWindow.show();
      }
      mainWindow.focus();
      applyContentProtectionIfNeeded();
    }
  });
}

function createWindow() {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { height: screenHeight } = primaryDisplay.workAreaSize; // Use workAreaSize to exclude taskbar/menubar

  // Calculate window dimensions based on Flutter settings
  const windowWidth = 500;
  const windowHeight = Math.floor(screenHeight * 0.8); // 80% of screen height, ensure integer

  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    webPreferences: {
      nodeIntegration: false, // Keep false for security
      contextIsolation: true, // Keep true for security
      preload: path.join(__dirname, 'preload.js') // Define path for preload script
    },
    autoHideMenuBar: true,
  });
  
   // Load the URL from the local server
  mainWindow.loadURL(`http://localhost:${PORT}`);

  // Open the DevTools (optional, useful for debugging)

  // Emitted when the window is closed.
  mainWindow.on('closed', function () {
    mainWindow = null;
  });

  // Listen for window minimize events
  mainWindow.on('minimize', function () {
    // Ensure skip taskbar is set (hide from taskbar)
    if (process.platform === 'darwin') {
      app.dock.hide();
    } else {
      mainWindow.setSkipTaskbar(true);
    }
  });
  
  // Listen for window restore event (to re-apply protection)
  mainWindow.on('restore', applyContentProtectionIfNeeded);
  mainWindow.on('show', applyContentProtectionIfNeeded);
}

// Function to request macOS media permissions (Screen & Microphone)
async function requestMediaPermissions() {
  if (process.platform !== 'darwin') {
    return true; // Not needed on non-macOS platforms
  }

  let screenAccessGranted = false;
  let microphoneAccessGranted = false; // Keep microphone check in case future features need it

  try {
    // Check/Request Screen Recording Permission
    const screenStatus = systemPreferences.getMediaAccessStatus('screen');
    console.log(`macOS Screen Recording Status: ${screenStatus}`);
    if (screenStatus === 'granted') {
      screenAccessGranted = true;
    } else if (screenStatus !== 'restricted' && screenStatus !== 'denied') {
      // Only ask if not already explicitly denied or restricted by policy
      screenAccessGranted = await systemPreferences.askForMediaAccess('screen');
      console.log(`macOS Screen Recording Permission Request Result: ${screenAccessGranted}`);
    } else {
       console.log('Screen recording permission is restricted or denied. Cannot ask.');
    }

    // For this app, only screen access is strictly required.
    // Modify if microphone becomes necessary.
    return screenAccessGranted;

  } catch (error) {
    console.error('Could not request media permissions:', error);
    return false;
  }
}

// --- IPC Handler for Desktop Capturer ---
ipcMain.handle('get-sources', async () => {
  console.log('Main process: Received get-sources request.');
  try {
    const sources = await desktopCapturer.getSources({ types: ['screen', 'window'] });
    console.log(`Main process: Found ${sources.length} sources.`);
    return sources.map(source => ({ // Send only necessary info
        id: source.id,
        name: source.name,
        thumbnail: source.thumbnail.toDataURL() // Convert thumbnail to data URL
    }));
  } catch (error) {
      console.error('Main process: Error getting sources:', error);
      return []; // Return empty array on error
  }
});

// Track session state for system tray menu
let hasActiveSession = false;

// Listen for session state updates
ipcMain.on('update-session-state', (_, isActive) => {
  hasActiveSession = isActive;
  // Update tray menu if tray exists
  if (tray) {
    updateTrayMenu();
  }
});

// --- IPC Handlers for Window Settings ---
ipcMain.on('set-opacity', (event, value) => {
  if (mainWindow && typeof value === 'number') {
    mainWindow.setOpacity(value);
  }
});

ipcMain.on('set-always-on-top', (event, value) => {
  if (mainWindow && typeof value === 'boolean') {
    mainWindow.setAlwaysOnTop(value);
  }
});

ipcMain.on('set-skip-taskbar', (event, value) => {
  if (typeof value === 'boolean') {
    if (process.platform === 'darwin') {
      // Use app.dock API for macOS
      if (value) { // value is true means "skip taskbar" -> hide dock icon
        app.dock.hide();
      } else { // value is false means "don't skip taskbar" -> show dock icon
        app.dock.show();
      }
    } else if (mainWindow) {
      // Use setSkipTaskbar for other platforms (Windows, Linux)
      mainWindow.setSkipTaskbar(value);
    }
  }
});

ipcMain.on('set-visible-on-all-workspaces', (event, value) => {
  // Note: setVisibleOnAllWorkspaces is primarily a macOS feature
  if (mainWindow && typeof value === 'boolean' && process.platform === 'darwin') {
     mainWindow.setVisibleOnAllWorkspaces(value);
  }
});

ipcMain.on('set-content-protection', (event, value) => {
  if (mainWindow && typeof value === 'boolean') {
    if (process.platform === 'win32') {
      setWindowsDisplayAffinity(value); // Use Windows-specific method
    } else if (process.platform === 'darwin') {
      mainWindow.setContentProtection(value); // Use Electron's built-in method for macOS
    } else {
       // Optionally log for unsupported platforms if protection is enabled
       if (value) {
         console.warn(`Platform ${process.platform} does not support content protection via this method.`);
       }
    }
  }
});

function setWindowsDisplayAffinity(exclude) {
  if (!user32LibLoaded || !mainWindow) {
    console.error('Cannot set display affinity: user32 library not loaded or window not available.');
    return;
  }
  try {
    const handleBuffer = mainWindow.getNativeWindowHandle();
    // HWND is a pointer, treat it as a 64-bit integer (BigInt) on Windows x64
    const handleBigInt = handleBuffer.readBigUInt64LE();
    const affinity = exclude ? WDA_EXCLUDEFROMCAPTURE : WDA_NONE;

    const successCode = load({
      library: 'user32',
      funcName: 'SetWindowDisplayAffinity',
      retType: DataType.I32, // Returns BOOL (int), non-zero for success
      paramsType: [DataType.BigInt, DataType.I32], // Pass HWND as BigInt, Affinity as I32
      paramsValue: [handleBigInt, affinity]
    });

    if (successCode === 0) { // 0 indicates failure for SetWindowDisplayAffinity
      console.error(`Windows: SetWindowDisplayAffinity(${exclude ? 'Exclude' : 'None'}) failed. Return code: ${successCode}`);
    }
    // No success log needed unless debugging
  } catch (err) {
    console.error('Windows: Failed to call SetWindowDisplayAffinity via ffi-rs:', err);
  }
}

// Public helper to (re)apply content protection for the current platform
function applyContentProtectionIfNeeded() {
  if (!mainWindow) return;
  if (process.platform === 'win32') {
    setWindowsDisplayAffinity(true);
  } else if (process.platform === 'darwin') {
    mainWindow.setContentProtection(true);
  }
}

// --- System Tray Handlers ---
// Create system tray
ipcMain.on('init-system-tray', () => {
  if (tray) return; // Already initialized
  
  try {
    // Set icon path based on whether app is packaged or not
    const iconPath = app.isPackaged
      ? path.join(process.resourcesPath, 'assets', 'icons', 'tray_icon.png')
      : path.join(__dirname, 'assets', 'icons', 'tray_icon.png');
    
    tray = new Tray(iconPath);
    tray.setToolTip(appName);
    updateTrayMenu();

    tray.on('click', () => {
      tray.popUpContextMenu();
    });
    
    tray.on('right-click', () => {
      tray.popUpContextMenu();
    });
  } catch (error) {
    console.error('Failed to initialize system tray:', error);
  }
});

// Function to update tray menu based on session state
function updateTrayMenu() {
  if (!tray) return;
  
  const menuItems = [
    { 
      label: 'Show App', 
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.focus();
          applyContentProtectionIfNeeded();
        }
      }
    },
    { type: 'separator' },
    { 
      label: 'Hide Tray Icon',
      click: () => {
        if (tray) {
          tray.destroy();
          tray = null;
        }

        // Notify renderer (Flutter) that tray icon was hidden via menu
        if (mainWindow && mainWindow.webContents) {
          mainWindow.webContents.send('hide-tray-icon-from-menu');
        }
      }
    }
  ];
  
  // Only add "End Session" if there's an active session
  if (hasActiveSession) {
    menuItems.push({ type: 'separator' });
    menuItems.push({ 
      label: 'End Session',
      click: () => {
        if (mainWindow) {
          // Send message to renderer to end session
          mainWindow.webContents.send('end-session-from-tray');
        }
      }
    });
  }
  
  menuItems.push({ type: 'separator' });
  menuItems.push({
    label: 'Exit',
    click: () => {
      app.quit();
    }
  });
  
  const contextMenu = Menu.buildFromTemplate(menuItems);
  tray.setContextMenu(contextMenu);
}

// Hide window to system tray
ipcMain.on('hide-window', () => {
  if (mainWindow) {
    // Ensure skip taskbar is set (hide from taskbar)
    if (process.platform === 'darwin') {
      app.dock.hide();
    } else {
      mainWindow.setSkipTaskbar(true);
    }
    
    // Hide the window
    mainWindow.hide();
  }
});

// Show window from system tray
ipcMain.on('show-window', () => {
  if (mainWindow) {
    // Restore and focus the window
    mainWindow.show();
    mainWindow.focus();
    applyContentProtectionIfNeeded();
  }
});

// Hide tray icon completely
ipcMain.on('hide-tray-icon', () => {
  if (tray) {
    tray.destroy();
    tray = null;
  }
});

// Show tray icon
ipcMain.on('show-tray-icon', () => {
  if (!tray) {
    ipcMain.emit('init-system-tray');
  }
});
// --- End System Tray Handlers ---


// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
app.whenReady().then(async () => {
  // Load user32.dll on Windows for SetWindowDisplayAffinity before creating the window
  if (process.platform === 'win32') {
    try {
      open({ library: 'user32', path: 'user32.dll' });
      user32LibLoaded = true;
    } catch (err) {
      console.error('Windows: Failed to load user32.dll for content protection:', err);
      user32LibLoaded = false; // Ensure it's false if loading failed
    }
  }

  // Request permissions before creating the window or starting the server
  const permissionsGranted = await requestMediaPermissions();

  if (!permissionsGranted && process.platform === 'darwin') {
    // Handle the case where screen permission is denied on macOS
    console.warn('Screen Recording permission was denied on macOS. Screenshot functionality will not work.');
  }

  try {
    await startServer(); // Start the server
    createWindow(); // Then create the window
    
    // Set initial content protection state after window creation
    if (mainWindow) {
      applyContentProtectionIfNeeded();
    }

  } catch (error) {
    console.error("Could not start server or create/setup window.", error);
    app.quit(); // Quit if server/window creation fails
  }

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS, or when system tray is active.
app.on('window-all-closed', function () {
  // If system tray is active, don't quit on window close
  if (tray) {
    return;
  }
  
  if (process.platform !== 'darwin') {
    if (server) {
      server.close(() => {
        console.log('Local server stopped.');
        app.quit();
      });
    } else {
      app.quit();
    }
  }
});

// Ensure server is closed on explicit quit (Cmd+Q on macOS)
app.on('will-quit', () => {
  // Clean up system tray
  if (tray) {
    tray.destroy();
    tray = null;
  }

  // Close user32 library if it was loaded
  if (user32LibLoaded) {
    try {
      close('user32');
    } catch (err) {
      console.error('Windows: Failed to close user32.dll:', err);
    }
    user32LibLoaded = false;
  }

  // Close the local server
  if (server) {
    server.close(() => {
      console.log('Local server stopped due to app quit.');
    });
  }
});

const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose specific IPC functions to the renderer process (<PERSON>lutter app)
// under the 'electronAPI' key on the window object.
contextBridge.exposeInMainWorld('electronAPI', {
  // Function to invoke the 'get-sources' handler in the main process
  getSources: () => {
    return ipcRenderer.invoke('get-sources');
  },
  // Functions to send commands to the main process for window settings
  setOpacity: (value) => {
    if (typeof value === 'number') {
      ipcRenderer.send('set-opacity', value);
    }
  },
  setAlwaysOnTop: (value) => {
    if (typeof value === 'boolean') {
      ipcRenderer.send('set-always-on-top', value);
    }
  },
  setSkipTaskbar: (value) => {
    if (typeof value === 'boolean') {
      ipcRenderer.send('set-skip-taskbar', value);
    }
  },
  setVisibleOnAllWorkspaces: (value) => {
    if (typeof value === 'boolean') {
      ipcRenderer.send('set-visible-on-all-workspaces', value);
    }
  },
  setContentProtection: (value) => {
    if (typeof value === 'boolean') {
      ipcRenderer.send('set-content-protection', value);
    }
  },
  hideWindow: () => {
    ipcRenderer.send('hide-window');
  },
  showWindow: () => {
    ipcRenderer.send('show-window');
  },
  initSystemTray: () => {
    ipcRenderer.send('init-system-tray');
  },
  updateSessionState: (isActive) => {
    if (typeof isActive === 'boolean') {
      ipcRenderer.send('update-session-state', isActive);
    }
  },
  hideTrayIcon: () => {
    ipcRenderer.send('hide-tray-icon');
  },
  showTrayIcon: () => {
    ipcRenderer.send('show-tray-icon');
  },
  // Listen for system tray events
  onEndSessionFromTray: (callback) => {
    ipcRenderer.on('end-session-from-tray', (_event) => {
      callback();
    });
  },
  onHideTrayIcon: (callback) => {
    ipcRenderer.on('hide-tray-icon-from-menu', (_event) => {
      callback();
    });
  }
});
console.log('Preload script: electronAPI exposed.');

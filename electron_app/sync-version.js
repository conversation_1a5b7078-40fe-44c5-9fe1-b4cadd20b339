const fs = require('fs');
const path = require('path');

// Path to the pubspec.yaml and package.json files
const pubspecPath = path.join(__dirname, '..', 'pubspec.yaml');
const packageJsonPath = path.join(__dirname, 'package.json');

// Read the pubspec.yaml file
const pubspecContent = fs.readFileSync(pubspecPath, 'utf8');

// Extract the version from pubspec.yaml using regex
const versionMatch = pubspecContent.match(/version:\s*([0-9]+\.[0-9]+\.[0-9]+)\+([0-9]+)/);

if (!versionMatch) {
  console.error('Failed to extract version from pubspec.yaml');
  process.exit(1);
}

// Get the version without the build number
const flutterVersion = versionMatch[1];

// Read the package.json file
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Update the version in package.json
packageJson.version = flutterVersion;

// Write the updated package.json back to file
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8');

console.log(`Version synchronized: ${flutterVersion}`);
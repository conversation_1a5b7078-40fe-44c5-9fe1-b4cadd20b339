# Electron Wrapper for InterviewHammer

Run the InterviewHammer Flutter app as a desktop application using Electron.

## Setup

1.  Install [Node.js](https://nodejs.org/) and the [Flutter SDK](https://flutter.dev/docs/get-started/install).
2.  Run `flutter pub get` in the project root (`../`).
3.  Run `npm install` in this directory (`electron_app/`).

## Run Locally

```bash
# In electron_app/ directory
npm start
pushd .. && flutter clean && popd && npm run build_and_start
```
This starts the Electron app and loads the Flutter web build.

## Package for Distribution

Run these commands from the `electron_app/` directory. Output goes to `electron_app/dist/`.

*   **macOS:** (Requires macOS)
    ```bash
      # universal (arm64 + x64)
      pushd .. && flutter clean && popd && npm run package:mac
      # Intel-only build (smaller download)
      pushd .. && flutter clean && popd && npm run package:mac:intel
    ```
*   **Windows (64-bit):** (Requires Windows for installers, recommended)
    ```bash
    # In electron_app/ directory
    pushd .. && flutter clean && popd && npm run package:win
    ```
    The output will be a file like: `dist/InterviewHammer Setup 2.1.6.exe` with the current Flutter version.

*   **Unpacked (Current OS):**
    ```bash
    npm run package
    ```
## Run/Package in Debug Mode
```bash
   cd electron_app
   npm run dev
```

# How to Open Developer Options in InterviewHammer
* On macOS:
    -Press Command (⌘) + Option (⌥) + I
* On Windows/Linux:
    - Press Ctrl + Shift + I

## Version Synchronization

The Electron app automatically synchronizes its version with the Flutter app:

1. The version is read from `../pubspec.yaml` during packaging
2. The Electron app package.json version is updated automatically
3. This ensures the output file name matches the Flutter app version

*(Optional)* Customize packaging (icons, signing, etc.) via the `build` section in `package.json`. See [electron-builder docs](https://www.electron.build/).


## Troubleshooting

### Clearing Application Data/Cache (macOS)

If you encounter persistent issues, you might need to clear the application's local data. **Warning:** This removes all local settings and data.

1.  Close the InterviewHammer application.
2.  Run the following command in your terminal:
    ```bash
    rm -rf "$HOME/Library/Application Support/electron_app"
    ```
3.  Restart the application.


import 'package:flutter/foundation.dart';

import 'model/management/personal_info.dart';

const addDebugData = kDebugMode && false;

const d_interviewTopic = 'Android Development';
final d_personalInfoData = PersonalInfo(
  name: 'Akil',
  currentRole: 'Android Developer',
  yearsOfExperience: "5",
  workHistory: """
- HolaFin, a fintech company in India
- Freelancer for 2 years
- Worked with a startup in the US
  """,
  company: 'HolaFin',
  education: 'B.Tech in Computer Science',
  additionalInfo: 'I am a hardworking and dedicated developer',
  skills: ['Java', 'Kotlin', 'Android Studio', 'Firebase'],
);
const d_extraInstructions = """
- Craft your responses in an easy-to-understand way, suitable for reading out loud.
- Respond in paragraphs. in an active turn when possible.
- Please provide answers in a clear and simple manner, making sure they are easy to read aloud. Add natural sounds like 'hmm' and 'uh' to make the responses sound more conversational
- Make sure to give detailed and long answers if needed.
""";

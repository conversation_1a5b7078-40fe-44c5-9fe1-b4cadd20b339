// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBiXpQ_YiGFxy0ad1kxKuDXhD3XeVpKcMI',
    appId: '1:342275778166:web:54d32f0b40ff99137da157',
    messagingSenderId: '342275778166',
    projectId: 'interviewhammer1',
    authDomain: 'interviewhammer1.firebaseapp.com',
    databaseURL: 'https://interviewhammer1-default-rtdb.firebaseio.com',
    storageBucket: 'interviewhammer1.appspot.com',
    measurementId: 'G-HV893PLPCB',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDpwuQU0y0coA6PRo5LdpRwvrxFs2H2jPg',
    appId: '1:342275778166:android:d4cf8e3651d028217da157',
    messagingSenderId: '342275778166',
    projectId: 'interviewhammer1',
    databaseURL: 'https://interviewhammer1-default-rtdb.firebaseio.com',
    storageBucket: 'interviewhammer1.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD2UfWuc-MPKit0Iys9WOhCF8bc4ooAYmE',
    appId: '1:342275778166:ios:392762741ad01c977da157',
    messagingSenderId: '342275778166',
    projectId: 'interviewhammer1',
    databaseURL: 'https://interviewhammer1-default-rtdb.firebaseio.com',
    storageBucket: 'interviewhammer1.appspot.com',
    iosBundleId: 'com.interview-ai-assistant.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyD2UfWuc-MPKit0Iys9WOhCF8bc4ooAYmE',
    appId: '1:342275778166:ios:82509e97d7bdde477da157',
    messagingSenderId: '342275778166',
    projectId: 'interviewhammer1',
    databaseURL: 'https://interviewhammer1-default-rtdb.firebaseio.com',
    storageBucket: 'interviewhammer1.appspot.com',
    iosBundleId: 'com.interviewhammer.mac',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBiXpQ_YiGFxy0ad1kxKuDXhD3XeVpKcMI',
    appId: '1:342275778166:web:cfeebe155b5989247da157',
    messagingSenderId: '342275778166',
    projectId: 'interviewhammer1',
    authDomain: 'interviewhammer1.firebaseapp.com',
    databaseURL: 'https://interviewhammer1-default-rtdb.firebaseio.com',
    storageBucket: 'interviewhammer1.appspot.com',
    measurementId: 'G-5DCB70ZF9C',
  );
}

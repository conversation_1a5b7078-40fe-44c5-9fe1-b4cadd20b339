import 'package:flutter/material.dart';
import 'package:intl/intl.dart' hide TextDirection;

void main() {
  runApp(
    MaterialApp(
      home: InterviewSettings(),
    ),
  );
}

class InterviewSettings extends StatefulWidget {
  const InterviewSettings({super.key});

  @override
  State<InterviewSettings> createState() => _InterviewSettingsState();
}

class _InterviewSettingsState extends State<InterviewSettings> {
  @override
  Widget build(BuildContext context) {
    String text = "استخدام Fragments يساعد في تحسين تجربة المستخدم";
    print(isRTL(text)); // false

    return Scaffold(
      appBar: AppBar(
        title: Text('Interview Settings'),
      ),
      body: Column(
        children: [
          Text(
            text,
            textDirection: Bidi.detectRtlDirectionality(text)
                ? TextDirection.rtl
                : TextDirection.ltr,
            style: TextStyle(fontSize: 18),
          )
        ],
      ),
    );
  }

  bool isRTL(String text) {
    return Bidi.detectRtlDirectionality(text);
  }
}

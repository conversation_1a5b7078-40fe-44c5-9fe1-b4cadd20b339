import 'package:cloud_firestore/cloud_firestore.dart';

class ScreenshotReference {
  final String screenshotUrl;
  final Timestamp timestamp;

  ScreenshotReference({
    required this.screenshotUrl,
    required this.timestamp,
  });

  factory ScreenshotReference.fromMap(Map<String, dynamic>? data) {
    if (data == null) {
      return ScreenshotReference(
        screenshotUrl: '',
        timestamp: Timestamp.now(),
      );
    }

    return ScreenshotReference(
      screenshotUrl: data['screenshotUrl'] ?? '',
      timestamp: data['timestamp'] ?? Timestamp.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'screenshotUrl': screenshotUrl,
      'timestamp': timestamp,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ScreenshotReference &&
        other.screenshotUrl == screenshotUrl &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode => screenshotUrl.hashCode ^ timestamp.hashCode;
}

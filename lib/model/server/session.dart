import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:interview_hammer/model/server/screenshot_reference.dart';

enum SessionStatus { WAITING, ACTIVE, ENDED }

enum CommandType { TAKE_SCREENSHOT, HIDE_APPLICATION, IDLE }

enum CommandStatus { PENDING, COMPLETED, FAILED }

class Session {
  final String sessionId;
  final String deviceName;
  final String userId;
  final Timestamp sessionStartTime;
  final Timestamp? sessionEndTime;
  final SessionStatus status;
  final CommandType currentCommand;
  final Timestamp? commandTimestamp;
  final CommandStatus commandStatus;
  final List<ScreenshotReference> screenshots;

  Session({
    required this.sessionId,
    required this.deviceName,
    required this.userId,
    required this.sessionStartTime,
    this.sessionEndTime,
    required this.status,
    required this.currentCommand,
    this.commandTimestamp,
    required this.commandStatus,
    required this.screenshots,
  });

  factory Session.fromFirestore(
      DocumentSnapshot<Map<String, dynamic>> snapshot) {
    final data = snapshot.data();
    final screenshots = (data?['screenshots'] as List<dynamic>? ?? [])
        .map((screenshotData) => ScreenshotReference.fromMap(screenshotData))
        .toList();

    return Session(
      sessionId: snapshot.id,
      deviceName: data?['deviceName'] ?? '',
      userId: data?['userId'] ?? '',
      sessionStartTime: data?['sessionStartTime'] ?? Timestamp.now(),
      sessionEndTime: data?['sessionEndTime'],
      status: _parseSessionStatus(data?['status'] ?? 'WAITING'),
      currentCommand: _parseCommandType(data?['currentCommand'] ?? 'IDLE'),
      commandTimestamp: data?['commandTimestamp'],
      commandStatus: _parseCommandStatus(data?['commandStatus'] ?? 'COMPLETED'),
      screenshots: screenshots,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'deviceName': deviceName,
      'userId': userId,
      'sessionStartTime': sessionStartTime,
      'sessionEndTime': sessionEndTime,
      'status': status.name,
      'currentCommand': currentCommand.name,
      'commandTimestamp': commandTimestamp,
      'commandStatus': commandStatus.name,
      'screenshots':
          screenshots.map((screenshot) => screenshot.toMap()).toList(),
    };
  }

  Session copyWith({
    String? sessionId,
    String? deviceName,
    String? userId,
    Timestamp? sessionStartTime,
    Timestamp? sessionEndTime,
    SessionStatus? status,
    CommandType? currentCommand,
    Timestamp? commandTimestamp,
    CommandStatus? commandStatus,
    List<ScreenshotReference>? screenshots,
  }) {
    return Session(
      sessionId: sessionId ?? this.sessionId,
      deviceName: deviceName ?? this.deviceName,
      userId: userId ?? this.userId,
      sessionStartTime: sessionStartTime ?? this.sessionStartTime,
      sessionEndTime: sessionEndTime ?? this.sessionEndTime,
      status: status ?? this.status,
      currentCommand: currentCommand ?? this.currentCommand,
      commandTimestamp: commandTimestamp ?? this.commandTimestamp,
      commandStatus: commandStatus ?? this.commandStatus,
      screenshots: screenshots ?? this.screenshots,
    );
  }

  static SessionStatus _parseSessionStatus(String value) {
    return SessionStatus.values.firstWhere(
      (status) => status.name == value,
      orElse: () => SessionStatus.WAITING,
    );
  }

  static CommandType _parseCommandType(String value) {
    return CommandType.values.firstWhere(
      (command) => command.name == value,
      orElse: () => CommandType.IDLE,
    );
  }

  static CommandStatus _parseCommandStatus(String value) {
    return CommandStatus.values.firstWhere(
      (status) => status.name == value,
      orElse: () => CommandStatus.COMPLETED,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Session &&
        other.sessionId == sessionId &&
        other.deviceName == deviceName &&
        other.userId == userId &&
        other.sessionStartTime == sessionStartTime &&
        other.sessionEndTime == sessionEndTime &&
        other.status == status &&
        other.currentCommand == currentCommand &&
        other.commandTimestamp == commandTimestamp &&
        other.commandStatus == commandStatus &&
        listEquals(other.screenshots, screenshots);
  }

  @override
  int get hashCode {
    return sessionId.hashCode ^
        deviceName.hashCode ^
        userId.hashCode ^
        sessionStartTime.hashCode ^
        sessionEndTime.hashCode ^
        status.hashCode ^
        currentCommand.hashCode ^
        commandTimestamp.hashCode ^
        commandStatus.hashCode ^
        screenshots.hashCode;
  }
}

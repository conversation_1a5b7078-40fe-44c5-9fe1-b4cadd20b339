import 'package:interview_hammer/model/management/language.dart';
import 'package:interview_hammer/model/management/user_profile.dart';

extension UserProfileExtensions on UserProfile {
  TranscriptionProvider getTranscriptionProvider() {
    final needsAzure = interviewLanguages?.any(
      (lang) => lang.transcriptionProvider == TranscriptionProvider.AZURE,
    );

    if (needsAzure == true) {
      return TranscriptionProvider.AZURE;
    }

    final needsGemini = interviewLanguages?.any(
      (lang) => lang.transcriptionProvider == TranscriptionProvider.GEMINI,
    );

    if (needsGemini == true) {
      return TranscriptionProvider.GEMINI;
    }

    return TranscriptionProvider.DEEPGRAM;
  }
}

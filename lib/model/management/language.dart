import 'package:flutter/material.dart';

import '../../utils/country_flag_extension.dart';
import '../../widgets/bottom_sheet_dropdowns/dropdown_item.dart';

class Language with CustomDropdownListFilter implements DropdownItem {
  final String code;
  final String label;
  final String countryCode;
  final TranscriptionProvider transcriptionProvider;

  const Language._(
      this.code, this.label, this.countryCode, this.transcriptionProvider);

  static const EN = Language._(
      'en', 'English (Default)', 'US', TranscriptionProvider.DEEPGRAM);
  static const EN_US = Language._(
      'en-US', 'English (United States)', 'US', TranscriptionProvider.DEEPGRAM);
  static const EN_AU = Language._(
      'en-AU', 'English (Australia)', 'AU', TranscriptionProvider.DEEPGRAM);
  static const EN_GB = Language._('en-GB', 'English (United Kingdom)', 'GB',
      TranscriptionProvider.DEEPGRAM);
  static const EN_NZ = Language._(
      'en-NZ', 'English (New Zealand)', 'NZ', TranscriptionProvider.DEEPGRAM);
  static const EN_IN = Language._(
      'en-IN', 'English (India)', 'IN', TranscriptionProvider.DEEPGRAM);
  static const BG =
      Language._('bg', 'Bulgarian', 'BG', TranscriptionProvider.DEEPGRAM);
  static const CA =
      Language._('ca', 'Catalan', 'ES', TranscriptionProvider.DEEPGRAM);
  static const ZH = Language._('zh', 'Chinese (Mandarin, Simplified)', 'CN',
      TranscriptionProvider.DEEPGRAM);
  static const ZH_CN = Language._('zh-CN', 'Chinese (Mandarin, Simplified)',
      'CN', TranscriptionProvider.DEEPGRAM);
  static const ZH_HANS = Language._('zh-Hans', 'Chinese (Mandarin, Simplified)',
      'CN', TranscriptionProvider.DEEPGRAM);
  static const ZH_TW = Language._('zh-TW', 'Chinese (Mandarin, Traditional)',
      'TW', TranscriptionProvider.DEEPGRAM);
  static const ZH_HANT = Language._('zh-Hant',
      'Chinese (Mandarin, Traditional)', 'TW', TranscriptionProvider.DEEPGRAM);
  static const ZH_HK = Language._('zh-HK', 'Chinese (Cantonese, Traditional)',
      'HK', TranscriptionProvider.DEEPGRAM);
  static const CS =
      Language._('cs', 'Czech', 'CZ', TranscriptionProvider.DEEPGRAM);
  static const DA =
      Language._('da', 'Danish', 'DK', TranscriptionProvider.DEEPGRAM);
  static const DA_DK = Language._(
      'da-DK', 'Danish (Denmark)', 'DK', TranscriptionProvider.DEEPGRAM);
  static const NL =
      Language._('nl', 'Dutch', 'NL', TranscriptionProvider.DEEPGRAM);
  static const NL_BE = Language._(
      'nl-BE', 'Flemish (Belgium)', 'BE', TranscriptionProvider.DEEPGRAM);
  static const ET =
      Language._('et', 'Estonian', 'EE', TranscriptionProvider.DEEPGRAM);
  static const FI =
      Language._('fi', 'Finnish', 'FI', TranscriptionProvider.DEEPGRAM);
  static const FR =
      Language._('fr', 'French', 'FR', TranscriptionProvider.DEEPGRAM);
  static const FR_CA = Language._(
      'fr-CA', 'French (Canada)', 'CA', TranscriptionProvider.DEEPGRAM);
  static const DE_DE =
      Language._('de', 'German', 'DE', TranscriptionProvider.DEEPGRAM);
  static const DE_CH = Language._(
      'de-CH', 'German (Switzerland)', 'CH', TranscriptionProvider.DEEPGRAM);
  static const EL =
      Language._('el', 'Greek', 'GR', TranscriptionProvider.DEEPGRAM);
  static const HI =
      Language._('hi', 'Hindi', 'IN', TranscriptionProvider.DEEPGRAM);
  static const HU =
      Language._('hu', 'Hungarian', 'HU', TranscriptionProvider.DEEPGRAM);
  static const ID =
      Language._('id', 'Indonesian', 'ID', TranscriptionProvider.DEEPGRAM);
  static const IT =
      Language._('it', 'Italian', 'IT', TranscriptionProvider.DEEPGRAM);
  static const JA =
      Language._('ja', 'Japanese', 'JP', TranscriptionProvider.DEEPGRAM);
  static const KO =
      Language._('ko', 'Korean', 'KR', TranscriptionProvider.DEEPGRAM);
  static const KO_KR = Language._(
      'ko-KR', 'Korean (South Korea)', 'KR', TranscriptionProvider.DEEPGRAM);
  static const LV =
      Language._('lv', 'Latvian', 'LV', TranscriptionProvider.DEEPGRAM);
  static const LT =
      Language._('lt', 'Lithuanian', 'LT', TranscriptionProvider.DEEPGRAM);
  static const MS =
      Language._('ms', 'Malay', 'MY', TranscriptionProvider.DEEPGRAM);
  static const NO =
      Language._('no', 'Norwegian', 'NO', TranscriptionProvider.DEEPGRAM);
  static const PL =
      Language._('pl', 'Polish', 'PL', TranscriptionProvider.DEEPGRAM);
  static const PT =
      Language._('pt', 'Portuguese', 'PT', TranscriptionProvider.DEEPGRAM);
  static const PT_BR = Language._(
      'pt-BR', 'Portuguese (Brazil)', 'BR', TranscriptionProvider.DEEPGRAM);
  static const PT_PT = Language._(
      'pt-PT', 'Portuguese (Portugal)', 'PT', TranscriptionProvider.DEEPGRAM);
  static const RO =
      Language._('ro', 'Romanian', 'RO', TranscriptionProvider.DEEPGRAM);
  static const RU =
      Language._('ru', 'Russian', 'RU', TranscriptionProvider.DEEPGRAM);
  static const SK =
      Language._('sk', 'Slovak', 'SK', TranscriptionProvider.DEEPGRAM);
  static const ES =
      Language._('es', 'Spanish', 'ES', TranscriptionProvider.DEEPGRAM);
  static const ES_419 = Language._('es-419', 'Spanish (Latin America)', 'MX',
      TranscriptionProvider.DEEPGRAM);
  static const SV =
      Language._('sv', 'Swedish', 'SE', TranscriptionProvider.DEEPGRAM);
  static const SV_SE = Language._(
      'sv-SE', 'Swedish (Sweden)', 'SE', TranscriptionProvider.DEEPGRAM);
  static const TH =
      Language._('th', 'Thai', 'TH', TranscriptionProvider.DEEPGRAM);
  static const TH_TH = Language._(
      'th-TH', 'Thai (Thailand)', 'TH', TranscriptionProvider.DEEPGRAM);
  static const TR =
      Language._('tr', 'Turkish', 'TR', TranscriptionProvider.DEEPGRAM);
  static const UK =
      Language._('uk', 'Ukrainian', 'UA', TranscriptionProvider.DEEPGRAM);
  static const VI =
      Language._('vi', 'Vietnamese', 'VN', TranscriptionProvider.DEEPGRAM);
  static const MULTI = Language._('multi', 'Multilingual (Auto-detect)', 'UN',
      TranscriptionProvider.DEEPGRAM);
  static const AR_AE = Language._('ar-AE', 'Arabic (United Arab Emirates)',
      'AE', TranscriptionProvider.GEMINI);
  static const AR_SA = Language._(
      'ar-SA', 'Arabic (Saudi Arabia)', 'SA', TranscriptionProvider.GEMINI);
  static const AR_KW = Language._(
      'ar-KW', 'Arabic (Kuwait)', 'KW', TranscriptionProvider.GEMINI);
  static const AR_BH = Language._(
      'ar-BH', 'Arabic (Bahrain)', 'BH', TranscriptionProvider.GEMINI);
  static const AR_EG =
      Language._('ar-EG', 'Arabic (Egypt)', 'EG', TranscriptionProvider.GEMINI);
  static const AR_DZ = Language._(
      'ar-DZ', 'Arabic (Algeria)', 'DZ', TranscriptionProvider.GEMINI);
  static const AR_IL = Language._(
      'ar-IL', 'Arabic (Israel)', 'IL', TranscriptionProvider.GEMINI);
  static const AR_IQ =
      Language._('ar-IQ', 'Arabic (Iraq)', 'IQ', TranscriptionProvider.GEMINI);
  static const AR_JO = Language._(
      'ar-JO', 'Arabic (Jordan)', 'JO', TranscriptionProvider.GEMINI);
  static const AR_LB = Language._(
      'ar-LB', 'Arabic (Lebanon)', 'LB', TranscriptionProvider.GEMINI);
  static const AR_LY =
      Language._('ar-LY', 'Arabic (Libya)', 'LY', TranscriptionProvider.GEMINI);
  static const AR_MA = Language._(
      'ar-MA', 'Arabic (Morocco)', 'MA', TranscriptionProvider.GEMINI);
  static const AR_OM =
      Language._('ar-OM', 'Arabic (Oman)', 'OM', TranscriptionProvider.GEMINI);
  static const AR_PS = Language._('ar-PS', 'Arabic (Palestinian Authority)',
      'PS', TranscriptionProvider.GEMINI);
  static const AR_QA =
      Language._('ar-QA', 'Arabic (Qatar)', 'QA', TranscriptionProvider.GEMINI);
  static const AR_SY =
      Language._('ar-SY', 'Arabic (Syria)', 'SY', TranscriptionProvider.GEMINI);
  static const AR_TN = Language._(
      'ar-TN', 'Arabic (Tunisia)', 'TN', TranscriptionProvider.GEMINI);
  static const AR_YE =
      Language._('ar-YE', 'Arabic (Yemen)', 'YE', TranscriptionProvider.GEMINI);

  static const List<Language> values = [
    EN,
    EN_US,
    EN_AU,
    EN_GB,
    EN_NZ,
    EN_IN,
    BG,
    CA,
    ZH,
    ZH_CN,
    ZH_HANS,
    ZH_TW,
    ZH_HANT,
    ZH_HK,
    CS,
    DA,
    DA_DK,
    NL,
    NL_BE,
    ET,
    FI,
    FR,
    FR_CA,
    DE_DE,
    DE_CH,
    EL,
    HI,
    HU,
    ID,
    IT,
    JA,
    KO,
    KO_KR,
    LV,
    LT,
    MS,
    NO,
    PL,
    PT,
    PT_BR,
    PT_PT,
    RO,
    RU,
    SK,
    ES,
    ES_419,
    SV,
    SV_SE,
    TH,
    TH_TH,
    TR,
    UK,
    VI,
    MULTI,
    AR_AE,
    AR_SA,
    AR_KW,
    AR_BH,
    AR_EG,
    AR_DZ,
    AR_IL,
    AR_IQ,
    AR_JO,
    AR_LB,
    AR_LY,
    AR_MA,
    AR_OM,
    AR_PS,
    AR_QA,
    AR_SY,
    AR_TN,
    AR_YE,
  ];

  @override
  String get displayText => label;

  @override
  Widget? get leadingWidget => Text(
        countryCode.toFlag,
        style: TextStyle(fontSize: 24),
      );

  String get aiName {
    // remove (Default) from the label.
    return label.replaceAll(' (Default)', '');
  }

  bool get isEnglish {
    return code.startsWith('en');
  }

  // needed for dropdown label.
  @override
  String toString() {
    return label;
  }

  @override
  bool filter(String query) {
    return label.toLowerCase().contains(query.toLowerCase()) ||
        code.toLowerCase().contains(query.toLowerCase());
  }
}

mixin CustomDropdownListFilter {
  bool filter(String query);
}

enum TranscriptionProvider {
  DEEPGRAM,
  AZURE,
  GEMINI,
}

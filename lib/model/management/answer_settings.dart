import 'package:indent/indent.dart';

import '../../api/ai/prompts.dart';
import '../../utils/enum_utils.dart';

class AnswerSettings {
  static const defaultBehavioralStructure = BehavioralAnswerStructure.STAR;
  static const defaultResponseStyle = ResponseStyle.BULLET_POINTS;
  static const defaultLength = AnswerLength.MEDIUM;
  static const defaultSensitivity = QuestionDetectionSensitivity.HIGH;
  static const defaultEnglishForTechnicalTerms = EnglishForTechnicalTerms.YES;
  static const defaultMaxNumberOfImages = 3;
  static const defaultTranscriptDurationSeconds = 30;

  final BehavioralAnswerStructure behavioralStructure;
  final ResponseStyle responseStyle;
  final AnswerLength length;
  final QuestionDetectionSensitivity questionDetectionSensitivity;
  final EnglishForTechnicalTerms englishForTechnicalTerms;
  final int maxNumberOfImages;
  final int transcriptDurationSeconds;

  AnswerSettings({
    this.behavioralStructure = defaultBehavioralStructure,
    this.responseStyle = defaultResponseStyle,
    this.length = defaultLength,
    this.questionDetectionSensitivity = defaultSensitivity,
    this.englishForTechnicalTerms = defaultEnglishForTechnicalTerms,
    this.maxNumberOfImages = defaultMaxNumberOfImages,
    this.transcriptDurationSeconds = defaultTranscriptDurationSeconds,
  });

  factory AnswerSettings.fromMap(Map<String, dynamic>? data) {
    if (data == null) return AnswerSettings();

    return AnswerSettings(
      behavioralStructure: enumFromString(BehavioralAnswerStructure.values,
          data['behavioralStructure'], defaultBehavioralStructure),
      responseStyle: enumFromString(
          ResponseStyle.values, data['responseStyle'], defaultResponseStyle),
      length:
          enumFromString(AnswerLength.values, data['length'], defaultLength),
      questionDetectionSensitivity: enumFromString(
          QuestionDetectionSensitivity.values,
          data['questionDetectionSensitivity'],
          defaultSensitivity),
      englishForTechnicalTerms: enumFromString(EnglishForTechnicalTerms.values,
          data['englishForTechnicalTerms'], defaultEnglishForTechnicalTerms),
      maxNumberOfImages:
          (data['maxNumberOfImages'] as int?) ?? defaultMaxNumberOfImages,
      transcriptDurationSeconds: (data['transcriptDurationSeconds'] as int?) ??
          defaultTranscriptDurationSeconds,
    );
  }

  // Converts enum values to strings for Firestore storage.
  // Example: BehavioralAnswerStructure.STAR becomes 'STAR'
  Map<String, dynamic> toFirestore() {
    return {
      'behavioralStructure': behavioralStructure.toString().split('.').last,
      'responseStyle': responseStyle.toString().split('.').last,
      'length': length.toString().split('.').last,
      'questionDetectionSensitivity':
          questionDetectionSensitivity.toString().split('.').last,
      'englishForTechnicalTerms':
          englishForTechnicalTerms.toString().split('.').last,
      'maxNumberOfImages': maxNumberOfImages,
      'transcriptDurationSeconds': transcriptDurationSeconds,
    };
  }

  AnswerSettings copyWith({
    BehavioralAnswerStructure? behavioralStructure,
    ResponseStyle? responseStyle,
    AnswerLength? length,
    QuestionDetectionSensitivity? questionDetectionSensitivity,
    EnglishForTechnicalTerms? englishForTechnicalTerms,
    int? maxNumberOfImages,
    int? transcriptDurationSeconds,
  }) {
    return AnswerSettings(
      behavioralStructure: behavioralStructure ?? this.behavioralStructure,
      responseStyle: responseStyle ?? this.responseStyle,
      length: length ?? this.length,
      questionDetectionSensitivity:
          questionDetectionSensitivity ?? this.questionDetectionSensitivity,
      englishForTechnicalTerms:
          englishForTechnicalTerms ?? this.englishForTechnicalTerms,
      maxNumberOfImages: maxNumberOfImages ?? this.maxNumberOfImages,
      transcriptDurationSeconds:
          transcriptDurationSeconds ?? this.transcriptDurationSeconds,
    );
  }

  @override
  String toString() {
    return 'AnswerSettings{behavioralStructure: $behavioralStructure, responseStyle: $responseStyle, length: $length, questionDetectionSensitivity: $questionDetectionSensitivity, englishForTechnicalTerms: $englishForTechnicalTerms, maxNumberOfImages: $maxNumberOfImages, transcriptDurationSeconds: $transcriptDurationSeconds}';
  }
}

enum BehavioralAnswerStructure {
  STAR,
  CAR,
  SOAR,
  PAR,
  SOARA;

  String get description {
    switch (this) {
      case STAR:
        return 'STAR: Situation, Task, Action, Result (default)';
      case CAR:
        return 'CAR: Context, Action, Result';
      case SOAR:
        return 'SOAR: Situation, Obstacle, Action, Result';
      case PAR:
        return 'PAR: Problem, Action, Result';
      case SOARA:
        return 'SOARA: Situation, Objective, Action, Result, Aftermath';
    }
  }

  String get headerInfo {
    return """
    |Structuring AI-Generated Behavioral Responses
    |
    |Choose a framework to organize the AI's answers to behavioral questions.
    |This setting affects how the AI structures its responses, helping to
    |provide comprehensive and relevant answers that highlight your skills.
    """
        .trimMargin();
  }

  String get info {
    switch (this) {
      case STAR:
        return """
        |• Default option, suitable for most behavioral questions
        |• AI generates a complete narrative: Situation, Task, Action, Result
        |• Balanced approach, covering context and outcome
        |• Differs from CAR by including a specific Task component
        """
            .trimMargin();
      case CAR:
        return """
        |• Streamlined structure for more concise AI-generated responses
        |• AI focuses on Context, Action, Result
        |• Emphasizes your actions more than STAR
        |• Skips the Task component, making it shorter than STAR
        """
            .trimMargin();
      case SOAR:
        return """
        |• AI highlights problem-solving in its responses
        |• Includes Situation, Obstacle, Action, Result
        |• Unlike STAR, explicitly states the challenge faced
        |• Good for showcasing how you overcome specific difficulties
        """
            .trimMargin();
      case PAR:
        return """
        |• AI generates responses focused on problem-solving
        |• Covers Problem, Action, Result
        |• Most concise option, skipping broader context
        |• Differs from others by starting directly with the problem
        """
            .trimMargin();
      case SOARA:
        return """
        |• Most comprehensive AI-generated responses
        |• Includes Situation, Objective, Action, Result, Aftermath
        |• Unlike others, includes your goals and long-term impacts
        |• Best for complex scenarios or leadership experiences
        """
            .trimMargin();
    }
  }

  String get prompt {
    switch (this) {
      case STAR:
        return """
        |STAR (Situation, Task, Action, Result)
        """
            .trimMargin();
      case CAR:
        return """
        |CAR (Context, Action, Result)
        """
            .trimMargin();
      case SOAR:
        return """
        |SOAR (Situation, Obstacle, Action, Result)
        """
            .trimMargin();
      case PAR:
        return """
        |PAR (Problem, Action, Result)
        """
            .trimMargin();
      case SOARA:
        return """
        |SOARA (Situation, Objective, Action, Result, Aftermath)
        """
            .trimMargin();
    }
  }
}

enum ResponseStyle {
  BULLET_POINTS,
  CONVERSATIONAL,
  EXAMPLE_DRIVEN;

  String get description {
    switch (this) {
      case BULLET_POINTS:
        return 'Bullet Points: Concise, easy-to-scan list of key points (default)';
      case CONVERSATIONAL:
        return 'Conversational: Natural, spoken-like response with filler words';
      case EXAMPLE_DRIVEN:
        return 'Example-Driven: Main points illustrated with specific examples';
    }
  }

  String get headerInfo {
    return """
    |Tailoring AI-Generated Communication Style
    |
    |Choose how the AI should format its responses.
    |This setting affects the presentation style of the AI-generated answers,
    |impacting how the information is structured and delivered.
    """
        .trimMargin();
  }

  String get info {
    switch (this) {
      case BULLET_POINTS:
        return """
        |• AI generates responses as concise bullet points
        |• Easy to scan and remember during mock interviews
        |• Best for delivering key points quickly
        |• Unlike other styles, avoids narrative format
        """
            .trimMargin();
      case CONVERSATIONAL:
        return """
        |• AI creates responses in a natural, spoken-like style
        |• Designed for practicing natural conversation flow
        |• Includes conversational elements and flow
        |• Contrasts with Bullet Points by using full sentences
        |• Unlike Example-Driven, may not always include specific examples
        """
            .trimMargin();
      case EXAMPLE_DRIVEN:
        return """
        |• AI illustrates main points with specific examples
        |• Combines elements of both Bullet Points and Conversational styles
        |• Unlike others, always includes concrete scenarios
        |• Best for demonstrating practical application of skills
        """
            .trimMargin();
    }
  }

  String get prompt {
    switch (this) {
      case BULLET_POINTS:
        return """
        |Bullet points if appropriate, bullet point should start with •.
        """
            .trimMargin();
      case CONVERSATIONAL:
        return """
        |Clear and simple, make sure they are easy to read aloud. 
        |Add natural sounds like 'hmm' and 'uh' to make the responses sound more conversational.
        """
            .trimMargin();
      case EXAMPLE_DRIVEN:
        return """
        |1. State each main point clearly.
        |2. Follow each point with a specific, relevant example.
        |3. Ensure examples are concise and illustrative.
        """
            .trimMargin();
    }
  }
}

enum AnswerLength {
  SHORT,
  MEDIUM,
  LONG;

  String get description {
    switch (this) {
      case SHORT:
        return 'Short: Concise and to-the-point';
      case MEDIUM:
        return 'Medium: Balanced level of detail (default)';
      case LONG:
        return 'Long: In-depth and comprehensive (Not recommended on mobile devices)';
    }
  }

  String get headerInfo {
    return """
    |Controlling AI-Generated Answer Length
    |
    |Select the desired length for AI-generated responses.
    |This setting determines how detailed or concise the AI's answers will be,
    |allowing you to tailor responses to different mock interview scenarios.
    """
        .trimMargin();
  }

  String get info {
    switch (this) {
      case SHORT:
        return """
        |• AI generates brief, focused responses.
        |• Typically 2-3 sentences or 30-50 words.
        |• Best for most mobile devices.
        |• Unlike Medium or Long, omits most supporting details.
        """
            .trimMargin();
      case MEDIUM:
        return """
        |• AI produces balanced, moderately detailed answers
        |• Usually 4-6 sentences or 75-125 words
        |• Provides context and some supporting information
        |• Best for desktop or bigger screens.
        """
            .trimMargin();
      case LONG:
        return """
        |• AI creates comprehensive, in-depth responses
        |• Typically 7+ sentences or 150+ words
        |• Includes extensive details and examples
        |• Unlike Short or Medium, covers multiple aspects of the question
        |• Not recommended unless running on desktop or tablet due to readability concerns.
        """
            .trimMargin();
    }
  }

  String get prompt {
    switch (this) {
      case SHORT:
        return """
        |Brief, 1-3 sentences maximum.
        |Focus on the most crucial information only.
        """
            .trimMargin();
      case MEDIUM:
        return """
        |Aim for a paragraph-length response.
        |Provide key details and brief explanations.
        """
            .trimMargin();
      case LONG:
        return """
        |Give a comprehensive answer with multiple paragraphs.
        |Include detailed explanations, examples, and context.
        """
            .trimMargin();
    }
  }
}

enum QuestionDetectionSensitivity {
  LOW,
  MEDIUM,
  HIGH;

  String get description {
    switch (this) {
      case LOW:
        return 'Low: Detect only explicit questions';
      case MEDIUM:
        return 'Medium: Balance between explicit and implicit questions';
      case HIGH:
        return 'High: Detect both explicit and subtle implicit questions (default)';
    }
  }

  String get headerInfo {
    return """
    |Question Detection Sensitivity
    |
    |Control how the AI identifies questions in the mock interview transcript.
    """
        .trimMargin();
  }

  String get info {
    switch (this) {
      case LOW:
        return """
        |• AI only responds to direct, explicitly phrased questions
        |• Minimizes false positives but may miss subtle inquiries
        |• Best for structured mock interviews with clear question-answer format
        """
            .trimMargin();
      case MEDIUM:
        return """
        |• AI detects both explicit questions and clear implicit inquiries
        |• Balances sensitivity and specificity
        |• Suitable for most mock interview types and conversational styles
        """
            .trimMargin();
      case HIGH:
        return """
        |• AI identifies explicit questions and subtle implied inquiries
        |• May interpret some statements as questions
        |• Ideal for unstructured or casual conversations
        |• Can lead to more frequent AI responses
        """
            .trimMargin();
    }
  }

  String get prompt {
    switch (this) {
      case LOW:
        return questionDetectionPromptSystem_low;
      case MEDIUM:
        return questionDetectionPromptSystem_medium;
      case HIGH:
        return questionDetectionPromptSystem_high;
    }
  }
}

enum EnglishForTechnicalTerms {
  YES,
  NO;

  String description(String answerLanguage) {
    switch (this) {
      case YES:
        return 'Yes: Use English for technical terms and expressions (default)';
      case NO:
        return 'No: Use $answerLanguage also for technical terms and expressions';
    }
  }

  String get headerInfo {
    return """
    |Technical Term Language
    |
    |Choose whether the AI should translate technical terms and expressions into English.
    """
        .trimMargin();
  }

  String info(String answerLanguage) {
    switch (this) {
      case YES:
        return """
        |• AI translates technical terms and expressions into English
        |• Mixes english with the $answerLanguage
        """
            .trimMargin();
      case NO:
        return """
        |• AI uses the $answerLanguage for technical terms and expressions
        """
            .trimMargin();
    }
  }

  String get prompt {
    switch (this) {
      case YES:
        return """
        |
        |Use english for all technical terms and expressions.
        """
            .trimMargin();
      case NO:
        return """""".trimMargin();
    }
  }
}

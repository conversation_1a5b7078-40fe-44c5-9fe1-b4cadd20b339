class PersonalInfo {
  final String? name;
  final String? currentRole;
  final String? company;
  final String? yearsOfExperience;
  final String? workHistory;
  final List<String>? skills;
  final String? education;
  final String? additionalInfo;

  PersonalInfo({
    this.name,
    this.currentRole,
    this.company,
    this.yearsOfExperience,
    this.workHistory,
    this.skills,
    this.education,
    this.additionalInfo,
  });

  factory PersonalInfo.fromMap(Map<String, dynamic>? data) {
    if (data == null) return PersonalInfo();

    return PersonalInfo(
      name: data['name']?.trim() ?? '',
      currentRole: data['currentRole']?.trim() ?? '',
      company: data['company']?.trim() ?? '',
      yearsOfExperience: data['yearsOfExperience']?.trim() ?? '',
      workHistory: data['workHistory']?.trim() ?? '',
      skills:
          List<String>.from(data['skills'] ?? []).map((s) => s.trim()).toList(),
      education: data['education']?.trim() ?? '',
      additionalInfo: data['additionalInfo']?.trim() ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'currentRole': currentRole,
      'company': company,
      'yearsOfExperience': yearsOfExperience,
      'workHistory': workHistory,
      'skills': skills,
      'education': education,
      'additionalInfo': additionalInfo,
    };
  }

  PersonalInfo copyWith({
    String? name,
    String? currentRole,
    String? company,
    String? yearsOfExperience,
    String? workHistory,
    List<String>? skills,
    String? education,
    String? additionalInfo,
  }) {
    return PersonalInfo(
      name: name ?? this.name,
      currentRole: currentRole ?? this.currentRole,
      company: company ?? this.company,
      yearsOfExperience: yearsOfExperience ?? this.yearsOfExperience,
      workHistory: workHistory ?? this.workHistory,
      skills: skills ?? this.skills,
      education: education ?? this.education,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  @override
  String toString() {
    return 'PersonalInfo{name: $name, currentRole: $currentRole, company: $company, yearsOfExperience: $yearsOfExperience, workHistory: $workHistory, skills: $skills, education: $education, additionalInfo: $additionalInfo}';
  }

  bool isEmpty() {
    return (name == null || name!.trim().isEmpty) &&
        (currentRole == null || currentRole!.trim().isEmpty) &&
        (company == null || company!.trim().isEmpty) &&
        (yearsOfExperience == null || yearsOfExperience!.trim().isEmpty) &&
        (workHistory == null || workHistory!.trim().isEmpty) &&
        (skills == null ||
            skills!.isEmpty ||
            skills!.every((s) => s.trim().isEmpty)) &&
        (education == null || education!.trim().isEmpty) &&
        (additionalInfo == null || additionalInfo!.trim().isEmpty);
  }

  String toPrompt() {
    List<String> promptParts = [];

    if (name != null && name!.trim().isNotEmpty) {
      promptParts.add('name: ${name!.trim()}');
    }
    if (currentRole != null && currentRole!.trim().isNotEmpty) {
      promptParts.add('current role: ${currentRole!.trim()}');
    }
    if (company != null && company!.trim().isNotEmpty) {
      promptParts.add('company: ${company!.trim()}');
    }
    if (yearsOfExperience != null && yearsOfExperience!.trim().isNotEmpty) {
      promptParts.add('years of experience: ${yearsOfExperience!.trim()}');
    }
    if (workHistory != null && workHistory!.trim().isNotEmpty) {
      promptParts.add('work history: ${workHistory!.trim()}');
    }
    if (skills != null && skills!.isNotEmpty) {
      promptParts.add(
          'skills: ${skills!.where((s) => s.trim().isNotEmpty).join(", ")}');
    }
    if (education != null && education!.trim().isNotEmpty) {
      promptParts.add('education: ${education!.trim()}');
    }
    if (additionalInfo != null && additionalInfo!.trim().isNotEmpty) {
      promptParts.add('additional info: ${additionalInfo!.trim()}');
    }

    return promptParts.join('\n');
  }
}

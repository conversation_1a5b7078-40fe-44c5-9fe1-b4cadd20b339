import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:interview_hammer/model/management/language.dart';
import 'package:interview_hammer/model/management/personal_info.dart';

import 'answer_settings.dart';

const PLAN_TYPE_FREE = "free";
const PLAN_TYPE_MONTHLY_UNLIMITED = "monthly unlimited";

class UserProfile {
  static const defaultLanguage = Language.EN;

  final String? uid;
  final String? email;

  final Timestamp? createdAt;
  final Timestamp? lastLogin;

  // plan
  final String? planType;
  final String? paymentStatus;
  final Timestamp? planExpirationDate;

  final int? usedMinutes;

  final int? availableMinutes;
  final int? sessionMaxMinutes;

  //interview settings
  final bool? automaticQuestionAnswering;
  final bool? showTranscript;
  final bool? showCustomMessageInput;
  final List<Language>? interviewLanguages;
  final Language? answerLanguage;

  final String? interviewTopic;
  final PersonalInfo? personalInfoData;
  final AnswerSettings? answerSettings;
  final String? extraInstructions;

  // marketing
  final String? marketingSource;

  UserProfile({
    this.uid,
    this.email,
    this.createdAt,
    this.lastLogin,
    this.planType,
    this.paymentStatus,
    this.planExpirationDate,
    this.availableMinutes,
    this.usedMinutes,
    this.sessionMaxMinutes,
    this.automaticQuestionAnswering,
    this.showTranscript,
    this.showCustomMessageInput,
    this.interviewLanguages,
    this.answerLanguage,
    this.interviewTopic,
    this.personalInfoData,
    this.answerSettings,
    this.extraInstructions,
    this.marketingSource,
  });

  factory UserProfile.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> snapshot,
    SnapshotOptions? options,
  ) {
    final data = snapshot.data();

    final interviewLanguagesList =
        (data?['interviewLanguages'] as List<dynamic>? ?? [])
            .map((s) => Language.values.firstWhere(
                  (lang) => lang.code == s,
                  orElse: () => defaultLanguage,
                ))
            .toSet()
            .toList();

    final interviewLanguages = interviewLanguagesList.isEmpty
        ? [defaultLanguage]
        : interviewLanguagesList;

    return UserProfile(
      uid: snapshot.id,
      email: data?['email'],
      createdAt: data?['createdAt'],
      lastLogin: data?['lastLogin'],
      planType: data?['planType'],
      paymentStatus: data?['paymentStatus'],
      planExpirationDate: data?['planExpirationDate'],
      availableMinutes: data?['availableMinutes'],
      usedMinutes: data?['usedMinutes'],
      sessionMaxMinutes: data?['sessionMaxMinutes'],
      automaticQuestionAnswering: data?['automaticQuestionAnswering'],
      showTranscript: data?['showTranscript'],
      showCustomMessageInput: data?['showCustomMessageInput'],
      interviewLanguages: interviewLanguages,
      answerLanguage: Language.values.firstWhere(
        (lang) => lang.code == data?['answerLanguage'],
        orElse: () => defaultLanguage,
      ),
      interviewTopic: data?['interviewTopic'],
      personalInfoData: PersonalInfo.fromMap(data?['personalInfoData']),
      answerSettings: AnswerSettings.fromMap(data?['answerSettings']),
      extraInstructions: data?['extraInstructions'],
      marketingSource: data?['marketingSource'],
    );
  }

  Map<String, dynamic> toFirestore(bool isNewUser) {
    return {
      if (uid != null) "uid": uid,
      if (email != null) "email": email,
      if (isNewUser) "createdAt": FieldValue.serverTimestamp(),
      "lastLogin": FieldValue.serverTimestamp(),
      if (planType != null) "planType": planType,
      if (paymentStatus != null) "paymentStatus": paymentStatus,
      if (planExpirationDate != null) "planExpirationDate": planExpirationDate,
      if (availableMinutes != null) "availableMinutes": availableMinutes,
      if (usedMinutes != null) "usedMinutes": usedMinutes,
      if (sessionMaxMinutes != null) "sessionMaxMinutes": sessionMaxMinutes,
      if (automaticQuestionAnswering != null)
        "automaticQuestionAnswering": automaticQuestionAnswering,
      if (showTranscript != null) "showTranscript": showTranscript,
      if (showCustomMessageInput != null)
        "showCustomMessageInput": showCustomMessageInput,
      if (interviewLanguages != null)
        "interviewLanguages": interviewLanguages?.map((e) => e.code).toList(),
      if (answerLanguage != null) "answerLanguage": answerLanguage?.code,
      if (interviewTopic != null) "interviewTopic": interviewTopic,
      if (personalInfoData != null)
        "personalInfoData": personalInfoData?.toFirestore(),
      if (answerSettings != null)
        "answerSettings": answerSettings?.toFirestore(),
      if (extraInstructions != null) "extraInstructions": extraInstructions,
      if (marketingSource != null) "marketingSource": marketingSource,
    };
  }

  @override
  String toString() {
    return 'UserProfile{uid: $uid, email: $email, createdAt: $createdAt, lastLogin: $lastLogin, planType: $planType, paymentStatus: $paymentStatus, planExpirationDate: $planExpirationDate, availableMinutes: $availableMinutes, usedMinutes: $usedMinutes, sessionMaxMinutes: $sessionMaxMinutes, automaticQuestionAnswering: $automaticQuestionAnswering, showTranscript: $showTranscript, showCustomMessageInput: $showCustomMessageInput, interviewLanguages: $interviewLanguages, answerLanguage:$answerLanguage interviewTopic: $interviewTopic, personalInfoData: $personalInfoData, answerSettings: $answerSettings, extraInstructions: $extraInstructions, marketingSource: $marketingSource}';
  }

  UserProfile copyWith({
    String? uid,
    String? email,
    Timestamp? createdAt,
    Timestamp? lastLogin,
    String? planType,
    String? paymentStatus,
    Timestamp? planExpirationDate,
    int? usedMinutes,
    int? availableMinutes,
    int? sessionMaxMinutes,
    bool? automaticQuestionAnswering,
    bool? showTranscript,
    bool? showCustomMessageInput,
    List<Language>? interviewLanguages,
    Language? answerLanguage,
    String? interviewTopic,
    PersonalInfo? personalInfoData,
    AnswerSettings? answerSettings,
    String? extraInstructions,
    String? marketingSource,
  }) {
    return UserProfile(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      planType: planType ?? this.planType,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      planExpirationDate: planExpirationDate ?? this.planExpirationDate,
      usedMinutes: usedMinutes ?? this.usedMinutes,
      availableMinutes: availableMinutes ?? this.availableMinutes,
      sessionMaxMinutes: sessionMaxMinutes ?? this.sessionMaxMinutes,
      automaticQuestionAnswering:
          automaticQuestionAnswering ?? this.automaticQuestionAnswering,
      showTranscript: showTranscript ?? this.showTranscript,
      showCustomMessageInput:
          showCustomMessageInput ?? this.showCustomMessageInput,
      interviewLanguages: interviewLanguages ?? this.interviewLanguages,
      answerLanguage: answerLanguage ?? this.answerLanguage,
      interviewTopic: interviewTopic ?? this.interviewTopic,
      personalInfoData: personalInfoData ?? this.personalInfoData,
      answerSettings: answerSettings ?? this.answerSettings,
      extraInstructions: extraInstructions ?? this.extraInstructions,
      marketingSource: marketingSource ?? this.marketingSource,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is UserProfile &&
        other.uid == uid &&
        other.email == email &&
        other.createdAt == createdAt &&
        other.lastLogin == lastLogin &&
        other.planType == planType &&
        other.paymentStatus == paymentStatus &&
        other.planExpirationDate == planExpirationDate &&
        other.usedMinutes == usedMinutes &&
        other.availableMinutes == availableMinutes &&
        other.sessionMaxMinutes == sessionMaxMinutes &&
        other.automaticQuestionAnswering == automaticQuestionAnswering &&
        other.showTranscript == showTranscript &&
        other.showCustomMessageInput == showCustomMessageInput &&
        listEquals(other.interviewLanguages, interviewLanguages) &&
        other.answerLanguage == answerLanguage &&
        other.interviewTopic == interviewTopic &&
        other.personalInfoData == personalInfoData &&
        other.answerSettings == answerSettings &&
        other.extraInstructions == extraInstructions &&
        other.marketingSource == marketingSource;
  }

  @override
  int get hashCode {
    return uid.hashCode ^
        email.hashCode ^
        createdAt.hashCode ^
        lastLogin.hashCode ^
        planType.hashCode ^
        paymentStatus.hashCode ^
        planExpirationDate.hashCode ^
        usedMinutes.hashCode ^
        availableMinutes.hashCode ^
        sessionMaxMinutes.hashCode ^
        automaticQuestionAnswering.hashCode ^
        showTranscript.hashCode ^
        showCustomMessageInput.hashCode ^
        interviewLanguages.hashCode ^
        answerLanguage.hashCode ^
        interviewTopic.hashCode ^
        personalInfoData.hashCode ^
        answerSettings.hashCode ^
        extraInstructions.hashCode ^
        marketingSource.hashCode;
  }
}

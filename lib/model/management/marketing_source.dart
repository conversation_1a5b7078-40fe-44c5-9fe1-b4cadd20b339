import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../widgets/bottom_sheet_dropdowns/dropdown_item.dart';

class MarketingSource implements DropdownItem {
  final String name;
  final IconData icon;

  const MarketingSource._({
    required this.name,
    required this.icon,
  });

  static const reddit =
      MarketingSource._(name: 'Reddit', icon: FontAwesomeIcons.reddit);
  static const youtube =
      MarketingSource._(name: 'YouTube', icon: FontAwesomeIcons.youtube);
  static const tiktok =
      MarketingSource._(name: 'TikTok', icon: FontAwesomeIcons.tiktok);
  static const facebook =
      MarketingSource._(name: 'Facebook', icon: FontAwesomeIcons.facebook);
  static const twitter =
      MarketingSource._(name: 'Twitter', icon: FontAwesomeIcons.twitter);
  static const other =
      MarketingSource._(name: 'Other', icon: Icons.help_outline);

  static const List<MarketingSource> values = [
    reddit,
    youtube,
    tiktok,
    facebook,
    twitter,
    other,
  ];

  @override
  String get displayText => name;

  @override
  Widget? get leadingWidget {
    if (icon == FontAwesomeIcons.reddit ||
        icon == FontAwesomeIcons.youtube ||
        icon == FontAwesomeIcons.tiktok ||
        icon == FontAwesomeIcons.facebook ||
        icon == FontAwesomeIcons.twitter) {
      return FaIcon(icon, size: 20);
    }
    return Icon(icon, size: 20);
  }

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MarketingSource && other.name == name && other.icon == icon;
  }

  @override
  int get hashCode => Object.hash(name, icon);
}

class AIConfig {
  final String baseUrl;
  final String apiKey;
  final String imageModel;
  final String questionDetectionModel;
  final String answerGenerationModel;
  final Map<String, String> modelFallbacks;
  final List<String> reasoningModels;
  final List<String> nonOpenAiModels;

  AIConfig({
    required this.baseUrl,
    required this.api<PERSON>ey,
    required this.imageModel,
    required this.questionDetectionModel,
    required this.answerGenerationModel,
    required this.modelFallbacks,
    required this.reasoningModels,
    required this.nonOpenAiModels,
  });

  // The single source of truth is the remote config, we have as a fallback.
  factory AIConfig.defaultConfig() {
    return AIConfig(
      baseUrl: "https://lite.meetingaitools.com",
      apiKey: "sk-J--S5q2AN323UnA3mFSD4A",
      imageModel: "o4-mini",
      questionDetectionModel: "gpt-4.1-mini",
      answerGenerationModel: "gpt-4.1",
      modelFallbacks: {
        'gemini-2.5-pro': 'gpt-4.1',
        'gemini-2.5-flash': 'gpt-4.1-mini',
        'gpt-4o': 'gpt-4.1-mini',
      },
      reasoningModels: ['o1', 'o3-mini', 'o3', 'o4-mini'],
      nonOpenAiModels: ['gemini'],
    );
  }

  factory AIConfig.fromJson(Map<String, dynamic> json) {
    return AIConfig(
      baseUrl: json['baseUrl'],
      apiKey: json['apiKey'],
      imageModel: json['imageModel'],
      questionDetectionModel: json['questionDetectionModel'],
      answerGenerationModel: json['answerGenerationModel'],
      modelFallbacks: Map<String, String>.from(json['modelFallbacks']),
      reasoningModels: List<String>.from(json['reasoningModels']),
      nonOpenAiModels: List<String>.from(json['nonOpenAiModels']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'baseUrl': baseUrl,
      'apiKey': apiKey,
      'imageModel': imageModel,
      'questionDetectionModel': questionDetectionModel,
      'answerGenerationModel': answerGenerationModel,
      'modelFallbacks': modelFallbacks,
      'reasoningModels': reasoningModels,
      'nonOpenAiModels': nonOpenAiModels,
    };
  }
}

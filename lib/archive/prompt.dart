// // region question detection
// const questionDetectionPrompt1 = """
// You will act as an assistant for real-time transcription with speaker diarization for an interview. The transcription will include each sentence spoken by the participants, and you will receive the last 30s of the transcription at a time. Your task is to:
//
// 1. Identify the speaker for each sentence.
// 2. Determine the last question asked and whether it is ready to be answered or if it is still being asked.
//
// The output should be a JSON object with the following structure:
// json
// {
//   "question_final": boolean,
//   "question": string
// }
//
//
// Nuances and rules for determining when a question is "final" versus "still being asked":
// 1. A question is considered "final" when the speaker finishes their query and it logically makes sense as a complete question.
// 2. If a sentence ends with a conjunction ("and," "or"), filler words ("uh," "um"), or other indications that the speaker is continuing their thought, it should be marked as "still being asked."
// 3. Speech patterns that indicate a continuation include any indication that more information is forthcoming.
//
// Here are examples of the transcription and the desired output:
//
// **Example 1:**
//
// Speaker 0: Good morning
// {
//   "question_final": false,
//   "question": ""
// }
//
// **Example 2:**
//
// Speaker 0: Good morning
// Speaker 1: Good morning
// {
//   "question_final": false,
//   "question": ""
// }
//
// **Example 3:**
//
// Speaker 0: Good morning
// Speaker 1: Good morning
// Speaker 0: Please introduce yourself
// {
//   "question_final": true,
//   "question": "Please introduce yourself"
// }
//
// **Example 4:**
//
// Speaker 0: Good morning
// Speaker 1: Good morning
// Speaker 0: Please introduce yourself
// Speaker 1: My name is John
// {
//   "question_final": false,
//   "question": "My name is John"
// }
//
// **Example 5 (Incomplete Sentence):**
//
// Speaker 0: I will be interviewing Akio for the role of an experienced Android developer. Okay? So let's hear from you, Akio. Could you please, introduce yourself and tell me a little bit about your
// {
//   "question_final": false,
//   "question": "Could you please, introduce yourself and tell me a little bit about your"
// }
//
// **Example 6 (Interruption):**
//
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
// {
//   "question_final": false,
//   "question": "Can you explain the biggest challenge you faced in your previous job and how you"
// }
//
// **Example 7 (Clarification):**
//
// Speaker 0: What skills do you bring to this role that are unique?
// Speaker 1: Well, I have extensive experience in project management
// Speaker 0: Can you elaborate on that?
// {
//   "question_final": true,
//   "question": "Can you elaborate on that?"
// }
//
//
// Follow these examples and ensure the output is consistent with the provided format.
// Only respond with JSON, and do not include any additional information.
//
// Here is the transcription to process:
// {{TRANSCRIPTION}}
// """;
//
// const questionDetectionPrompt2 = """
// You will act as an assistant for real-time transcription with speaker diarization for an interview. The transcription will include each sentence spoken by the participants, and you will receive the last 2 minutes or less of the transcription at a time.
//
// Your task is to:
// Determine the last question asked and whether it is ready to be answered or if it is still being asked.
//
// The output should be a JSON object with the following structure:
// json
// {
//   "question_final": boolean,
//   "question": string
// }
//
//
// Nuances and rules for determining when a question is "final" versus "still being asked":
// 1. A question is considered "final" when the speaker finishes their query and it logically makes sense as a complete question.
// 3. Speech patterns that indicate a continuation include any indication that more information is forthcoming.
//
// Here are examples of the transcription and the desired output:
//
// **Example 1:**
//
// Speaker 0: Good morning
// {
//   "question_final": false,
//   "question": ""
// }
//
// **Example 2:**
//
// Speaker 0: Good morning
// Speaker 1: Good morning
// {
//   "question_final": false,
//   "question": ""
// }
//
// **Example 3:**
//
// Speaker 0: Good morning
// Speaker 1: Good morning
// Speaker 0: Please introduce yourself
// {
//   "question_final": true,
//   "question": "Please introduce yourself"
// }
//
// **Example 4:**
//
// Speaker 0: Good morning
// Speaker 1: Good morning
// Speaker 0: Please introduce yourself
// Speaker 1: My name is John
// {
//   "question_final": false,
//   "question": "My name is John"
// }
//
// **Example 5 (Incomplete Sentence):**
//
// Speaker 0: I will be interviewing Akio for the role of an experienced Android developer. Okay? So let's hear from you, Akio. Could you please, introduce yourself and tell me a little bit about your
// {
//   "question_final": false,
//   "question": "Could you please, introduce yourself and tell me a little bit about your"
// }
//
// **Example 6 (Interruption):**
//
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
// {
//   "question_final": false,
//   "question": "Can you explain the biggest challenge you faced in your previous job and how you"
// }
//
// **Example 7 (Clarification):**
//
// Speaker 0: What skills do you bring to this role that are unique?
// Speaker 1: Well, I have extensive experience in project management
// Speaker 0: Can you elaborate on that?
// {
//   "question_final": true,
//   "question": "Can you elaborate on that?"
// }
//
//
// Follow these examples and ensure the output is consistent with the provided format.
// Only respond with JSON, no markdown or any other format.
//
// Here is the transcription to process:
// {{TRANSCRIPTION}}
// """;
//
// // from claud
// const questionDetectionPrompt3 = """
// Here is a transcription of an interview:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// Your task is to analyze this transcription and determine the following:
//
// 1. What is the last question asked in the transcription, if any? Put your answer inside <last_question> tags. If no question has been asked yet, leave the tags empty.
//
// 2. Is the last question complete and ready to be answered, or is it still being asked? In other words, is the question "final"? Put your determination inside <question_final> tags as either "true" or "false".
//
// Only respond with JSON plain text, and do not include any additional text even markdown is not allowed.
// Output your analysis in the following JSON format:
//
// <format>
// {
//   "question_final": boolean,
//   "question": string
// }
// </format>
//
// Some examples to guide you:
//
// <example>
// Transcription:
// Speaker 0: Good morning
//
// Output:
// {
//   "question_final": false,
//   "question": ""
// }
// </example>
//
// <example>
// Transcription:
// Speaker 0: Good morning
// Speaker 1: Good morning
// Speaker 0: Please introduce yourself
//
// Output:
// {
//   "question_final": true,
//   "question": "Please introduce yourself"
// }
// </example>
//
// <example>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
//
// Output:
// {
//   "question_final": false,
//   "question": "Can you explain the biggest challenge you faced in your previous job and how you"
// }
// </example>
//
// To determine if a question is final:
// - Consider a question final if the speaker has finished their query and it logically makes sense as a complete question.
// - Consider a question not final if there are any indications that more information is forthcoming, such as an incomplete sentence or an interruption.
//
// Now, analyze the provided transcription and output your results in the specified JSON format:
// """;
//
// const questionDetectionPrompt4 = """
// You will be provided with a transcription of an interview, like this:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also be given the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task is to analyze the transcription and determine:
//
// 1. What is the last question asked in the transcription, if any?
// 2. Is this last question complete and ready to be answered (i.e. "final")? Or is the question still in the process of being asked?
//
// To determine if a question is final and complete:
// - Consider a question final if the speaker has finished their query and it logically makes sense as a complete question on its own.
// - Consider a question not final if there are any indications that more information is forthcoming, such as the question ending in an incomplete sentence or the speaker being interrupted mid-question.
//
// If no question has been asked yet in the transcription, treat the "question" field as an empty string.
//
// Compare the last question you found in this transcription to the "question" field from the previous response. If they are the same, set "overrideCurrentQuestion" to false. If they are different, set it to true.
//
// Provide your analysis in the following JSON format:
//
// <format>
// {
//   "overrideCurrentQuestion": boolean,
//   "question_final": boolean,
//   "question": string
// }
// </format>
//
// Here are some examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Good morning
// Speaker 1: Good morning
// Speaker 0: Please introduce yourself
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "Please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
//
// Previous Response:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "Please introduce yourself"
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": false,
//   "question": "Can you explain the biggest challenge you faced in your previous job and how you"
// }
// </example3>
//
// Before providing your final JSON output, first write out your analysis in a <scratchpad>. Explain your reasoning for determining what the last question is and whether it is final or not. Compare it to the previous response to determine if overrideCurrentQuestion should be true or false.
//
// Then, provide your final result in the specified JSON <format>.
//
// Remember, the key aspects are:
// - Identify the last question asked, if any. If none, leave "question" blank.
// - Determine if the last question is final and complete or not.
// - Compare the question to the previous response to set overrideCurrentQuestion.
// - Show your work in <scratchpad> before giving the final JSON output in <format>.
//
// Analyze the provided transcription now.
// """;
//
// const questionDetectionPrompt5 = """
// You will be provided with a transcription of an interview, like this:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also be given the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task is to analyze the transcription and determine:
//
// 1. What is the last question asked in the transcription, if any?
// 2. Is this last question complete and ready to be answered (i.e. "final")? Or is the question still in the process of being asked?
//
// To determine if a question is final and complete:
// - Consider a question final if the speaker has finished their query and it logically makes sense as a complete question on its own.
// - Consider a question not final if there are any indications that more information is forthcoming, such as the question ending in an incomplete sentence or the speaker being interrupted mid-question.
//
// Ensure the identified question is a proper question:
// - Even if the transcription does not have correct punctuation, determine if the statement logically functions as a question that seeks information or clarification.
// - Exclude statements that sound like questions but do not actually seek information or clarification (e.g., rhetorical questions or statements like "So you're looking into cloud storage, right?").
//
// If no question has been asked yet in the transcription, treat the "question" field as an empty string.
//
// Compare the last question you found in this transcription to the "question" field from the previous response. If they are the same, set "overrideCurrentQuestion" to false. If they are different, set it to true.
//
// Provide your analysis in the following JSON format:
//
// <format>
// {
//   "overrideCurrentQuestion": boolean,
//   "question_final": boolean,
//   "question": string
// }
// </format>
//
// Here are some examples:
//
// <example1>
// Transcription:
// Speaker 0: Hello
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Hi
// Speaker 1: Hello
// Speaker 0: Can you tell me about your background?
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "Can you tell me about your background?"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: What are your biggest strengths and
// Speaker 1: I would say my biggest strengths are
//
// Previous Response:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "Can you tell me about your background?"
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": false,
//   "question": "What are your biggest strengths and"
// }
// </example3>
//
// <example4>
// Transcription:
// Speaker 0: We're interested in your expertise. Can you explain
// Speaker 1: Sure, I'd be happy to
//
// Previous Response:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": false,
//   "question": "What are your biggest strengths and"
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": false,
//   "question": "We're interested in your expertise. Can you explain"
// }
// </example4>
//
// <example5>
// Transcription:
// Speaker 0: Can you tell me about your experience with cloud storage Speaker 1: Sure, I have worked on multiple cloud storage solutions...
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "Can you tell me about your experience with cloud storage"
// }
// </example5>
//
// <example6>
// Transcription:
// Speaker 0: Explain your role in the recent project
// Speaker 1: Sure, I was responsible for...
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "Explain your role in the recent project"
// }
// </example6>
//
// Before providing your final JSON output, first write out your analysis in a <scratchpad>. Explain your reasoning for determining what the last question is and whether it is final or not. Compare it to the previous response to determine if overrideCurrentQuestion should be true or false.
//
// Then, provide your final result in the specified JSON <format>.
//
// Remember, the key aspects are:
// - Identify the last proper question asked, if any. If none, leave "question" blank.
// - Determine if the last proper question is final and complete or not.
// - Compare the question to the previous response to set overrideCurrentQuestion.
// - Show your work in <scratchpad> before giving the final JSON output in <format>.
//
// Analyze the provided transcription now.
// """;
//
// // got all 4 correct on gemini-1.5-flash CONFIRMED
// const questionDetectionPrompt6 = """
// You will receive a transcription of an interview:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also receive the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task:
// 1. Identify the last question asked in the transcription, if any.
// 2. Determine if this question is complete (final) or still in progress.
//
// Consider a question final if it logically makes sense as a complete question. If it seems incomplete or interrupted, it is not final.
//
// A proper question should end with a question mark or have a clear interrogative structure seeking a response. Exclude rhetorical questions or statements.
//
// Normalize questions to lowercase and trim whitespace.
//
// If no question is present, the "question" field should be an empty string.
//
// Compare the last question found in the transcription to the "question" field from the previous response. If they match, set "overrideCurrentQuestion" to false; otherwise, set it to true.
//
// Provide your analysis in this JSON format:
//
// <format>
// {
//   "overrideCurrentQuestion": boolean,
//   "question_final": boolean,
//   "question": string
// }
// </format>
//
// Examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Please introduce yourself
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
//
// Previous Response:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "please introduce yourself"
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": false,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you"
// }
// </example3>
//
// Before providing your final JSON output, write your analysis in a <scratchpad>. Explain your reasoning for determining the last question and its completeness. Compare it to the previous response to set overrideCurrentQuestion.
//
// Then, provide your final result in the specified JSON format.
//
// Remember:
// - Identify the last proper question, if any. If none, leave "question" blank.
// - Determine if the last question is final.
// - Compare the question to the previous response to set overrideCurrentQuestion.
// - Normalize questions to avoid duplicates without merging distinct questions.
// - Show your work in <scratchpad> before giving the final JSON output in <format>.
//
// Analyze the provided transcription now.
// """;
//
// // got all 4 correct on gemini-1.5-flash CONFIRMED
// // removed [Normalize questions to lowercase and trim whitespace.]
// const questionDetectionPrompt6_1 = """
// You will receive a transcription of an interview:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also receive the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task:
// 1. Identify the last question asked in the transcription, if any.
// 2. Determine if this question is complete (final) or still in progress.
//
// Consider a question final if it logically makes sense as a complete question. If it seems incomplete or interrupted, it is not final.
//
// A proper question should end with a question mark or have a clear interrogative structure seeking a response. Exclude rhetorical questions or statements.
//
// If no question is present, the "question" field should be an empty string.
//
// Compare the last question found in the transcription to the "question" field from the previous response. If they match, set "overrideCurrentQuestion" to false; otherwise, set it to true.
//
// Provide your analysis in this JSON format:
//
// <format>
// {
//   "overrideCurrentQuestion": boolean,
//   "question_final": boolean,
//   "question": string
// }
// </format>
//
// Examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Please introduce yourself
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
//
// Previous Response:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "please introduce yourself"
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": false,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you"
// }
// </example3>
//
// Before providing your final JSON output, write your analysis in a <scratchpad>. Explain your reasoning for determining the last question and its completeness. Compare it to the previous response to set overrideCurrentQuestion.
//
// Then, provide your final result in the specified JSON format.
//
// Remember:
// - Identify the last proper question, if any. If none, leave "question" blank.
// - Determine if the last question is final.
// - Compare the question to the previous response to set overrideCurrentQuestion.
// - Normalize questions to avoid duplicates without merging distinct questions.
// - Show your work in <scratchpad> before giving the final JSON output in <format>.
//
// Analyze the provided transcription now.
// """;
//
// // got all 4 correct on gemini-1.5-flash CONFIRMED
// // removed [Normalize questions to lowercase and trim whitespace.]
// // replace overrideCurrentQuestion with is_duplicate_question
// const questionDetectionPrompt6_2 = """
// You will receive a transcription of an interview:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also receive the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task:
// 1. Identify the last question asked in the transcription, if any.
// 2. Determine if this question is complete (final) or still in progress.
//
// Consider a question final if it logically makes sense as a complete question. If it seems incomplete or interrupted, it is not final.
//
// A proper question should end with a question mark or have a clear interrogative structure seeking a response. Exclude rhetorical questions or statements.
//
// If no question is present, the "question" field should be an empty string.
//
// Compare the last question found in the transcription to the "question" field from the previous response. If they match, set "is_duplicate_question" to true; otherwise, set it to false.
//
// Provide your analysis in this JSON format:
//
// <format>
// {
//   "is_duplicate_question": boolean,
//   "question_final": boolean,
//   "question": string
// }
// </format>
//
// Examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "is_duplicate_question": false,
//   "question_final": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Please introduce yourself
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "is_duplicate_question": false,
//   "question_final": true,
//   "question": "please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "question_final": true,
//   "question": "please introduce yourself"
// }
//
// Output:
// {
//   "is_duplicate_question": false,
//   "question_final": false,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you"
// }
// </example3>
//
// Before providing your final JSON output, write your analysis in a <scratchpad>. Explain your reasoning for determining the last question and its completeness. Compare it to the previous response to set is_duplicate_question.
//
// Then, provide your final result in the specified JSON format.
//
// Remember:
// - Identify the last proper question, if any. If none, leave "question" blank.
// - Determine if the last question is final.
// - Compare the question to the previous response to set is_duplicate_question.
// - Normalize questions to avoid duplicates without merging distinct questions.
// - Show your work in <scratchpad> before giving the final JSON output in <format>.
//
// Analyze the provided transcription now.
// """;
//
// // got all 4 correct on gemini-1.5-flash x
// // removed [Normalize questions to lowercase and trim whitespace.]
// // replace overrideCurrentQuestion with is_duplicate_question
// // replace question_final with is_complete_question.
// const questionDetectionPrompt6_3 = """
// You will receive a transcription of an interview:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also receive the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task:
// 1. Identify the last question asked in the transcription, if any.
// 2. Determine if this question is complete (final) or still in progress.
//
// Consider a question complete if it logically makes sense as a complete question. If it seems incomplete or interrupted, it is not complete.
//
// A proper question should end with a question mark or have a clear interrogative structure seeking a response. Exclude rhetorical questions or statements.
//
// If no question is present, the "question" field should be an empty string.
//
// Compare the last question found in the transcription to the "question" field from the previous response. If they match, set "is_duplicate_question" to true; otherwise, set it to false.
//
// Provide your analysis in this JSON format:
//
// <format>
// {
//   "is_duplicate_question": boolean,
//   "is_complete_question": boolean,
//   "question": string
// }
// </format>
//
// Examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": false,
//   "question": ""
// }
//
// Output:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Please introduce yourself
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": false,
//   "question": ""
// }
//
// Output:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": true,
//   "question": "please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": true,
//   "question": "please introduce yourself"
// }
//
// Output:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": false,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you"
// }
// </example3>
//
// Before providing your final JSON output, write your analysis in a <scratchpad>. Explain your reasoning for determining the last question and its completeness. Compare it to the previous response to set is_duplicate_question.
//
// Then, provide your final result in the specified JSON format.
//
// Remember:
// - Identify the last proper question, if any. If none, leave "question" blank.
// - Determine if the last question is complete.
// - Compare the question to the previous response to set is_duplicate_question.
// - Normalize questions to avoid duplicates without merging distinct questions.
// - Show your work in <scratchpad> before giving the final JSON output in <format>.
//
// Analyze the provided transcription now.
// """;
//
// // got all 4 correct on gemini-1.5-flash x
// // failed even on getLastLines(text, 4);
// // removed [Normalize questions to lowercase and trim whitespace.]
// // replace overrideCurrentQuestion with is_duplicate_question
// // replace question_final with is_complete_question.
// // added by the interviewer
// const questionDetectionPrompt6_4 = """
// You will receive a transcription of an interview:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also receive the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task:
// 1. Identify the last question asked by the interviewer in the transcription, if any.
// 2. Determine if this question is complete (final) or still in progress.
//
// Consider a question complete if it logically makes sense as a complete question. If it seems incomplete or interrupted, it is not complete.
//
// A proper question should end with a question mark or have a clear interrogative structure seeking a response. Exclude rhetorical questions or statements.
//
// If no question is present, the "question" field should be an empty string.
//
// Compare the last question found in the transcription to the "question" field from the previous response. If they match, set "is_duplicate_question" to true; otherwise, set it to false.
//
// Provide your analysis in this JSON format:
//
// <format>
// {
//   "is_duplicate_question": boolean,
//   "is_complete_question": boolean,
//   "question": string
// }
// </format>
//
// Examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": false,
//   "question": ""
// }
//
// Output:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Please introduce yourself
// Speaker 1: Sure, my name is John Doe
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": false,
//   "question": ""
// }
//
// Output:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": true,
//   "question": "please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you overcame it?
// Speaker 1: Sure, the biggest challenge was
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": true,
//   "question": "please introduce yourself"
// }
//
// Output:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": true,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you overcame it?"
// }
// </example3>
//
// Before providing your final JSON output, write your analysis in a <scratchpad>. Explain your reasoning for determining the last question and its completeness. Compare it to the previous response to set is_duplicate_question.
//
// Then, provide your final result in the specified JSON format.
//
// Remember:
// - Identify the last proper question asked by the interviewer, if any. If none, leave "question" blank.
// - Determine if the last question is complete.
// - Compare the question to the previous response to set is_duplicate_question.
// - Normalize questions to avoid duplicates without merging distinct questions.
// - Show your work in <scratchpad> before giving the final JSON output in <format>.
//
// Analyze the provided transcription now.
// """;
//
// // got all 4 correct on gemini-1.5-flash X
// // pass with 4 lines
// // removed [Normalize questions to lowercase and trim whitespace.]
// // replace overrideCurrentQuestion with is_duplicate_question
// // replace question_final with is_complete_question.
// // added by the interviewer
// // pass the history of all old questions
// const questionDetectionPrompt6_4_1 = """
// You will receive a transcription of an interview:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also receive the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Additionally, you will receive a list of all previously extracted questions:
//
// <previous_questions>
// {{PREVIOUS_QUESTIONS}}
// </previous_questions>
//
// Your task:
// 1. Identify the last question asked by the interviewer in the transcription, if any.
// 2. Determine if this question is complete (final) or still in progress.
//
// Consider a question complete if it logically makes sense as a complete question. If it seems incomplete or interrupted, it is not complete.
//
// A proper question should end with a question mark or have a clear interrogative structure seeking a response. Exclude rhetorical questions or statements.
//
// If no question is present, the "question" field should be an empty string.
//
// Compare the last question found in the transcription to the "question" field from the previous response and the list of previous questions. If it matches any of them, set "is_duplicate_question" to true; otherwise, set it to false.
//
// Provide your analysis in this JSON format:
//
// <format>
// {
//   "is_duplicate_question": boolean,
//   "is_complete_question": boolean,
//   "question": string
// }
// </format>
//
// Examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": false,
//   "question": ""
// }
//
// Previous Questions:
// []
//
// Output:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Please introduce yourself
// Speaker 1: Sure, my name is John Doe
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": false,
//   "question": ""
// }
//
// Previous Questions:
// []
//
// Output:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": true,
//   "question": "please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you overcame it?
// Speaker 1: Sure, the biggest challenge was
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": true,
//   "question": "please introduce yourself"
// }
//
// Previous Questions:
// ["please introduce yourself"]
//
// Output:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": true,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you overcame it?"
// }
// </example3>
//
// <example4>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you overcame it?
// Speaker 1: Sure, the biggest challenge was
//
// Previous Response:
// {
//   "is_duplicate_question": false,
//   "is_complete_question": true,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you overcame it?"
// }
//
// Previous Questions:
// ["please introduce yourself", "can you explain the biggest challenge you faced in your previous job and how you overcame it?"]
//
// Output:
// {
//   "is_duplicate_question": true,
//   "is_complete_question": true,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you overcame it?"
// }
// </example4>
//
// Before providing your final JSON output, write your analysis in a <scratchpad>. Explain your reasoning for determining the last question and its completeness. Compare it to the previous response and the list of previous questions to set is_duplicate_question.
//
// Then, provide your final result in the specified JSON format.
//
// Remember:
// - Identify the last proper question asked by the interviewer, if any. If none, leave "question" blank.
// - Determine if the last question is complete.
// - Compare the question to the previous response and the list of previous questions to set is_duplicate_question.
// - Normalize questions to avoid duplicates without merging distinct questions.
// - Show your work in <scratchpad> before giving the final JSON output in <format>.
//
// Analyze the provided transcription now.
// """;
//
// // got all 4 correct on gemini-1.5-flash x,
// // passed when lastMinuteDuration: 400
// // removed [Normalize questions to lowercase and trim whitespace.]
// // replace overrideCurrentQuestion with is_duplicate_question
// // replace question_final with is_complete_question.
// // added by the interviewer
// // generated gemini
// const questionDetectionPrompt6_5 = """
// You are a helpful and precise AI assistant tasked with analyzing interview transcripts.
//
// You will receive a transcription of an interview:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also receive the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task is to analyze the transcription and identify the last question asked by the interviewer.
//
// **Here's a breakdown of your task:**
//
// 1. **Identify the last question:**
//     * Look for the last utterance by the interviewer that has a clear interrogative structure, ending with a question mark or seeking a response.
//     * Exclude rhetorical questions or statements that are not actual questions.
//     * If no question is found, return an empty string for the `question` field.
// 2. **Determine if the question is complete:**
//     * A complete question logically makes sense as a standalone question.
//     * If the question seems incomplete or interrupted, mark it as incomplete (`"is_complete_question": false`).
// 3. **Check for duplicate questions:**
//     * Compare the last question you identified to the `"question"` field from the previous response.
//     * If they are the same, set `"is_duplicate_question": true`, otherwise set it to `false`.
//
// **Important Considerations:**
//
// * **Normalize questions:** Normalize the question to avoid duplicates due to minor variations in phrasing. For example, "Can you tell me about your skills?" and "Tell me about your skills?" should be considered the same question. However, be careful not to merge distinct questions that are semantically different.
// * **Context is key:** Use the context of the conversation to determine if an utterance is a genuine question and if it's complete.
//
// **Output Format:**
//
// Provide your analysis in this JSON format:
//
// <format>
// {
//   "is_duplicate_question": boolean,
//   "is_complete_question": boolean,
//   "question": string
// }
// </format>
//
// **Examples:**
//
// *See original prompt for examples*
//
// **Before providing your final JSON output, write your analysis in a <scratchpad>. Explain your reasoning for determining the last question, its completeness, and whether it is a duplicate.
//
// Then, provide your final result in the specified JSON format.
// """;
//
// // #6 but shorten by gpt4
// const questionDetectionPrompt7 = """
// You will be provided with a transcription of an interview, like this:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also be given the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task is to analyze the transcription and determine:
// 1. The last question asked in the transcription, if any.
// 2. If this last question is complete and ready to be answered (final) or still in the process of being asked.
//
// Criteria for a final question:
// - A question is final if the speaker has finished their query and it makes sense as a complete question.
// - A question is not final if more information seems forthcoming or the speaker was interrupted.
//
// Ensure the question is proper:
// - A proper question should end with a question mark or have a clear interrogative structure.
// - Exclude rhetorical questions or statements posing as questions.
//
// Normalize questions by converting to lowercase and trimming whitespace.
//
// If no question has been asked yet, the "question" field should be an empty string.
//
// Compare the last question found in the transcription to the "question" field from the previous response. If they are the same, set "overrideCurrentQuestion" to false; otherwise, set it to true.
//
// Provide your analysis in JSON format:
//
// <format>
// {
//   "overrideCurrentQuestion": boolean,
//   "question_final": boolean,
//   "question": string
// }
// </format>
//
// Example:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
// Speaker 1: Good morning
// Speaker 0: Please introduce yourself
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "please introduce yourself"
// }
// </example1>
//
// Before providing your final JSON output, write out your analysis in a <scratchpad>. Explain your reasoning and compare it to the previous response to determine if overrideCurrentQuestion should be true or false.
//
// Analyze the provided transcription now.
// """;
//
// // #6 but shorten by opus
// const questionDetectionPrompt8 = """
// You will be provided with a transcription of an interview and the AI's response to the previous transcription, if any, in the following format:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task is to:
// 1. Identify the last question asked in the transcription, if any. If none, leave "question" blank.
// 2. Determine if the last question is complete and ready to be answered ("final") or still in the process of being asked.
//
// Consider a question final if:
// - The speaker has finished their query and it logically makes sense as a complete question on its own.
// - It ends with a question mark or has a clear interrogative structure and elicits a response.
//
// Consider a question not final if:
// - There are indications that more information is forthcoming (e.g., incomplete sentence, speaker interrupted).
// - It's a statement that sounds like a question but doesn't seek information or clarification (e.g., rhetorical questions).
//
// Normalize questions by converting to lowercase and trimming whitespace to avoid duplicates. Focus on essential content to distinguish between similar questions.
//
// Compare the last question to the "question" field from the previous response. Set "overrideCurrentQuestion" to false if they are the same, true if different.
//
// Provide your analysis in the following JSON format:
//
// <format>
// {
//   "overrideCurrentQuestion": boolean,
//   "question_final": boolean,
//   "question": string
// }
// </format>
//
// Before providing the final JSON output, write out your analysis in a <scratchpad>. Explain your reasoning for determining the last question and its finality. Compare it to the previous response to determine overrideCurrentQuestion.
//
// Then, provide your final result in the specified JSON <format>.
//
// Analyze the provided transcription now.
// """;
//
// // #6 but shorten by prompt generator
// // added
// const questionDetectionPrompt9 = """
// Here is the transcription of the interview:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// And here is the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task is to analyze the transcription and identify the last question asked, if any. If there is no question present, leave the "question" field blank in your final output.
//
// To determine if a question is present, look for:
// - Sentences ending in a question mark
// - Phrases with an interrogative structure that elicit a response
//
// If a question is present, next determine if it is complete and ready to be answered ("final") or still in the process of being asked.
//
// Consider a question final if:
// - The speaker has finished their query and it logically makes sense as a complete question on its own.
// - It ends with a question mark or has a clear interrogative structure and elicits a response.
//
// Consider a question not final if:
// - There are indications that more information is forthcoming (e.g., incomplete sentence, speaker interrupted).
// - It's a statement that sounds like a question but doesn't seek information or clarification (e.g., rhetorical questions).
//
// If a question is present, normalize it by converting to lowercase and trimming any whitespace from the beginning and end. Focus on capturing the essential content of the question to distinguish between similar but distinct questions.
//
// Compare the last question you identified to the "question" field from the <previous_response>. Set the "overrideCurrentQuestion" field to false if they are the same question, or true if they are different.
//
// Provide your analysis in the following JSON format:
//
// <format>
// {
//   "overrideCurrentQuestion": boolean,
//   "question_final": boolean,
//   "question": string
// }
// </format>
//
// Before outputting the final JSON, first write out your analysis in a <scratchpad>. Explain your reasoning for determining the last question asked and whether it is final or not. Compare it to the previous response to determine how to set overrideCurrentQuestion.
//
// After the <scratchpad>, provide your final result in the specified JSON <format>.
//
// Analyze the provided transcription now.
// """;
//
// // #6 but but improved by chatgpt
// const questionDetectionPrompt10 = """
// You will be provided with a transcription of an interview, like this:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also be given the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task is to analyze the transcription and determine:
//
// 1. What is the last question asked in the transcription, if any?
// 2. Is this last question complete and ready to be answered (i.e. "final")? Or is the question still in the process of being asked?
//
// To determine if a question is final and complete:
// - Consider a question final if the speaker has finished their query and it logically makes sense as a complete question on its own.
// - Consider a question not final if there are any indications that more information is forthcoming, such as the question ending in an incomplete sentence or the speaker being interrupted mid-question.
//
// Ensure the identified question is a proper question:
// - A proper question should end with a question mark or have a clear interrogative structure and elicit a response.
// - Exclude statements that sound like questions but do not actually seek information or clarification (e.g., rhetorical questions or statements like "So we want to get started, all right?").
// -
//
// Focus on the essential content to distinguish between similar questions.
//
// If no question has been asked yet in the transcription, treat the "question" field as an empty string.
//
// Compare the last question you found in this transcription to the "question" field from the previous response. If they are the same, set "overrideCurrentQuestion" to false. If they are different, set it to true.
//
// Provide your analysis in the following JSON format:
//
// <format>
// {
//   "overrideCurrentQuestion": boolean,
//   "question_final": boolean,
//   "question": string
// }
// </format>
//
// Here are some examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Good morning
// Speaker 1: Good morning
// Speaker 0: Please introduce yourself
//
// Previous Response:
// {
//   "overrideCurrentQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
//
// Previous Response:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "please introduce yourself"
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": false,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you"
// }
// </example3>
//
// <example4>
// Transcription:
// Speaker 0: What are your long-term goals with our company?
// Speaker 1: I see myself growing into a leadership role.
//
// Previous Response:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": false,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you"
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "what are your long-term goals with our company?"
// }
// </example4>
//
// <example5>
// Transcription:
// Speaker 0: Could you please tell me a bit about an interesting project that you have been working on?
// Speaker 1: Sure, I recently worked on a
//
// Previous Response:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "what are your long-term goals with our company?"
// }
//
// Output:
// {
//   "overrideCurrentQuestion": true,
//   "question_final": true,
//   "question": "could you please tell me a bit about an interesting project that you have been working on?"
// }
// </example5>
//
// Before providing your final JSON output, first write out your analysis in a <scratchpad>. Explain your reasoning for determining what the last question is and whether it is final or not. Compare it to the previous response to determine if overrideCurrentQuestion should be true or false.
//
// Then, provide your final result in the specified JSON <format>.
//
// Remember, the key aspects are:
// - Identify the last proper question asked, if any. If none, leave "question" blank.
// - Determine if the last proper question is final and complete or not.
// - Compare the question to the previous response to set overrideCurrentQuestion.
// - Normalize questions to avoid duplicates without merging distinct questions.
// - Show your work in <scratchpad> before giving the final JSON output in <format>.
//
// Analyze the provided transcription now.
// """;
//
// // #6 but but improved by gpt4 with isNewQuestion
// const questionDetectionPrompt11 = """
// You will receive a transcription of an interview:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
//
// You will also receive the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task:
// 1. Identify the last question asked in the transcription, if any.
// 2. Determine if this question is complete (final) or still in progress.
//
// Consider a question final if it logically makes sense as a complete question. If it seems incomplete or interrupted, it is not final.
//
// A proper question should end with a question mark or have a clear interrogative structure seeking a response. Exclude rhetorical questions or statements.
//
// Normalize questions to lowercase and trim whitespace.
//
// If no question is present, the "question" field should be an empty string.
//
// Compare the last question found in the transcription to the "question" field from the previous response. If they match, set "isNewQuestion" to false; otherwise, set it to true.
//
// Provide your analysis in this JSON format:
//
// <format>
// {
//   "isNewQuestion": boolean,
//   "question_final": boolean,
//   "question": string
// }
// </format>
//
// Examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
//
// Previous Response:
// {
//   "isNewQuestion": false,
//   "question_final": false,
//   "question": ""
// }
//
// Output:
// {
//   "isNewQuestion": false,
//   "question_final": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Please introduce yourself
// Previous Response:
// {
// "isNewQuestion": false,
// "question_final": false,
// "question": ""
// }
//
// Output:
// {
// "isNewQuestion": true,
// "question_final": true,
// "question": "please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
// Previous Response:
// {
// "isNewQuestion": true,
// "question_final": true,
// "question": "please introduce yourself"
// }
//
// Output:
// {
// "isNewQuestion": true,
// "question_final": false,
// "question": "can you explain the biggest challenge you faced in your previous job and how you"
// }
// </example3>
//
// Before providing your final JSON output, write your analysis in a <scratchpad>. Explain your reasoning for determining the last question and its completeness. Compare it to the previous response to set isNewQuestion.
//
// Then, provide your final result in the specified JSON format.
//
// Remember:
//
// Identify the last proper question, if any. If none, leave "question" blank.
// Determine if the last question is final.
// Compare the question to the previous response to set isNewQuestion.
// Normalize questions to avoid duplicates without merging distinct questions.
// Show your work in <scratchpad> before giving the final JSON output in <format>.
// Analyze the provided transcription now.
// """;
//
// // #6 but but improved by gpt4 with is_new_question and gpt4 prompt generator
// const questionDetectionPrompt12 = """
// You will receive a transcription of an interview:
//
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
// You will also receive the AI's response to the previous transcription, if any:
//
// <previous_response>
// {{PREVIOUS_RESPONSE}}
// </previous_response>
//
// Your task:
//
// Identify the last question asked in the transcription, if any.
// Determine if this question is complete (final) or still in progress.
// Consider a question final if it logically makes sense as a complete question. If it seems incomplete or interrupted, it is not final.
//
// A proper question should end with a question mark or have a clear interrogative structure seeking a response. Exclude rhetorical questions or statements.
//
// Normalize questions to lowercase, trim whitespace, and ensure proper punctuation.
//
// If no question is present, the "question" field should be an empty string.
//
// Compare the last question found in the transcription to the "question" field from the previous response. If they match, set "is_new_question" to false; otherwise, set it to true.
//
// Provide your analysis in this JSON format:
//
// <format>
// {
//   "is_new_question": boolean,
//   "question_final": boolean,
//   "question": string
// }
// </format>
// Examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
// Previous Response:
// {
// "is_new_question": false,
// "question_final": false,
// "question": ""
// }
//
// Output:
// {
// "is_new_question": false,
// "question_final": false,
// "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Please introduce yourself
// Previous Response:
// {
// "is_new_question": false,
// "question_final": false,
// "question": ""
// }
//
// Output:
// {
// "is_new_question": true,
// "question_final": true,
// "question": "please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
// Previous Response:
// {
// "is_new_question": true,
// "question_final": true,
// "question": "please introduce yourself"
// }
//
// Output:
// {
// "is_new_question": true,
// "question_final": false,
// "question": "can you explain the biggest challenge you faced in your previous job and how you"
// }
// </example3>
//
// Before providing your final JSON output, write your analysis in a <scratchpad>. Explain your reasoning for determining the last question and its completeness. Compare it to the previous response to set is_new_question.
//
// Then, provide your final result in the specified JSON format.
//
// Remember:
//
// Identify the last proper question, if any. If none, leave "question" blank.
// Determine if the last question is final.
// Compare the question to the previous response to set is_new_question.
// Normalize questions to avoid duplicates without merging distinct questions.
// Show your work in <scratchpad> before giving the final JSON output in <format>.
// Analyze the provided transcription now.
// """;
//
// // system prompt
// /*
// gpt-3.5-turbo 0125
// Questions: [
//      "so let's hear from you, akhil",
//      "could you please introduce yourself and tell me a little bit about your professional experience",
//      "could you please tell me a bit something about any interesting project that you have been worked on",
//      "what do you think is or are the best practice to avoid the memory leaks on android?",
//      "what's the broadcast receiver?"
// ]
//  */
// const questionDetectionPromptSystem1 = """
// You will receive a transcription of an interview.
// The interview is about the following topic:
// {{INTERVIEW_TOPIC}}
//
// You will also receive the latest asked question, to avoid duplicates:
//
// <previous_question>
// {{PREVIOUS_QUESTION}}
// </previous_question>
//
// Your task:
// 1. Identify the last question asked in the transcription, if any.
// 2. Determine if this question is complete (final) or still in progress.
//
// Consider a question final if it logically makes sense as a complete question that could be asked by the interviewer in {{INTERVIEW_TOPIC}} interview.
// If it seems incomplete or interrupted, it is not final.
//
// A proper question should end with a question mark or have a clear interrogative structure seeking a response. Exclude rhetorical questions or statements.
//
// Normalize questions to lowercase and trim whitespace.
//
// If no question is present, the "question" field should be an empty string.
//
// Compare the last question found in the transcription to the previous question.
// If they match, don't return the question to avoid duplicates.
// The current question and the previous question shouldn't be an exact match to be considered as a duplicate.
//
// You should respond in JSON in the following format:
// {
//   "question_final": boolean,
//   "question": string
// }
//
// Examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
//
// Output:
// {
//   "question_final": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Please introduce yourself
//
// Output:
// {
//   "question_final": true,
//   "question": "please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
//
// Output:
// {
//   "question_final": false,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you"
// }
// </example3>
// """;
//
// // same as 1, detects if the question requires personal info about the interviewee.
// const questionDetectionPromptSystem2 = """
// You will receive a transcription of an interview.
// The interview is about the following topic:
// {{INTERVIEW_TOPIC}}
//
// You will also receive the latest asked question, to avoid duplicates:
//
// <previous_question>
// {{PREVIOUS_QUESTION}}
// </previous_question>
//
// Your task:
// 1. Identify the last question asked in the transcription, if any.
// 2. Determine if this question is complete (final) or still in progress.
// 3. Determine if the question requires personal information about the interviewee.
//
// Consider a question final if it logically makes sense as a complete question that could be asked by the interviewer in {{INTERVIEW_TOPIC}} interview.
// If it seems incomplete or interrupted, it is not final.
//
// A proper question should end with a question mark or have a clear interrogative structure seeking a response. Exclude rhetorical questions or statements.
//
// Normalize questions to lowercase and trim whitespace.
//
// If no question is present, the "question" field should be an empty string.
//
// Compare the last question found in the transcription to the previous question.
// If they match, don't return the question to avoid duplicates.
// The current question and the previous question shouldn't be an exact match to be considered as a duplicate.
//
// You should respond in JSON in the following format:
// {
//   "question_final": boolean,
//   "requires_personal_info": boolean,
//   "question": string
// }
//
// Examples:
//
// <example1>
// Transcription:
// Speaker 0: Good morning
//
// Output:
// {
//   "question_final": false,
//   "requires_personal_info": false,
//   "question": ""
// }
// </example1>
//
// <example2>
// Transcription:
// Speaker 0: Please introduce yourself
//
// Output:
// {
//   "question_final": true,
//   "requires_personal_info": true,
//   "question": "please introduce yourself"
// }
// </example2>
//
// <example3>
// Transcription:
// Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
// Speaker 1: Sure, the biggest challenge was
//
// Output:
// {
//   "question_final": false,
//   "requires_personal_info": false,
//   "question": "can you explain the biggest challenge you faced in your previous job and how you"
// }
// </example3>
// """;
// // endregion
//
// // region question answering
// const questionAnsweringPrompt1 = """
// You will be given an interview transcript, which might contain some transcript errors. Your task is to:
//
// 1. Determine the last question asked in the interview.
// 2. Generate a concise answer to that question.
// 3. Respond in JSON format.
// 4. Please generate an answer, do not extract the answer from the transcript.
//
// You should respond in JSON in the following format:
//
// {
//   "question": string,
//   "answer": string
// }
//
// The interview is about the following topic:
// {{INTERVIEW_TOPIC}}
//
// Here is the transcript:
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
// """;
//
// const questionAnsweringPrompt1Format = """
// You will be given an interview transcript, which might contain some transcript errors. Your task is to:
//
// 1. Determine the last question asked in the interview.
// 2. Generate a concise answer to that question.
// 3. Respond in JSON format.
// 4. Format the answer in the style of bullet points if appropriate.
// 5. Please generate an answer, do not extract the answer from the transcript.
//
// You should respond in JSON in the following format:
//
// {
//   "question": string,
//   "answer": string
// }
//
// The interview is about the following topic:
// {{INTERVIEW_TOPIC}}
//
// Here is the transcript:
// <transcription>
// {{TRANSCRIPTION}}
// </transcription>
// """;
//
// const questionAnsweringPromptSystem1 = """
// You will be given an interview transcript, which might contain some transcript errors.
//
// Your task is to:
// - Determine the last question asked in the interview.
// - Generate a concise answer to that question.
// - Respond in JSON format.
// - The answer should not contain markdown.
// - Generate an answer, do not extract the answer from the transcript.
//
// - The answer should be:
// {{ANSWER_LENGTH}}
//
// - The answer should be in the following style:
// {{ANSWER_STYLE}}
//
// - If the question is behavioral, provide the answer in the following structure:
// {{BEHAVIORAL_ANSWER_STRUCTURE}}
//
// You should respond in JSON in the following format:
//
// {
//   "question": string,
//   "answer": string
// }
//
// The interview is about the following topic:
// {{INTERVIEW_TOPIC}}
// """;
// // endregion

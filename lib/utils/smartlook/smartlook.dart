import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smartlook/flutter_smartlook.dart';

import '../platform_utils.dart';
import 'smartlook_web.dart' if (dart.library.io) 'smartlook_mobile.dart'
    as smartlook_platform;

final enableSmartlook = kReleaseMode && !kIsNativeDesktop;

void initSmartlook() {
  if (!enableSmartlook) return;

  if (kIsWeb) {
    // Web platform: Smartlook is already initialized in index.html
    // You can add additional configuration here if needed
  } else {
    // Mobile platforms: Use Flutter Smartlook SDK
    final smartlook = Smartlook.instance;

    smartlook.start();
    smartlook.preferences
        .setProjectKey('393451d814f5c54819aa75b6a2d6aa70f96023be');

    Smartlook.instance.sensitivity.changeWidgetClassSensitivity(
      classType: TextField,
      isSensitive: false,
    );
    Smartlook.instance.sensitivity.changeWidgetClassSensitivity(
      classType: TextFormField,
      isSensitive: false,
    );

    Smartlook.instance.sensitivity.changeNativeClassSensitivity([
      SensitivityTuple(
        classType: SmartlookNativeClassSensitivity.WebView,
        isSensitive: false,
      ),
      SensitivityTuple(
        classType: SmartlookNativeClassSensitivity.WKWebView,
        isSensitive: false,
      ),
    ]);
  }
}

void setupSmartlookUser() {
  if (!enableSmartlook) return;

  final currentUser = FirebaseAuth.instance.currentUser;
  final uid = currentUser?.uid;
  final email = currentUser?.email;

  smartlook_platform.setSmartlookUser(uid, email);
}

class AppSmartlookRecordingWidget extends StatelessWidget {
  final Widget child;

  const AppSmartlookRecordingWidget({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    if (!enableSmartlook || kIsWeb) {
      return child;
    } else {
      return SmartlookRecordingWidget(
        child: child,
      );
    }
  }
}

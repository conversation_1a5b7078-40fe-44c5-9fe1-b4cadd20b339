import 'dart:js_interop';

import 'package:flutter_toolbox/flutter_toolbox.dart';

@JS('smartlook')
external void _jsSmartlook(String method, [JSAny? arg1, JSAny? arg2]);

void setSmartlookUser(String? uid, String? email) {
  d('user: uid=$uid, email=$email');

  // Identify user
  if (uid != null) {
    _jsSmartlook('identify', uid.toJS);
  }

  // Set user properties
  if (email != null) {
    _jsSmartlook('properties', {'email': email}.jsify());
  }

  // Track user properties
  _jsSmartlook(
    'track',
    'user_properties'.toJS,
    {
      'user_id': uid ?? 'null user_id',
      'email': email ?? 'null email',
    }.jsify(),
  );
}

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../api/management/user_profile_manager.dart';
// Conditional import for web
import 'events_web.dart' if (dart.library.io) 'events_mobile.dart' as platform;

void trackEvent(String event, [String? value]) {
  // d('Event: $event, Value: $value');

  // Use platform-specific implementation
  platform.trackSmartlookEvent(event, value);

  final userProfile = userProfileManager.userProfile;
  final email = userProfile?.email;
  final userId = userProfile?.uid;

  // Build user data map
  final userData = <String, Object>{
    if (email != null) 'email': email,
    if (userId != null) 'userId': userId,
  };

  // Sentry breadcrumb logging (works for both web and mobile)
  Sentry.addBreadcrumb(Breadcrumb(message: event, data: {
    if (value != null) 'value': value,
    ...userData,
  }));

  // Firebase Analytics (works for both web and mobile)
  FirebaseAnalytics.instance.logEvent(name: event, parameters: {
    if (value != null) 'value': value,
    ...userData,
  });
}

void logError(dynamic error, [StackTrace? stackTrace]) {
  identifyUser();

  if (error is String) {
    // Firebase Crashlytics (works for both web and mobile)
    if (!kIsWeb) {
      FirebaseCrashlytics.instance.log(error);
    }

    // Sentry logging (works for both web and mobile)
    Sentry.captureMessage(error, level: SentryLevel.error);
  } else {
    d2('Exception: $error');

    final trace = stackTrace ?? StackTrace.current;

    // Crashlytics with stack trace when available
    if (!kIsWeb) {
      FirebaseCrashlytics.instance.recordError(error, trace);
    }

    // Sentry with stack trace when available
    Sentry.captureException(error, stackTrace: trace);
  }
}

void identifyUser() {
  final userProfile = userProfileManager.userProfile;
  final email = userProfile?.email;
  final userId = userProfile?.uid;

  // Set user context for Sentry
  Sentry.configureScope((scope) {
    if (email != null || userId != null) {
      scope.setUser(SentryUser(
        id: userId,
        email: email,
      ));
    }
  });

  // Set user context for Firebase Crashlytics
  if (!kIsWeb && (email != null || userId != null)) {
    if (userId != null) {
      FirebaseCrashlytics.instance.setUserIdentifier(userId);
    }
    if (email != null) {
      FirebaseCrashlytics.instance.setCustomKey('email', email);
    }
  }
}

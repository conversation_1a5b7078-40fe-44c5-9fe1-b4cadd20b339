import 'package:firebase_auth/firebase_auth.dart';
import 'package:interview_hammer/utils/platform_utils.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

final enableOneSignal = kIsNativeMobile;

void initOneSignal() {
  if (!enableOneSignal) return;

  OneSignal.initialize("************************************");
}

void setupOneSignalUser() {
  if (!enableOneSignal) return;

  OneSignal.Notifications.requestPermission(true);

  var currentUser = FirebaseAuth.instance.currentUser;
  final uid = currentUser?.uid;
  if (uid != null) {
    OneSignal.login(uid);
  }

  final email = currentUser?.email;
  if (email != null) {
    OneSignal.User.addEmail(email);
  }
}

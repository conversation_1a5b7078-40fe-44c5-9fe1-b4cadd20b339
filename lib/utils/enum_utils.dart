// Converts a string to an enum value, with a fallback to a default value.
// Case-sensitive and expects the exact enum case name.
// Example: _enumFromString(BehavioralAnswerStructure.values, 'STAR', BehavioralAnswerStructure.CAR)
//          returns BehavioralAnswerStructure.STAR
T enumFromString<T extends Enum>(
    List<T> values, String? value, T defaultValue) {
  if (value == null) return defaultValue;
  return values.firstWhere(
    (e) => e.toString().split('.').last == value,
    orElse: () => defaultValue,
  );
}

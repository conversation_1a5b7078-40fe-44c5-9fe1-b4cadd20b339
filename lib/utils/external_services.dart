import 'package:flutter/foundation.dart';
import 'package:interview_hammer/api/management/management_api.dart';
import 'package:interview_hammer/utils/smartlook/smartlook.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../api/management/user_profile_manager.dart';
import 'events/events.dart';
import 'onesignal.dart';

void initExternalServices() {
  initOneSignal();
  initSmartlook();
  _setupSentUser();
}

Future<void> _setupSentUser() async {
  final user = getCurrentFirebaseUser();
  Sentry.configureScope(
    (scope) => scope.setUser(
      SentryUser(
        id: user?.uid,
        username: user?.email,
      ),
    ),
  );
}

void setupUserForExternalServices() {
  setupOneSignalUser();
  setupSmartlookUser();

  userProfileManager.userProfileStream.listen((userProfile) {
    trackEvent('planType', userProfile?.planType);
  });
}

void logoutUserFromExternalServices() {
  if (!kIsWeb) OneSignal.logout();

  Sentry.configureScope((scope) => scope.setUser(null));
}

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:interview_hammer/utils/platform_utils.dart';

import 'events/events.dart';

/// Detects if Zoom is installed on the system (macOS only).
/// Returns a [ZoomInfo] object containing installation information.
Future<ZoomInfo> detectZoom() async {
  final channel = const MethodChannel('window_controls');

  if (kIsNativeDesktop && defaultTargetPlatform == TargetPlatform.macOS) {
    try {
      final Map<dynamic, dynamic> result =
          await channel.invokeMethod('detectZoom');
      return ZoomInfo.fromMap(result);
    } catch (e) {
      logError(e);
      // Return default object indicating Zoom is not installed when an error occurs
      return ZoomInfo(installed: false);
    }
  }

  // Not on macOS or not on desktop, so return default
  return ZoomInfo(installed: false);
}

/// A class that represents zoom detection results.
class ZoomInfo {
  /// Whether Zoom is installed on the system
  final bool installed;

  /// The version of Zoom if installed, otherwise null
  final String? version;

  /// How Zoom was installed: "standard", "non-standard location", "homebrew", or null if not found
  final String? installMethod;

  ZoomInfo({
    required this.installed,
    this.version,
    this.installMethod,
  });

  /// Creates a ZoomInfo instance from a map returned by the platform channel
  factory ZoomInfo.fromMap(Map<dynamic, dynamic> map) {
    return ZoomInfo(
      installed: map['installed'] as bool,
      version: map['version'] as String?,
      installMethod: map['installMethod'] as String?,
    );
  }

  @override
  String toString() {
    if (installed) {
      return 'Zoom is installed (Version: ${version ?? 'unknown'}, Method: ${installMethod ?? 'unknown'})';
    }
    return 'Zoom is not installed';
  }
}

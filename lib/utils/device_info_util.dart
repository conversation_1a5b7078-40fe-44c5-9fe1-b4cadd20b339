import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/utils/platform_utils.dart';

/// Utility class to handle device information retrieval
class DeviceInfoUtil {
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  /// Get the device name in a consistent format across platforms
  static Future<String> getDeviceName() async {
    try {
      if (isElectron()) {
        if (defaultTargetPlatform == TargetPlatform.windows) {
          return 'Windows Desktop';
        } else if (defaultTargetPlatform == TargetPlatform.macOS) {
          return 'macOS Desktop';
        } else if (defaultTargetPlatform == TargetPlatform.linux) {
          return 'Linux Desktop';
        } else {
          return 'Desktop';
        }
      } else if (kIsWeb) {
        final webInfo = await _deviceInfo.webBrowserInfo;
        return '${webInfo.browserName.name} on ${webInfo.platform}';
      } else if (kIsNativeDesktop) {
        if (defaultTargetPlatform == TargetPlatform.macOS) {
          final macInfo = await _deviceInfo.macOsInfo;
          return '${macInfo.computerName} (macOS)';
        } else if (defaultTargetPlatform == TargetPlatform.windows) {
          final windowsInfo = await _deviceInfo.windowsInfo;
          return '${windowsInfo.computerName} (Windows)';
        } else if (defaultTargetPlatform == TargetPlatform.linux) {
          final linuxInfo = await _deviceInfo.linuxInfo;
          return '${linuxInfo.prettyName} (Linux)';
        }
      } else if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return androidInfo.model;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return '${iosInfo.name} ${iosInfo.model}';
      }
      return 'Unknown Device';
    } catch (e) {
      d('Error getting device name: $e');
      return 'Unknown Device';
    }
  }
}

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:interview_hammer/utils/platform_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:window_manager/window_manager.dart';

import '../screens/display_settings_screen.dart';
import 'events/events.dart';

Future<void> setHideFromScreenSharing(bool hideFromScreenSharing) async {
  final channel = const MethodChannel('window_controls');

  if (kIsNativeDesktop && defaultTargetPlatform == TargetPlatform.macOS) {
    try {
      await channel.invokeMethod('setHideFromScreenSharing', {
        'hideFromScreenSharing': hideFromScreenSharing,
      });

      // for some reason hiding from screen sharing breaks always on top so we set it again
      final prefs = await SharedPreferences.getInstance();
      final isAlwaysOnTop =
          prefs.getBool(KEY_WINDOW_ALWAYS_ON_TOP) ?? DEFAULT_ALWAYS_ON_TOP;
      await windowManager.setAlwaysOnTop(isAlwaysOnTop);
    } catch (e) {
      logError(e);
    }
  }
}

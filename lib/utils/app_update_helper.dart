import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:interview_hammer/api/management/remote_config.dart';
import 'package:interview_hammer/utils/platform_utils.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

Future<void> checkAppUpdate(BuildContext context) async {
  final appUpdateState = await getAppUpdateState();
  // d("appUpdateState = $appUpdateState");
  if (!context.mounted) return;

  switch (appUpdateState) {
    case AppUpdateState.REQUIRED:
      await _showUpdateVersionDialog(context, false);
      break;
    case AppUpdateState.RECOMMENDED:
      await _showUpdateVersionDialog(context, true);
      break;
    case AppUpdateState.UPDATED:
      break;
  }
}

Future<int> getCurrentVersion() async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  final currentVersion = packageInfo.version;

  return _getExtendedVersionNumber(currentVersion);
}

int getMinSupportedVersion() {
  final minSupportedVersionCode =
      remoteConfig.getString(KEY_MIN_SUPPORTED_VERSION);
  return _getExtendedVersionNumber(minSupportedVersionCode);
}

int getMinRecommendedVersion() {
  final minRecommendedVersionCode =
      remoteConfig.getString(KEY_MIN_RECOMMENDED_VERSION);
  return _getExtendedVersionNumber(minRecommendedVersionCode);
}

Future<AppUpdateState> getAppUpdateState() async {
  final currentVersionCode = await getCurrentVersion();
  final minSupportedVersionCode = getMinSupportedVersion();
  final minRecommendedVersionCode = getMinRecommendedVersion();

  if (kIsWeb && !isElectron()) return AppUpdateState.UPDATED;

  if (minSupportedVersionCode > currentVersionCode) {
    return AppUpdateState.REQUIRED;
  } else if (minRecommendedVersionCode > currentVersionCode) {
    return AppUpdateState.RECOMMENDED;
  } else {
    return AppUpdateState.UPDATED;
  }
}

enum AppUpdateState { REQUIRED, RECOMMENDED, UPDATED }

// region utils
int _getExtendedVersionNumber(String version) {
  List versionCells = version.split('.');
  versionCells = versionCells.map((i) => int.parse(i)).toList();
  return versionCells[0] * 100000 + versionCells[1] * 1000 + versionCells[2];
}
// endregion utils

// region ui
Future<void> _showUpdateVersionDialog(
    BuildContext context, bool isSkippable) async {
  return showDialog<void>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text("New version available"),
        content: SingleChildScrollView(
          child: ListBody(
            children: <Widget>[
              Text("Please update to the latest version of the app."),
            ],
          ),
        ),
        actions: <Widget>[
          // A "skip" button is only shown if it's a recommended upgrade
          if (isSkippable)
            TextButton(
              child: const Text('Skip'),
              onPressed: () => Navigator.of(context).pop(),
            )
          else
            Container(),
          TextButton(
            child: const Text('Update'),
            onPressed: () async {
              final updateLink = remoteConfig.getString(KEY_UPDATE_LINK);
              await launchUrl(Uri.parse(updateLink));
            },
          ),
        ],
      );
    },
  );
}

import 'dart:math';

extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return this;
    return "${this[0].toUpperCase()}${substring(1)}";
  }

  String capitalizeEachWord() {
    return split(' ')
        .map((word) =>
            word.isEmpty ? word : word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  /// Replaces standard dollar signs ($) with full width dollar signs (＄)
  /// Full width dollar sign is Unicode U+FF04
  /// Needs to do this because the standard dollar sign breaks the Markdown parser
  String replaceDollarWithFullWidth() {
    return replaceAll('\$', '＄');
  }

  String slice([int? start, int? end]) {
    // Handle negative indices
    start = start ?? 0;
    end = end ?? length;

    if (start < 0) start = max(0, length + start);
    if (end < 0) end = max(0, length + end);

    // Ensure start is not greater than end
    if (start > end) return '';

    // Clamp indices to string bounds
    start = min(start, length);
    end = min(end, length);

    return substring(start, end);
  }

  /// Normalizes spacing around bullet points (•), ensuring there are double
  /// newlines (\n\n) before and after bullet points. Ignores any text within
  /// triple backtick code blocks. Preserves existing paragraph structure,
  /// leading spaces, and handles the ". •" pattern by turning it into ".\n\n•".
  static final _codeBlockRegex = RegExp(r'```[\s\S]*?```');
  static final _bulletRegex = RegExp(r'(\.\s*•)|( •)');

  String normalizeBulletAndParagraphSpacing() {
    final buffer = StringBuffer();
    var lastPos = 0;

    // Traverse code blocks in this string
    for (final match in _codeBlockRegex.allMatches(this)) {
      // Process everything before the code block
      buffer.write(_processPlainText(substring(lastPos, match.start)));

      // Write the code block unchanged
      buffer.write(substring(match.start, match.end));
      lastPos = match.end;

      // If there's a newline immediately after the code block, keep it
      if (lastPos < length && this[lastPos] == '\n') {
        buffer.write('\n');
        lastPos++;
      }
    }

    // Process any remaining text after the last code block
    buffer.write(_processPlainText(substring(lastPos)));

    return buffer.toString();
  }

  // Private helper that handles bullet-point spacing in non-code text.
  static String _processPlainText(String text) {
    if (text.isEmpty) return '';

    // Replace ". •" → ".\n\n•" and " •" → "\n\n•" in a single pass
    final replaced = text.replaceAllMapped(
      _bulletRegex,
      (m) => m.group(1) != null ? '.\n\n•' : '\n\n•',
    );

    // Split into lines, then add blank lines before and after bullet lines
    final lines = replaced.split('\n');
    final processed = <String>[];

    for (var i = 0; i < lines.length; i++) {
      final line = lines[i];
      final trimmed = line.trim();
      final isBullet = trimmed.startsWith('•');

      // If this line is a bullet and the previous processed line has text, insert a blank line before it.
      if (isBullet &&
          processed.isNotEmpty &&
          processed.last.trim().isNotEmpty) {
        processed.add('');
      }

      // Add bullet lines with .trim(), but preserve original spacing for non-bullet lines.
      processed.add(isBullet ? trimmed : line);

      // If the current line is a bullet, and the next line has text, insert a blank line after it.
      if (isBullet && i < lines.length - 1 && lines[i + 1].trim().isNotEmpty) {
        processed.add('');
      }
    }

    return processed.join('\n');
  }
}

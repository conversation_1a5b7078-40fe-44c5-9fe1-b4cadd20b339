// import 'dart:convert';
//
// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:flutter_toolbox/flutter_toolbox.dart';
//
// import '../../app.dart';
//
// final FirebaseMessaging _firebaseMessaging = FirebaseMessaging();
//
// void initFcm(BuildContext context) {
//   _firebaseMessaging.configure(
//     onMessage: (Map<String, dynamic> message) async {
//       d("onMessage: $message");
//       FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//           FlutterLocalNotificationsPlugin();
//       var initializationSettingsAndroid =
//           AndroidInitializationSettings('ic_notification');
//       var initializationSettingsIOS = IOSInitializationSettings();
//       var initializationSettings = InitializationSettings(
//           initializationSettingsAndroid, initializationSettingsIOS);
//
//       flutterLocalNotificationsPlugin.initialize(
//         initializationSettings,
//         onSelectNotification: (payload) async {
//           final message = json.decode(payload);
//           onNotificationClick(context, message);
//         },
//       );
//
//       var androidPlatformChannelSpecifics = AndroidNotificationDetails(
//         appName,
//         appName,
//         appName,
//         importance: Importance.Max,
//         priority: Priority.High,
//       );
//       var iOSPlatformChannelSpecifics = IOSNotificationDetails();
//       var platformChannelSpecifics = NotificationDetails(
//           androidPlatformChannelSpecifics, iOSPlatformChannelSpecifics);
//       await flutterLocalNotificationsPlugin.show(
//         message['notification']['body'].hashCode,
//         message['notification']['title'],
//         message['notification']['body'],
//         platformChannelSpecifics,
//         payload: json.encode(message),
//       );
//     },
//     onLaunch: (Map<String, dynamic> message) async {
//       d("onLaunch: $message");
//       onNotificationClick(context, message);
//     },
//     onResume: (Map<String, dynamic> message) async {
//       d("onResume: $message");
//       onNotificationClick(context, message);
//     },
//   );
//   _firebaseMessaging.requestNotificationPermissions(
//       const IosNotificationSettings(sound: true, badge: true, alert: true));
//   _firebaseMessaging.onIosSettingsRegistered
//       .listen((IosNotificationSettings settings) {
//     d("Settings registered: $settings");
//   });
// }
//
// void onNotificationClick(BuildContext context, Map<String, dynamic> message) {
//   // ignore: unused_local_variable
//   var data = message['data'] ?? message;
// }

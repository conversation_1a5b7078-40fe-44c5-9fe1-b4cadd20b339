import 'package:flutter/foundation.dart';
import 'package:interview_hammer/services/electron_interaction/electron_interaction_service.dart';

final kIsMobile = defaultTargetPlatform == TargetPlatform.android ||
    defaultTargetPlatform == TargetPlatform.iOS;

final kIsWebMobile = kIsWeb && kIsMobile;
final kIsWebDesktop = kIsWeb && kIsDesktop;

final kIsNativeMobile = !kIsWeb && kIsMobile;

final kIsDesktop = defaultTargetPlatform == TargetPlatform.linux ||
    defaultTargetPlatform == TargetPlatform.macOS ||
    defaultTargetPlatform == TargetPlatform.windows ||
    defaultTargetPlatform == TargetPlatform.fuchsia;

final kIsNativeDesktop = !kIsWeb && kIsDesktop;

final kIsNativeMacOS = !kIsWeb && defaultTargetPlatform == TargetPlatform.macOS;

/// Whether this is a store build (excludes private API features)
const kIsStoreBuild = bool.fromEnvironment('STORE_BUILD', defaultValue: false);

bool isElectron() => getElectronInteractionService().isElectron;

bool isDesktopPlatform() {
  try {
    return kIsNativeDesktop || isElectron();
  } catch (e) {
    return kIsNativeDesktop;
  }
}

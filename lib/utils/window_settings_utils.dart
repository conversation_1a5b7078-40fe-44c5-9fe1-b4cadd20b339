import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_acrylic/flutter_acrylic.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/services/electron_interaction/electron_interaction_service.dart';
import 'package:interview_hammer/services/system_tray/system_tray_service.dart';
import 'package:interview_hammer/utils/events/events.dart';
import 'package:interview_hammer/utils/platform_utils.dart';
import 'package:interview_hammer/utils/screen_sharing.dart';
import 'package:window_manager/window_manager.dart';

final _electronService = getElectronInteractionService();

/// Applies the window opacity setting.
Future<void> applyOpacitySetting(double value) async {
  try {
    if (isElectron()) {
      await _electronService.setOpacity(value);
    } else if (kIsNativeDesktop) {
      try {
        await Window.setWindowAlphaValue(value);
      } catch (e) {
        d("Error applying native opacity: $e");
        logError(e);
      }
    }
  } catch (e) {
    d("Error applying opacity setting: $e");
    logError(e);
  }
}

/// Applies the 'always on top' setting.
Future<void> applyAlwaysOnTopSetting(bool value) async {
  try {
    if (isElectron()) {
      await _electronService.setAlwaysOnTop(value);
    } else if (kIsNativeDesktop) {
      try {
        await windowManager.setAlwaysOnTop(value);
      } catch (e) {
        d("Error applying native alwaysOnTop: $e");
        logError(e);
      }
    }
  } catch (e) {
    d("Error applying always on top setting: $e");
    logError(e);
  }
}

/// Applies the 'skip taskbar' (hide icon) setting.
Future<void> applySkipTaskbarSetting(bool value) async {
  try {
    if (isElectron()) {
      await _electronService.setSkipTaskbar(value);
    } else if (kIsNativeDesktop) {
      try {
        await windowManager.setSkipTaskbar(value);
      } catch (e) {
        d("Error applying native skipTaskbar: $e");
        logError(e);
      }
    }
  } catch (e) {
    d("Error applying skip taskbar setting: $e");
    logError(e);
  }
}

/// Applies the 'visible on all workspaces' (macOS) setting.
Future<void> applyVisibleOnAllWorkspacesSetting(bool value) async {
  try {
    // This setting is macOS specific
    if (defaultTargetPlatform == TargetPlatform.macOS) {
      if (isElectron()) {
        await _electronService.setVisibleOnAllWorkspaces(value);
      } else if (kIsNativeDesktop) {
        await windowManager.setVisibleOnAllWorkspaces(value);
      }
    }
  } catch (e) {
    d("Error applying visible on all workspaces setting: $e");
    logError(e);
  }
}

/// Applies the 'hide from screen sharing' (content protection) setting.
Future<void> applyHideFromScreenSharingSetting(bool value) async {
  try {
    if (isElectron()) {
      await _electronService.setContentProtection(value);
    } else {
      await setHideFromScreenSharing(value);
    }
  } catch (e) {
    d("Error applying hide from screen sharing setting: $e");
    logError(e);
  }
}

/// Applies the app name setting.
Future<void> applyAppNameSetting(String name) async {
  try {
    if (isElectron()) {
      // todo: replace with script call
      // await _electronService.setAppName(name);
    } else if (kIsNativeDesktop &&
        defaultTargetPlatform == TargetPlatform.macOS &&
        !kIsStoreBuild) {
      const channel = MethodChannel('app_name_control');
      await channel.invokeMethod('setProcessDisplayName', name);
    }
  } catch (e) {
    d("Error applying app name setting: $e");
    logError(e);
  }
}

/// Applies the hide system tray setting.
Future<void> applyHideSystemTraySetting(bool value) async {
  try {
    if (value) {
      await SystemTrayService.instance.hideTrayIcon();
    } else {
      await SystemTrayService.instance.showTrayIcon();
    }
  } catch (e) {
    d("Error applying hide system tray setting: $e");
    logError(e);
  }
}

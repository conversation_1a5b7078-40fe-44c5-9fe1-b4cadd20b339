import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:interview_hammer/model/server/screenshot_reference.dart';
import 'package:interview_hammer/model/server/session.dart';
import 'package:interview_hammer/utils/device_info_util.dart';
import 'package:uuid/uuid.dart';

import '../../utils/events/events.dart';

final firestore = FirebaseFirestore.instance;
final colSessions = firestore.collection('sessions');

const commandTimeout = Duration(seconds: 5);

// Get active sessions for a user
Stream<List<Session>> getActiveSessions() {
  final userId = FirebaseAuth.instance.currentUser?.uid;
  if (userId == null) {
    return Stream.value([]);
  }

  try {
    return colSessions
        .where('userId', isEqualTo: userId)
        .where('status',
            whereIn: [SessionStatus.ACTIVE.name, SessionStatus.WAITING.name])
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            return Session.fromFirestore(doc);
          }).toList();
        });
  } catch (e) {
    logError('Error setting up active sessions stream: $e');
    return Stream.value([]);
  }
}

// Create a new session
Future<Session> createSession() async {
  final String userId = FirebaseAuth.instance.currentUser!.uid;

  try {
    final String deviceName = await DeviceInfoUtil.getDeviceName();
    final String sessionId = const Uuid().v4();

    final session = Session(
      sessionId: sessionId,
      deviceName: deviceName,
      userId: userId,
      sessionStartTime: Timestamp.now(),
      sessionEndTime: null,
      status: SessionStatus.WAITING,
      currentCommand: CommandType.IDLE,
      commandTimestamp: null,
      commandStatus: CommandStatus.COMPLETED,
      screenshots: [],
    );

    await colSessions.doc(sessionId).set(session.toFirestore());
    return session;
  } catch (e) {
    logError('Failed to create session: $e');
    throw SessionException(
        'Unable to start a new session. Please try again later.');
  }
}

// End a session
Future<void> endSession(String sessionId) async {
  try {
    await colSessions.doc(sessionId).update({
      'status': SessionStatus.ENDED.name,
      'sessionEndTime': Timestamp.now(),
      'currentCommand': CommandType.IDLE.name,
      'commandStatus': CommandStatus.COMPLETED.name,
    });
  } catch (e) {
    logError('Failed to end session: $e');
    throw SessionException('Unable to end the session. Please try again.');
  }
}

// Set command on a session with timeout handling
Future<void> setCommand(String sessionId, CommandType command) async {
  try {
    // Check if session exists and is active
    final session = await colSessions.doc(sessionId).get();
    if (!session.exists) {
      throw CommandException(
          'Session not found. It may have expired or been deleted.');
    }

    final sessionData = Session.fromFirestore(session);
    if (sessionData.status != SessionStatus.ACTIVE &&
        sessionData.status != SessionStatus.WAITING) {
      throw CommandException(
          'This session is no longer active. Please start a new undetectable mode session at the Select Mode screen.');
    }

    // Check if there's already a pending command
    if (sessionData.commandStatus == CommandStatus.PENDING) {
      // If the pending command is too old, mark it as failed
      final commandAge = Timestamp.now().toDate().difference(
            sessionData.commandTimestamp?.toDate() ?? Timestamp.now().toDate(),
          );

      if (commandAge > commandTimeout) {
        await updateCommandStatus(sessionId, CommandStatus.FAILED);
      } else {
        throw CommandException(
            'Please wait for the current operation to complete.');
      }
    }

    // Set new command
    await colSessions.doc(sessionId).update({
      'currentCommand': command.name,
      'commandTimestamp': Timestamp.now(),
      'commandStatus': CommandStatus.PENDING.name,
    });
  } catch (e) {
    logError('Failed to set command: $e');
    if (e is CommandException) {
      rethrow;
    }
    throw CommandException(
        'Unable to perform this operation. Please try again.');
  }
}

// Update command status
Future<void> updateCommandStatus(String sessionId, CommandStatus status) async {
  try {
    await colSessions.doc(sessionId).update({
      'commandStatus': status.name,
    });
  } catch (e) {
    logError('Failed to update command status: $e');
    throw CommandException(
        'Unable to update the operation status. Please try again.');
  }
}

// Update session status
Future<void> updateSessionStatus(String sessionId, SessionStatus status) async {
  try {
    await colSessions.doc(sessionId).update({
      'status': status.name,
    });
  } catch (e) {
    logError('Failed to update session status: $e');
    throw SessionException('Failed to update session status: $e');
  }
}

// Listen to commands for a session
Stream<Session> listenToSessionCommands(String sessionId) {
  try {
    return colSessions.doc(sessionId).snapshots().map((snapshot) {
      if (!snapshot.exists) {
        throw SessionException(
            'Session not found. It may have expired or been deleted.');
      }
      return Session.fromFirestore(snapshot);
    });
  } catch (e) {
    logError('Failed to set up command listener: $e');
    return Stream.error(SessionException(
        'Unable to connect to the session. Please check your connection.'));
  }
}

// Add a screenshot to a session
Future<void> addScreenshotToSession(
    String sessionId, String screenshotUrl) async {
  try {
    // Check if the screenshot URL is valid
    if (screenshotUrl.isEmpty) {
      throw ScreenshotException('Unable to save the screenshot.');
    }

    final screenshot = ScreenshotReference(
      screenshotUrl: screenshotUrl,
      timestamp: Timestamp.now(),
    );

    await colSessions.doc(sessionId).update({
      'screenshots': FieldValue.arrayUnion([screenshot.toMap()]),
    });
  } catch (e) {
    logError('Failed to add screenshot: $e');
    if (e is ScreenshotException) {
      rethrow;
    }
    throw ScreenshotException(
        'Unable to save the screenshot. Please try again.');
  }
}

// Custom exceptions
class SessionException implements Exception {
  final String message;

  SessionException(this.message);

  @override
  String toString() => message;
}

class CommandException implements Exception {
  final String message;

  CommandException(this.message);

  @override
  String toString() => message;
}

class ScreenshotException implements Exception {
  final String message;

  ScreenshotException(this.message);

  @override
  String toString() => message;
}

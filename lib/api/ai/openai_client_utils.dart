import 'package:flutter/foundation.dart';
import 'package:interview_hammer/api/ai/ai_config_service.dart';
import 'package:interview_hammer/api/ai/openai.dart';
import 'package:interview_hammer/api/management/user_profile_manager.dart';
import 'package:interview_hammer/api/storage/image_storage_service.dart';
import 'package:interview_hammer/model/screenshot.dart';
import 'package:openai_dart/openai_dart.dart';

import '../../utils/events/events.dart';
import '../utils.dart';

// disable when using dev environment
final bool store =
    kReleaseMode && !(openAIClient.baseUrl?.contains('dev') == true);

// region chatCompletion
Future<CreateChatCompletionResponse> createCompletion({
  required String model,
  required List<ChatCompletionMessage> messages,
  required ResponseFormat responseFormat,
}) {
  return openAIClient.createChatCompletion(
    request: _createChatCompletionRequest(
      model: model,
      messages: messages,
      responseFormat: responseFormat,
    ),
  );
}

/// Creates a completion stream and handles JSON accumulation and parsing
/// with automatic fallback to fallback model if needed
void createCompletionStreamWithHandler({
  required String model,
  required List<ChatCompletionMessage> messages,
  required ResponseFormat responseFormat,
  required Function(dynamic) onParsedJson,
  required Function(String fullResponse) onDone,
  required Function(dynamic) onError,
  bool isRetry = false,
}) {
  var accumulator = '';

  try {
    final stream = _createCompletionStream(
      model: model,
      messages: messages,
      responseFormat: responseFormat,
    );

    stream.listen(
      (streamChatCompletion) {
        final text = streamChatCompletion.choices.first.delta?.content;
        if (text != null) {
          accumulator += text;

          final response = parseIncompleteJson(accumulator);
          if (response != null) {
            onParsedJson(response);
          } else {
            logError('Error in completion stream: $accumulator');
          }
        }
      },
      onDone: () => onDone(accumulator),
      onError: (error) => _handleError(
        error: error,
        model: model,
        messages: messages,
        responseFormat: responseFormat,
        onParsedJson: onParsedJson,
        onDone: onDone,
        onError: onError,
        isRetry: isRetry,
      ),
    );
  } catch (e) {
    _handleError(
      error: e,
      model: model,
      messages: messages,
      responseFormat: responseFormat,
      onParsedJson: onParsedJson,
      onDone: onDone,
      onError: onError,
      isRetry: isRetry,
    );
  }
}

CreateChatCompletionRequest _createChatCompletionRequest({
  required String model,
  required List<ChatCompletionMessage> messages,
  required ResponseFormat responseFormat,
}) {
  final baseRequest = CreateChatCompletionRequest(
    model: ChatCompletionModel.modelId(model),
    messages: messages,
    responseFormat: responseFormat,
    topP: null,
    presencePenalty: null,
    frequencyPenalty: null,
  );

  if (aiConfigService.aiConfig.reasoningModels.contains(model)) {
    return baseRequest.copyWith(
      store: store,
      metadata: _getOpenAIMetadata(),
      maxTokens: null,
      temperature: null,
    );
  }

  final isNonOpenAiModel = aiConfigService.aiConfig.nonOpenAiModels
      .any((nonOpenAiModel) => model.contains(nonOpenAiModel));
  if (isNonOpenAiModel) {
    return baseRequest.copyWith(
      temperature: 0,
      // gemini sometimes give longer responses, and gives incomplete json if we have small maxTokens
      maxTokens: null,
    );
  }

  // Default to using OpenAI settings
  return baseRequest.copyWith(
    store: store,
    metadata: _getOpenAIMetadata(),
    maxTokens: 1000,
    temperature: 0,
  );
}

Stream<CreateChatCompletionStreamResponse> _createCompletionStream({
  required String model,
  required List<ChatCompletionMessage> messages,
  required ResponseFormat responseFormat,
}) {
  if (model == 'o1') {
    return _createO1CompletionStream(
      model: model,
      messages: messages,
      responseFormat: responseFormat,
    );
  }

  return openAIClient.createChatCompletionStream(
    request: _createChatCompletionRequest(
      model: model,
      messages: messages,
      responseFormat: responseFormat,
    ),
  );
}

/// Handles errors and tries fallback model if appropriate
void _handleError({
  required dynamic error,
  required String model,
  required List<ChatCompletionMessage> messages,
  required ResponseFormat responseFormat,
  required Function(dynamic) onParsedJson,
  required Function(String) onDone,
  required Function(dynamic) onError,
  required bool isRetry,
}) {
  if (isRetry) {
    logError('Retry failed for model: $model');
    onError(error);
    return;
  }

  // Use the fallback model if it exists, otherwise retry with the same model
  final fallbackModel = aiConfigService.aiConfig.modelFallbacks[model];
  final modelToUse = fallbackModel ?? model;

  final retryMessage = fallbackModel != null
      ? 'Model $model failed, trying fallback model: $fallbackModel'
      : 'Model $model failed, retrying with same model';

  logError(retryMessage);

  // Try again with either the fallback model or the same model
  createCompletionStreamWithHandler(
    model: modelToUse,
    messages: messages,
    responseFormat: responseFormat,
    onParsedJson: onParsedJson,
    onDone: onDone,
    onError: onError,
    isRetry: true, // Mark this as a retry to prevent further attempts
  );
}

// O1 model does not support streaming, so we fake it to keep the same interface
Stream<CreateChatCompletionStreamResponse> _createO1CompletionStream({
  required String model,
  required List<ChatCompletionMessage> messages,
  required ResponseFormat responseFormat,
}) {
  return Stream.fromFuture(createCompletion(
    model: model,
    messages: messages,
    responseFormat: responseFormat,
  )).map((response) => CreateChatCompletionStreamResponse(
        id: response.id,
        created: response.created,
        model: response.model,
        choices: [
          ChatCompletionStreamResponseChoice(
            index: 0,
            finishReason: response.choices.first.finishReason,
            delta: ChatCompletionStreamResponseDelta(
              role: response.choices.first.message.role,
              content: response.choices.first.message.content,
            ),
          )
        ],
        object: 'chat.completion.chunk',
      ));
}

Map<String, String>? _getOpenAIMetadata() {
  return store
      ? {
          'userId': userProfileManager.userProfile?.uid ?? '',
          'userEmail': userProfileManager.userProfile?.email ?? '',
        }
      : null;
}
// endregion chatCompletion

// region imageProcessing
final _imageStorageService = ImageStorageService.instance;

/// Processes screenshots for AI consumption:
///
/// For local screenshots (with image bytes):
/// 1. Uses cached storage URLs when available for already uploaded images
/// 2. For new images, immediately uses base64 encoding for instant response
/// 3. Simultaneously starts a background upload to Firebase Storage for future requests
/// 4. This approach gives fastest initial response while optimizing for subsequent requests
///
/// For remote screenshots: Uses the provided URL directly without modification
///
Future<List<ChatCompletionMessageContentPart>> processImagesForAI(
    List<Screenshot> screenshots) async {
  return screenshots.map((screenshot) {
    if (screenshot is LocalScreenshot) {
      return ChatCompletionMessageContentPart.image(
        imageUrl: ChatCompletionMessageImageUrl(
          url: _imageStorageService.getBestImageUrl(screenshot.bytes),
        ),
      );
    } else if (screenshot is RemoteScreenshot) {
      return ChatCompletionMessageContentPart.image(
        imageUrl: ChatCompletionMessageImageUrl(url: screenshot.url),
      );
    }
    throw ArgumentError('Unknown screenshot type: ${screenshot.runtimeType}');
  }).toList();
}

// ignore: unused_element
List<ChatCompletionMessageContentPart> _encodeImages(List<Uint8List> images) {
  return images.map((imageBytes) {
    return ChatCompletionMessageContentPart.image(
      imageUrl: ChatCompletionMessageImageUrl(
        // url:
        //     'https://firebasestorage.googleapis.com/v0/b/interviewhammer1.appspot.com/o/userImages%2Fi8BBlw6jwrcZ0RQtcqrMjC1TXzb2%2F1740485544316_a30ad5b7388fa76a.png?alt=media&token=d7fe0176-2c32-4142-982a-f6e6f2e93459',
        url: _imageStorageService.getBase64Image(imageBytes),
      ),
    );
  }).toList();
}
// endregion imageProcessing

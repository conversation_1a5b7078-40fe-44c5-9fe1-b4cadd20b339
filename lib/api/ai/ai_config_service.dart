import 'dart:convert';

import 'package:interview_hammer/api/management/remote_config.dart';
import 'package:interview_hammer/model/ai/ai_config.dart';
import 'package:interview_hammer/utils/events/events.dart';

class AIConfigService {
  static final AIConfigService _instance = AIConfigService._internal();

  AIConfigService._internal();

  static AIConfigService get instance => _instance;

  late AIConfig _aiConfig;
  bool _isInitialized = false;

  AIConfig get aiConfig {
    if (!_isInitialized) {
      _loadConfig();
    }
    return _aiConfig;
  }

  String getQuestionDetectionModel({required bool hasImages}) {
    if (!_isInitialized) _loadConfig();
    return hasImages ? _aiConfig.imageModel : _aiConfig.questionDetectionModel;
  }

  String getAnswerGenerationModel({required bool hasImages}) {
    if (!_isInitialized) _loadConfig();
    return hasImages ? _aiConfig.imageModel : _aiConfig.answerGenerationModel;
  }

  void _loadConfig() {
    try {
      final configJson = remoteConfig.getString(KEY_AI_CONFIG);

      if (configJson.isEmpty) {
        _aiConfig = AIConfig.defaultConfig();
      } else {
        final Map<String, dynamic> jsonMap = json.decode(configJson);
        _aiConfig = AIConfig.fromJson(jsonMap);
      }
    } catch (e) {
      logError(e);
      _aiConfig = AIConfig.defaultConfig();
    }
    _isInitialized = true;
  }
}

final aiConfigService = AIConfigService.instance;

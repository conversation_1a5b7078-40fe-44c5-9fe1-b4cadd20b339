import 'package:flutter/foundation.dart';
import 'package:interview_hammer/api/ai/ai_config_service.dart';
import 'package:openai_dart/openai_dart.dart';

const logAnswering = kDebugMode && false;
const logDetection = kDebugMode && false;

late final OpenAIClient openAIClient;
bool _isOpenAiInitialized = false;

void initOpenAi() {
  if (_isOpenAiInitialized) {
    return;
  }

  openAIClient = OpenAIClient(
    apiKey: aiConfigService.aiConfig.apiKey,
    baseUrl: aiConfigService.aiConfig.baseUrl,
  );

  _isOpenAiInitialized = true;
}

import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/api/ai/ai_config_service.dart';
import 'package:interview_hammer/api/management/user_profile_manager.dart';
import 'package:interview_hammer/api/utils.dart';
import 'package:interview_hammer/model/management/user_profile.dart';
import 'package:interview_hammer/model/screenshot.dart';
import 'package:interview_hammer/utils/list_utils.dart';
import 'package:openai_dart/openai_dart.dart';

import '../../model/management/answer_settings.dart';
import 'openai.dart';
import 'openai_client_utils.dart';
import 'prompts.dart';

// region extractQuestion
Future<dynamic> extractQuestion(
  String transcript,
  String previousQuestion, {
  List<Screenshot> images = const [],
}) async {
  final startTime = DateTime.now();
  // d('extractQuestionChat');
  // d("previousQuestion = $previousQuestion");
  final settings =
      userProfileManager.userProfile?.answerSettings ?? AnswerSettings();
  final lastMinute = getLastTranscriptByDuration(
      transcript, settings.transcriptDurationSeconds);
  final lastImages = images.takeLast(settings.maxNumberOfImages);

  final model = aiConfigService.getQuestionDetectionModel(
      hasImages: lastImages.isNotEmpty);

  final systemMessage = ChatCompletionMessage.system(
    content: _getExtractQuestionPrompt(previousQuestion, lastImages),
  );

  final userMessage = ChatCompletionMessage.user(
    content: ChatCompletionUserMessageContent.parts([
      ChatCompletionMessageContentPart.text(
        text: _getTranscriptionMessage(lastMinute),
      ),
      ...(await processImagesForAI(lastImages)),
    ]),
  );

  final completion = await createCompletion(
    model: model,
    messages: [systemMessage, userMessage],
    responseFormat: extractQuestionResponseFormat,
  );

  final text = completion.choices.firstOrNull?.message.content;

  _logDetection(systemMessage, lastMinute, text, startTime);

  return text;
}

String _getExtractQuestionPrompt(
  String previousQuestion,
  List<Screenshot> images,
) {
  final userProfile = userProfileManager.userProfile;
  final sensitivity =
      userProfile!.answerSettings?.questionDetectionSensitivity ??
          AnswerSettings.defaultSensitivity;

  final basePrompt =
      images.isNotEmpty ? screenshotsExtractQuestionPrompt : sensitivity.prompt;

  final interviewTopic = userProfile.interviewTopic ?? 'Not specified';

  var prompt = basePrompt
      .replaceAll("{{INTERVIEW_TOPIC}}", interviewTopic)
      .replaceAll("{{PREVIOUS_QUESTION}}", previousQuestion);

  prompt += _getLanguagePromptForExtractQuestion(userProfile);

  return prompt;
}

String _getLanguagePromptForExtractQuestion(UserProfile userProfile) {
  final languages =
      userProfile.interviewLanguages ?? [UserProfile.defaultLanguage];

  // If the language is the default language, don't include it in the prompt
  if (languages.length == 1 && languages.first == UserProfile.defaultLanguage) {
    return '';
  }

  final languageNames = languages.map((lang) => lang.aiName).join(', ');
  final prefix =
      languages.length == 1 ? 'Interview language: ' : 'Interview languages: ';

  return '\n$prefix$languageNames';
}

void _logDetection(ChatCompletionMessage systemMessage, String lastMinute,
    String? text, DateTime startTime) {
  if (logDetection) {
    dl('# START extractQuestion');
    dl('# systemMessage: ➡️ ${systemMessage.content}');
    dl('# userMessage: ➡️ ${_getTranscriptionMessage(lastMinute)}');
    dl('# response: ⬅️ $text');
    dl('# duration: ⏱️ ${DateTime.now().difference(startTime)}');
    dl('# END extractQuestion');
  }
}
// endregion extractQuestion

// region answerQuestion
Future answerQuestionStream(
  String transcript, {
  required bool requiresPersonalInfo,
  String? customUserMessage,
  required Function(dynamic) listener,
  required Function() onDone,
  required Function() onError,
  List<Screenshot> images = const [],
  String prompt = questionAnsweringPromptSystem,
}) async {
  final startTime = DateTime.now();
  final settings =
      userProfileManager.userProfile?.answerSettings ?? AnswerSettings();
  final lastMinute = getLastTranscriptByDuration(
      transcript, settings.transcriptDurationSeconds);
  final lastImages = images.takeLast(settings.maxNumberOfImages);

  final model = aiConfigService.getAnswerGenerationModel(
      hasImages: lastImages.isNotEmpty);

  final systemPrompt = _getQuestionAnsweringSystemPrompt(
    prompt,
    userProfileManager.userProfile!.interviewTopic ?? '',
    requiresPersonalInfo,
    customUserMessage,
    lastImages,
  );

  final systemMessage = ChatCompletionMessage.system(content: systemPrompt);

  final userMessage = ChatCompletionMessage.user(
    content: ChatCompletionUserMessageContent.parts([
      ChatCompletionMessageContentPart.text(
        text: _getTranscriptionMessage(lastMinute),
      ),
      ...await processImagesForAI(lastImages),
    ]),
  );

  createCompletionStreamWithHandler(
    model: model,
    messages: [systemMessage, userMessage],
    responseFormat: answerQuestionResponseFormat,
    onParsedJson: listener,
    onDone: (fullResponse) {
      _logAnswering(systemPrompt, lastMinute, fullResponse, startTime);
      onDone();
    },
    onError: (_) => onError(),
  );
}

String _getTranscriptionMessage(String lastMinute) {
  return transcriptPrompt.replaceAll("{{TRANSCRIPT}}", lastMinute);
}

String _getQuestionAnsweringSystemPrompt(
  String prompt,
  String interviewTopic,
  bool requiresPersonalInfo,
  String? customUserMessage,
  List<Screenshot> images,
) {
  final basePrompt = images.isNotEmpty
      ? _getScreenshotsQuestionAnsweringPrompt(images)
      : prompt;

  final userProfile = userProfileManager.userProfile!;
  final answerSettings = userProfile.answerSettings!;

  var systemPrompt = basePrompt
      .replaceAll("{{INTERVIEW_TOPIC}}", interviewTopic)
      .replaceAll("{{ANSWER_LENGTH}}", answerSettings.length.prompt)
      .replaceAll("{{RESPONSE_STYLE}}", answerSettings.responseStyle.prompt)
      .replaceAll("{{BEHAVIORAL_ANSWER_STRUCTURE}}",
          answerSettings.behavioralStructure.prompt);

  final personalInfo = userProfile.personalInfoData!;
  if (requiresPersonalInfo && !personalInfo.isEmpty()) {
    systemPrompt +=
        "\nMy personal information is: \n${personalInfo.toPrompt()}";
  }

  final extraInstructions = userProfile.extraInstructions;
  if (extraInstructions != null && extraInstructions.trim().isNotEmpty) {
    systemPrompt += extraInstructionsPrompt.replaceAll(
        "{{EXTRA_INSTRUCTIONS}}", extraInstructions);
  }

  if (customUserMessage != null && customUserMessage.isNotEmpty) {
    systemPrompt += customUserMessagePrompt.replaceAll(
        "{{CUSTOM_USER_MESSAGE}}", customUserMessage);
  }

  final answerLanguage =
      userProfile.answerLanguage ?? UserProfile.defaultLanguage;
  if (answerLanguage != UserProfile.defaultLanguage) {
    systemPrompt +=
        "\nGive the answer in the following language: ${answerLanguage.aiName}";
  }

  if (!answerLanguage.isEnglish) {
    systemPrompt += answerSettings.englishForTechnicalTerms.prompt;
  }

  return systemPrompt;
}

String _getScreenshotsQuestionAnsweringPrompt(List<Screenshot> images) {
  final multipleScreenshotsPrompt =
      images.length > 1 ? multipleScreenshotsInstructions : '';

  return screenshotsQuestionAnsweringPrompt.replaceAll(
      "{{MULTIPLE_SCREENSHOTS_PROMPT}}", multipleScreenshotsPrompt);
}

void _logAnswering(String systemPrompt, String lastMinute, String accumulator,
    DateTime startTime) {
  if (logAnswering) {
    dl('# START answerQuestionStream');
    dl('# systemPrompt: ➡️ $systemPrompt');
    dl('# userMessage: ➡️ $lastMinute');
    dl('# response: ⬅️ $accumulator');
    dl('# duration: ⏱️ ${DateTime.now().difference(startTime)}');
    dl('# END answerQuestionStream');
  }
}
// endregion answerQuestion

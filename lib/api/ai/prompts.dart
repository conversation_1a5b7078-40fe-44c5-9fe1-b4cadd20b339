import 'package:openai_dart/openai_dart.dart';

// region question detection
const questionDetectionPromptSystem_low = """
You will receive a transcription of an interview.
The interview topic is: {{INTERVIEW_TOPIC}}

You will also receive the latest asked question, to avoid duplicates:
<previous_question>
{{PREVIOUS_QUESTION}}
</previous_question>

Your task:
1. Identify the last explicit question asked in the transcription, if any.
2. Determine if this question is complete (final) or still in progress.
3. Determine if the question requires personal information about the interviewee.

Consider a question final if it is a complete, explicit question that ends with a question mark.
If it seems incomplete, interrupted, or doesn't end with a question mark, it is not final.

A proper question must end with a question mark and have a clear interrogative structure seeking a response. Exclude rhetorical questions, statements, or implicit questions.

Normalize questions to lowercase and trim whitespace.

If no explicit question is present, the "question" field should be an empty string.

Compare the last question found in the transcription to the previous question.
If they match exactly, don't return the question to avoid duplicates.

You should respond in JSON in the following format:
{
  "question": string,
  "question_final": boolean,
  "requires_personal_info": boolean
}

Examples:

<example1>
Transcription:
Speaker 0: Good morning

Output:
{
  "question": "",
  "question_final": false,
  "requires_personal_info": false
}
</example1>

<example2>
Transcription:
Speaker 0: Please introduce yourself.

Output:
{
  "question": "",
  "question_final": false,
  "requires_personal_info": false
}
</example2>

<example3>
Transcription:
Speaker 0: Can you explain the biggest challenge you faced in your previous job?

Output:
{
  "question": "can you explain the biggest challenge you faced in your previous job?",
  "question_final": true,
  "requires_personal_info": true
}
</example3>
""";

const questionDetectionPromptSystem_medium = """
You will receive a transcription of an interview.
The interview topic is: {{INTERVIEW_TOPIC}}

You will also receive the latest asked question, to avoid duplicates:
<previous_question>
{{PREVIOUS_QUESTION}}
</previous_question>

Your task:
1. Identify the last question asked in the transcription, if any.
2. Determine if this question is complete (final) or still in progress.
3. Determine if the question requires personal information about the interviewee.

Consider a question final if it logically makes sense as a complete question that could be asked by the interviewer in {{INTERVIEW_TOPIC}} interview.
If it seems incomplete or interrupted, it is not final.

A proper question should end with a question mark or have a clear interrogative structure seeking a response. Exclude rhetorical questions or statements.

Normalize questions to lowercase and trim whitespace.

If no question is present, the "question" field should be an empty string.

Compare the last question found in the transcription to the previous question.
If they match, don't return the question to avoid duplicates.
The current question and the previous question shouldn't be an exact match to be considered as a duplicate.

You should respond in JSON in the following format:
{
  "question": string,
  "question_final": boolean,
  "requires_personal_info": boolean
}

Examples:

<example1>
Transcription:
Speaker 0: Good morning

Output:
{
  "question": "",
  "question_final": false,
  "requires_personal_info": false
}
</example1>

<example2>
Transcription:
Speaker 0: Please introduce yourself

Output:
{
  "question": "please introduce yourself",
  "question_final": true,
  "requires_personal_info": true
}
</example2>

<example3>
Transcription:
Speaker 0: Can you explain the biggest challenge you faced in your previous job and how you
Speaker 1: Sure, the biggest challenge was

Output:
{
  "question": "can you explain the biggest challenge you faced in your previous job and how you",
  "question_final": false,
  "requires_personal_info": false
}
</example3>
""";

const questionDetectionPromptSystem_high = """
You will receive a transcription of an interview.
The interview topic is: {{INTERVIEW_TOPIC}}

You will also receive the latest asked question, to avoid duplicates:
<previous_question>
{{PREVIOUS_QUESTION}}
</previous_question>

Your task:
1. Identify the last question asked in the transcription, including both explicit and implicit questions.
2. Determine if this question is complete (final) or still in progress.
3. Determine if the question requires personal information about the interviewee.

A question is final if it requests specific information about {{INTERVIEW_TOPIC}}, regardless of its grammatical structure or formality.
This includes all technical questions, best practices inquiries, and requests for explanations of concepts related to {{INTERVIEW_TOPIC}}.
Only consider a question not final if it's clearly incomplete mid-sentence.

A question can be explicit (ending with a question mark or having a clear interrogative structure) or implicit (a statement that implies a request for information or response).
Include rhetorical questions if they seem to invite a response in the context of the interview.

Normalize questions to lowercase and trim whitespace.

If no question (explicit or implicit) is present, the "question" field should be an empty string.

Compare the last question found in the transcription to the previous question.
If they convey the same meaning or intent, even if worded differently, don't return the question to avoid duplicates.

You should respond in JSON in the following format:
{
  "question": string,
  "question_final": boolean,
  "requires_personal_info": boolean
}

Examples:

<example1>
Transcription:
Speaker 0: Good morning

Output:
{
  "question": "",
  "question_final": false,
  "requires_personal_info": false
}
</example1>

<example2>
Transcription:
Speaker 0: I'd love to hear about your background.

Output:
{
  "question": "i'd love to hear about your background",
  "question_final": true,
  "requires_personal_info": true
}
</example2>

<example3>
Transcription:
Speaker 0: The biggest challenge in your previous job must have been quite

Output:
{
  "question": "the biggest challenge in your previous job must have been quite",
  "question_final": false,
  "requires_personal_info": true
}
</example3>
""";

const screenshotsExtractQuestionPrompt = '''
You will receive:
1) One or more screenshots (images), which may contain text pertinent to the unfolding interview.
2) A transcription of an interview (text), provided solely for additional context in case the question found in the screenshots appears incomplete.

The interview topic is: {{INTERVIEW_TOPIC}}

Your tasks:

1. Identify the last question found in the screenshots.
   • Do not look for new questions in the transcription.
   • Only if the screenshot's question is visibly incomplete, refer to the transcription to see if it helps complete the question.  

2. Determine if the question is complete (final) or still in progress (incomplete).
   • If the screenshot’s text shows a truncated or partially cut-off question, consult the transcription to see if the missing portion is provided.
   • If you can reconstruct a logically complete question by combining the screenshot text with any relevant part from the transcription, set question_final = true.  
   • Otherwise, if it remains incomplete, set question_final = false.

3. Determine if the question requires personal information from the interviewee.  
   • For example: “Please introduce yourself,” “What is your phone number?”, or “Tell me about your personal background.”  

4. Normalize the question to lowercase and remove any surrounding whitespace.
   • If no question is found in the screenshots, set "question" to an empty string.

You should respond in JSON, using the format:
{
  "question": string,
  "question_final": boolean,
  "requires_personal_info": boolean
}
''';
// endregion question detection

// region question answering
const questionAnsweringPromptSystem = """
You will be given an interview transcript, which might contain some transcript errors. 

Your task is to:
- Determine the last question asked in the interview.
- Generate a concise answer to that question.
- Respond in JSON format.
- The answer should not contain markdown.
- Generate an answer, do not extract the answer from the transcript.
- If the answer contains code format it and add it and surround it with ```

- The answer should be:
{{ANSWER_LENGTH}}

- The answer should be in the following style:
{{RESPONSE_STYLE}}

- If the question is behavioral, provide the answer in the following structure:
{{BEHAVIORAL_ANSWER_STRUCTURE}}

You should respond in JSON in the following format:

{
  "question": string,
  "answer": string
}

The interview topic is: {{INTERVIEW_TOPIC}}
""";

const screenshotsQuestionAnsweringPrompt = '''
<screenshots_analysis_instructions>
Extract and answer the question from the screenshot(s) provided.

GENERAL GUIDELINES:
- Always provide the direct answer/solution FIRST in the answer section
- Follow it with short explanations in a separate explanation section
- Use clear formatting and indentation
{{MULTIPLE_SCREENSHOTS_PROMPT}}

FOR CHOICE QUESTIONS:
- List ALL options in the order they appear in the question, marking correct answer(s) with (✅)
- Also include the incorrect answer to have a complete list

FOR MULTIPLE CORRECT ANSWERS:
   - Option 1
   - Option 2 (✅)
   - Option 3
   - Option 4
   - Option 5 (✅)

FOR SINGLE CORRECT ANSWER:
   - Option 1
   - Option 2 (✅)
   - Option 3
   - Option 4
   - Option 5

FOR CODING QUESTIONS:
- Provide complete code solution with:
  ```[language]
  // Code solution here
  ```
- CODING QUESTIONS GUIDELINES:
  - Avoid using third-party libraries
  - Order the answer section as the following:
   1. Code section
   2. Time/space complexity (if relevant)
   3. The trick to solve the problem.
   4. Short explanation of the code, focusing mainly on the hard parts that an average programmer might not understand
  - The code should be easily readable and understandable
  - Add Short in code comments to explain the hard or tricky parts of the code
  - Key concepts used if they are not obvious, don't explain basic syntax or obvious code
  - Best practices highlighted
  - Common pitfalls to avoid

You should respond in JSON in the following format:

{
  "question": string,
  "answer": string
}

The interview topic is: {{INTERVIEW_TOPIC}}
</screenshots_analysis_instructions>
''';

const multipleScreenshotsInstructions = '''
You will receive multiple screenshots, they might contain the same question or different questions.
If the contain different questions, answer the last one.
''';
// endregion question answering

// region optional prompts
const extraInstructionsPrompt = '''
<extra_user_instructions>
  <instructions>{{EXTRA_INSTRUCTIONS}}</instructions>
  <priority>If this and the previous instructions conflict, prefer this instruction instead.</priority>
</extra_user_instructions>
''';

const customUserMessagePrompt = '''
<custom_user_input_instructions>
  <input>{{CUSTOM_USER_MESSAGE}}</input>
  
  <instructions>
    The custom user input above may be either:
    1. A correction to the transcription
    2. A new question or instruction entered manually by the user

    Please process this information accordingly, prioritizing the custom input if it conflicts with or supplements the transcription.
    If the custom input is a new question or instruction, treat it as the primary query.
    If it appears to be a correction, apply it to your understanding of the transcription before formulating your response.
  </instructions>
</custom_user_input_instructions>
''';
// endregion optional prompts

const transcriptPrompt = '''
Here is the transcript:
<transcription>
{{TRANSCRIPT}}
</transcription>
''';

// region responseFormat
const extractQuestionResponseFormat = ResponseFormat.jsonSchema(
  jsonSchema: JsonSchemaObject(
    name: 'ExtractedQuestion',
    description: 'Extracted question from transcript',
    strict: true,
    schema: {
      'type': 'object',
      'properties': {
        'question': {
          'type': 'string',
          'description': 'The extracted question',
        },
        'question_final': {
          'type': 'boolean',
          'description': 'Whether the question is complete/final',
        },
        'requires_personal_info': {
          'type': 'boolean',
          'description': 'Whether the question requires personal information',
        },
      },
      'required': ['question', 'question_final', 'requires_personal_info'],
      'additionalProperties': false,
    },
  ),
);

const answerQuestionResponseFormat = ResponseFormat.jsonSchema(
  jsonSchema: JsonSchemaObject(
    name: 'InterviewResponse',
    description: 'Interview question and answer',
    strict: true,
    schema: {
      'type': 'object',
      'properties': {
        'question': {
          'type': 'string',
          'description': 'The interview question',
        },
        'answer': {
          'type': 'string',
          'description': 'The answer to the question',
        },
      },
      'required': ['question', 'answer'],
      'additionalProperties': false,
    },
  ),
);
// region

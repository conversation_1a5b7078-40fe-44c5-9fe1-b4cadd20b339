import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:http/http.dart' as http;
import 'package:interview_hammer/api/gemini/audio_utils.dart';
import 'package:interview_hammer/api/gemini/live_event.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

/// A reusable, low-level wrapper around Google Gemini live transcription APIs.
///
/// This class is **deliberately** agnostic of any UI or business-logic details –
/// it only knows how to:
///
/// 1. Establish a WebSocket connection for streaming audio to Gemini 2.x
/// 2. Send the initial setup payload describing the requested model & prompt
/// 3. Stream raw 16-kHz mono PCM chunks as they become available
/// 4. Expose the incoming server messages as a standard Dart [Stream]
/// 5. Perform an optional second-pass transcription through Gemini 2.5 using a
///    regular HTTPS request (this is useful for refinement once a turn ends)
///
/// By moving this functionality into its own class we can reuse it from
/// multiple call-sites (e.g. real-time meeting transcription, interview
/// analysis, etc.) and swap the underlying model without touching business
/// logic.
class GeminiClient {
  GeminiClient._internal({required this.apiKey});

  factory GeminiClient({required String apiKey}) {
    _instance ??= GeminiClient._internal(apiKey: apiKey);
    return _instance!;
  }

  static GeminiClient? _instance;

  static GeminiClient get instance {
    if (_instance == null) {
      throw StateError(
          'GeminiClient has not been initialised. Call GeminiClient(apiKey: ...) first.');
    }
    return _instance!;
  }

  static GeminiClient init({required String apiKey}) {
    _instance ??= GeminiClient._internal(apiKey: apiKey);
    return _instance!;
  }

  final String apiKey;

  WebSocketChannel? _channel;
  StreamSubscription<dynamic>? _wsSub;
  String? _cachedModelName;
  String? _cachedSystemPrompt;
  void Function(GeminiLiveEvent event)? _cachedOnEvent;
  Function(dynamic error)? _cachedOnError;
  Function(int? closeCode, String? closeReason)? _cachedOnDone;
  int _cachedMaxContextTokens = 200;
  int _cachedTargetContextTokens = 100;
  bool _isReconnecting = false;
  bool _manuallyClosed = false;
  bool _initialReadyEventSent = false;

  /// Opens a live WebSocket session and sends the required setup payload.
  ///
  /// The [onEvent] callback receives *high-level* server events.
  Future<void> connectLive({
    required String modelName,
    required String systemPrompt,
    required void Function(GeminiLiveEvent event) onEvent,
    required Function(dynamic error) onError,
    Function(int? closeCode, String? closeReason)? onDone,
    int maxContextTokens = 200,
    int targetContextTokens = 100,
  }) async {
    _cachedModelName = modelName;
    _cachedSystemPrompt = systemPrompt;
    _cachedOnEvent = onEvent;
    _cachedOnError = onError;
    _cachedOnDone = onDone;
    _cachedMaxContextTokens = maxContextTokens;
    _cachedTargetContextTokens = targetContextTokens;

    final String wsEndpoint =
        'wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=$apiKey';

    _channel = WebSocketChannel.connect(Uri.parse(wsEndpoint));
    await _channel!.ready;

    _initialReadyEventSent = false;

    _wsSub = _channel!.stream.listen((event) {
      final String? decodedEvent = decodeEvent(event);
      if (decodedEvent == null) return;

      final Map<String, dynamic> payload =
          unwrapPayload(jsonDecode(decodedEvent));

      final bool setupAck = payload.containsKey('setupResponse') ||
          payload.containsKey('setupComplete');

      String? recognizerText;
      final Map<String, dynamic>? inputTranscriptionData =
          payload['inputTranscription'];
      if (inputTranscriptionData != null) {
        recognizerText = inputTranscriptionData['text'] as String?;
      }

      String? modelText;
      final List<dynamic>? parts =
          payload['modelTurn']?['parts'] as List<dynamic>?;
      if (parts != null && parts.isNotEmpty) {
        final buffer = StringBuffer();
        for (final part in parts) {
          final txt = part['text'] as String? ?? '';
          if (txt.isNotEmpty) buffer.write(txt);
        }
        modelText = buffer.toString();
      }

      final bool turnComplete = payload['turnComplete'] == true;

      final bool hasText =
          (recognizerText?.isNotEmpty == true || modelText?.isNotEmpty == true);

      // `readyForAudioFlag` becomes true exactly once—either when we
      // receive the explicit `setup*` acknowledgement *or* when the first
      // text-bearing frame arrives (some experimental models emit words
      // before sending the setup ack).  Downstream callers (e.g.
      // transcription services) can start streaming microphone audio as
      // soon as they see this flag without worrying about these protocol
      // quirks.
      final bool readyForAudioFlag =
          _shouldSignalReadyForAudio(setupAck, hasText);

      onEvent(GeminiLiveEvent(
        readyForAudio: readyForAudioFlag,
        recognizerText: recognizerText,
        modelText: modelText,
        turnComplete: turnComplete,
      ));
    }, onError: (err) {
      onError(err);
      if (!_manuallyClosed) _restartWebSocket();
    }, onDone: () {
      onDone?.call(_channel?.closeCode, _channel?.closeReason);
      if (!_manuallyClosed) _restartWebSocket();
    }, cancelOnError: false);

    final setupPayload = jsonEncode({
      'setup': {
        'model': modelName,
        'generationConfig': {
          'responseModalities': ['TEXT'],
          'temperature': 0.0,
          'topK': 1,
        },
        'contextWindowCompression': {
          'triggerTokens': maxContextTokens,
          'slidingWindow': {
            'targetTokens': targetContextTokens,
          }
        },
        'inputAudioTranscription': {},
        'systemInstruction': {
          'parts': [
            {'text': systemPrompt}
          ]
        },
      }
    });
    _channel!.sink.add(setupPayload);
  }

  /// Sends a single chunk of **raw** 16-bit little-endian PCM (16-kHz, mono)
  /// data to Gemini Live.
  void sendAudioChunk(Uint8List pcmChunk) {
    if (_channel == null) return;
    final encoded = base64Encode(pcmChunk);
    _channel!.sink.add(jsonEncode({
      'realtimeInput': {
        'audio': {
          'data': encoded,
          'mimeType': 'audio/pcm',
        }
      }
    }));
  }

  /// Performs a second-pass transcription using Gemini 2.5 (non-streaming).
  ///
  /// Provide the **raw** PCM bytes captured for the turn. The helper will wrap
  /// them in a minimal WAVE container because the 2.5 endpoint expects
  /// `audio/wav`.
  Future<String?> generateContentFromAudio(
    Uint8List pcm16kMono, {
    required String refinementModelName,
    required String systemPrompt,
  }) async {
    final Uint8List wavData = pcmToWav(pcm16kMono, sampleRate: 16000);
    final String base64Audio = base64Encode(wavData);

    final Map<String, dynamic> requestBody = {
      'contents': [
        {
          'role': 'user',
          'parts': [
            {'text': systemPrompt},
            {
              'inlineData': {
                'mimeType': 'audio/wav',
                'data': base64Audio,
              }
            }
          ]
        }
      ],
      'generationConfig': {
        'temperature': 0.0,
        'topK': 1,
      },
    };

    final uri = Uri.parse(
        'https://generativelanguage.googleapis.com/v1beta/$refinementModelName:generateContent?key=$apiKey');

    final response = await http
        .post(uri,
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode(requestBody))
        .timeout(const Duration(seconds: 5));

    if (response.statusCode != 200) {
      throw HttpException('Non-200 response: ${response.statusCode}', uri: uri);
    }

    final body = jsonDecode(response.body) as Map<String, dynamic>;
    final List<dynamic>? candidates = body['candidates'] as List<dynamic>?;
    if (candidates != null && candidates.isNotEmpty) {
      final Map<String, dynamic>? content =
          candidates.first['content'] as Map<String, dynamic>?;
      final List<dynamic>? parts = content?['parts'] as List<dynamic>?;
      if (parts != null && parts.isNotEmpty) {
        final String? text = parts.first['text'] as String?;
        return text?.trim();
      }
    }

    return null;
  }

  bool _shouldSignalReadyForAudio(bool setupAck, bool hasText) {
    if (setupAck) return true;

    if (!_initialReadyEventSent && hasText) {
      _initialReadyEventSent = true;
      return true;
    }

    return false;
  }

  Future<void> _restartWebSocket() async {
    if (_manuallyClosed) return;
    if (_isReconnecting) return;
    _isReconnecting = true;

    await _tearDownChannel();

    log('🔄 Gemini WebSocket reconnecting...');

    // Small delay to avoid busy-loop reconnections.
    await Future.delayed(const Duration(milliseconds: 500));

    final model = _cachedModelName;
    final prompt = _cachedSystemPrompt;
    final evt = _cachedOnEvent;
    final err = _cachedOnError;
    final maxTokens = _cachedMaxContextTokens;
    final targetTokens = _cachedTargetContextTokens;

    if (model != null && prompt != null && evt != null && err != null) {
      try {
        await connectLive(
          modelName: model,
          systemPrompt: prompt,
          onEvent: evt,
          onError: err,
          onDone: _cachedOnDone,
          maxContextTokens: maxTokens,
          targetContextTokens: targetTokens,
        );
      } catch (e, st) {
        log('Reconnect attempt failed: $e', stackTrace: st);
      }
    }

    _isReconnecting = false;
  }

  Future<void> close() async {
    _manuallyClosed = true;
    await _tearDownChannel();
  }

  /// Cleanly tear-down the current WebSocket connection and listener.
  Future<void> _tearDownChannel() async {
    try {
      await _wsSub?.cancel();
    } catch (_) {}
    _wsSub = null;

    try {
      await _channel?.sink.close();
    } catch (_) {}

    _channel = null;
    _initialReadyEventSent = false;
  }
}

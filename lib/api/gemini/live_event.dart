class GeminiLiveEvent {
  const GeminiLiveEvent({
    required this.readyForAudio,
    required this.turnComplete,
    this.recognizerText,
    this.modelText,
  });

  final bool readyForAudio;
  final String? recognizerText;
  final String? modelText;
  final bool turnComplete;

  bool get hasRecognizerText => (recognizerText?.isNotEmpty ?? false);

  bool get hasModelText => (modelText?.isNotEmpty ?? false);
}

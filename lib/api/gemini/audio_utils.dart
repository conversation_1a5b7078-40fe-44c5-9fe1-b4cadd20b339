import 'dart:convert' show utf8, ascii;
import 'dart:convert';
import 'dart:typed_data';

/// Collection of helper utilities shared between AI clients (Gemini, Vertex, etc.).
///
/// These utilities are intentionally kept free-standing (instead of being mixed
/// into a class) so that they can be imported by both `dart:io` as well as
/// Flutter packages without tight coupling or inheritance constraints.
///
/// Keeping them in a dedicated file avoids duplication across the individual
/// client implementations and makes unit-testing easier.

// Interprets a single WebSocket frame and converts it into a UTF-8 string when
// necessary. Returns `null` for frames that cannot be decoded.
String? decodeEvent(dynamic event) {
  if (event is String) return event;
  if (event is List<int>) return utf8.decode(event);
  return null;
}

// Some Google streaming payloads wrap the actual server message inside a
// `serverContent` envelope. This helper unwraps that layer so that the caller
// can treat both shapes in a uniform way.
Map<String, dynamic> unwrapPayload(Map<String, dynamic> data) {
  return data.containsKey('serverContent')
      ? (data['serverContent'] as Map<String, dynamic>?) ?? {}
      : data;
}

// Converts **raw** 16-bit PCM (little-endian, mono) captured at `sampleRate`
// into a minimal WAVE container. The resulting byte array can be fed directly
// into Gemini / Vertex "non-streaming" endpoints that expect `audio/wav`.
Uint8List pcmToWav(Uint8List pcmBytes, {required int sampleRate}) {
  const int bitsPerSample = 16;
  const int numChannels = 1;

  final int byteRate = sampleRate * numChannels * bitsPerSample ~/ 8;
  final int blockAlign = numChannels * bitsPerSample ~/ 8;
  final int dataLength = pcmBytes.lengthInBytes;
  final int riffChunkSize = 36 + dataLength;

  final BytesBuilder builder = BytesBuilder();
  // RIFF header
  builder.add(ascii.encode('RIFF'));
  builder.add(_intToBytesLE(riffChunkSize, 4));
  builder.add(ascii.encode('WAVE'));

  // fmt  sub-chunk
  builder.add(ascii.encode('fmt '));
  builder.add(_intToBytesLE(16, 4)); // Subchunk1Size (16 for PCM)
  builder.add(_intToBytesLE(1, 2)); // AudioFormat (1 = PCM)
  builder.add(_intToBytesLE(numChannels, 2));
  builder.add(_intToBytesLE(sampleRate, 4));
  builder.add(_intToBytesLE(byteRate, 4));
  builder.add(_intToBytesLE(blockAlign, 2));
  builder.add(_intToBytesLE(bitsPerSample, 2));

  // data sub-chunk
  builder.add(ascii.encode('data'));
  builder.add(_intToBytesLE(dataLength, 4));
  builder.add(pcmBytes);

  return builder.takeBytes();
}

Uint8List _intToBytesLE(int value, int byteCount) {
  final Uint8List bytes = Uint8List(byteCount);
  final ByteData byteData = bytes.buffer.asByteData();
  if (byteCount == 2) {
    byteData.setUint16(0, value, Endian.little);
  } else if (byteCount == 4) {
    byteData.setUint32(0, value, Endian.little);
  } else {
    throw ArgumentError('Unsupported byteCount: $byteCount');
  }
  return bytes;
}

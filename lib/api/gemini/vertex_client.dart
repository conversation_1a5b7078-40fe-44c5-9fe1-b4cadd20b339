import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:googleapis_auth/auth_io.dart' as auth;
import 'package:http/http.dart' as http;
import 'package:interview_hammer/api/gemini/audio_utils.dart';
import 'package:interview_hammer/api/gemini/live_event.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

/// A minimal Vertex AI client that mirrors the public interface of [GeminiClient].
///
/// Vertex currently exposes its generative models primarily through HTTP (REST)
/// endpoints rather than WebSockets.  To keep the call-sites identical we still
/// expose `connectLive`, `sendAudioChunk`, `generateContentFromAudio`, and
/// `close`, but the live-streaming methods are implemented as *no-ops* that
/// immediately surface an `UnsupportedError` via the provided callbacks.  This
/// guarantees **compile-time compatibility** while making the behavioural
/// difference explicit to the caller.
///
/// The second-pass transcription (`generateContentFromAudio`) is fully
/// functional and uses the `generateContent` REST endpoint behind the scenes.
class VertexClient {
  VertexClient._internal({
    required this.projectId,
    required this.accessToken,
    required this.location,
  });

  static VertexClient? _instance;

  static VertexClient get instance {
    if (_instance == null) {
      throw StateError(
          'VertexClient has not been initialised. Call initFromServiceAccountJson() first.');
    }
    return _instance!;
  }

  /// Initialises the singleton from a raw *service-account* JSON payload.
  static Future<VertexClient> init(
    String serviceAccountJson, {
    String location = 'global',
    List<String> scopes = const [
      'https://www.googleapis.com/auth/cloud-platform',
    ],
  }) async {
    if (_instance != null) return _instance!;

    // Obtain `projectId` and `accessToken` via the dedicated helper.
    final authResult =
        await _authenticateServiceAccount(serviceAccountJson, scopes);

    _instance = VertexClient._internal(
      projectId: authResult.projectId,
      accessToken: authResult.accessToken,
      location: location,
    );

    return _instance!;
  }

  final String projectId;

  /// OAuth2 bearer token with at least the `https://www.googleapis.com/auth/cloud-platform` scope.
  ///
  /// In production you would typically obtain this from `googleapis_auth` or
  /// Application-Default Credentials (ADC).  The token is injected here so that
  /// this low-level helper stays agnostic of the chosen auth mechanism.
  final String accessToken;

  /// Region / location used when calling Vertex. Defaults to `global`.
  final String location;

  // Underlying WebSocket channel for live streaming (initially null).
  WebSocketChannel? _channel;

  // Tracks whether the initial `readyForAudio` event has already been emitted.
  bool _initialReadyEventSent = false;

  String? _cachedModelName;
  String? _cachedSystemPrompt;
  void Function(GeminiLiveEvent event)? _cachedOnEvent;
  Function(dynamic error)? _cachedOnError;
  Function(int? closeCode, String? closeReason)? _cachedOnDone;
  bool _isReconnecting = false;

  Future<void> connectLive({
    required String modelName,
    required String systemPrompt,
    required void Function(GeminiLiveEvent event) onEvent,
    required Function(dynamic error) onError,
    Function(int? closeCode, String? closeReason)? onDone,
  }) async {
    _cachedModelName = modelName;
    _cachedSystemPrompt = systemPrompt;
    _cachedOnEvent = onEvent;
    _cachedOnError = onError;
    _cachedOnDone = onDone;

    try {
      // Live streaming is currently only supported in the "us-central1" region
      const liveRegion = 'us-central1';

      final String wsEndpoint =
          'wss://$liveRegion-aiplatform.googleapis.com/ws/google.cloud.aiplatform.v1beta1.LlmBidiService/BidiGenerateContent';

      _channel = IOWebSocketChannel.connect(
        Uri.parse(wsEndpoint),
        headers: {
          'Authorization': 'Bearer $accessToken',
        },
      );

      // Wait until the TCP/WebSocket handshake completes to avoid racing with
      // subsequent `sink.add`.
      await _channel!.ready;

      // Reset readiness tracking whenever we establish a new connection.
      _initialReadyEventSent = false;

      _channel!.stream.listen(
        (event) {
          final String? decoded = decodeEvent(event);
          _compactPrintWsEvent(decoded);
          if (decoded == null) return;

          final Map<String, dynamic> payload =
              unwrapPayload(jsonDecode(decoded));

          final bool setupAck = payload.containsKey('setupResponse') ||
              payload.containsKey('setupComplete');

          String? recognizerText;
          final Map<String, dynamic>? inputTranscriptionData =
              payload['inputTranscription'];
          if (inputTranscriptionData != null) {
            recognizerText = inputTranscriptionData['text'] as String?;
          }

          String? modelText;
          final List<dynamic>? parts =
              payload['modelTurn']?['parts'] as List<dynamic>?;
          if (parts != null && parts.isNotEmpty) {
            final buffer = StringBuffer();
            for (final part in parts) {
              final txt = part['text'] as String? ?? '';
              if (txt.isNotEmpty) buffer.write(txt);
            }
            modelText = buffer.toString();
          }

          final bool turnComplete = payload['turnComplete'] == true;

          final bool hasText = (recognizerText?.isNotEmpty == true ||
              modelText?.isNotEmpty == true);

          final bool readyForAudioFlag =
              _shouldSignalReadyForAudio(setupAck, hasText);

          onEvent(GeminiLiveEvent(
            readyForAudio: readyForAudioFlag,
            recognizerText: recognizerText,
            modelText: modelText,
            turnComplete: turnComplete,
          ));
        },
        onError: (err) {
          onError(err);
          _restartWebSocket();
        },
        onDone: () {
          onDone?.call(_channel?.closeCode, _channel?.closeReason);
          _restartWebSocket();
        },
        cancelOnError: false,
      );

      // Send the initial "setup" frame – identical structure to Gemini Live
      // but routed through Vertex AI backend.
      final String qualifiedModelName = modelName.startsWith('projects/')
          ? modelName
          : 'projects/$projectId/locations/$liveRegion/publishers/google/models/$modelName';

      final setupPayload = jsonEncode({
        'setup': {
          'model': qualifiedModelName,
          'generationConfig': {
            'responseModalities': ['TEXT'],
            'temperature': 0.0,
            'topK': 1,
          },
          'inputAudioTranscription': {},
          'systemInstruction': {
            'parts': [
              {'text': systemPrompt}
            ]
          },
        }
      });
      _channel!.sink.add(setupPayload);
    } catch (e) {
      onError(e);
      _restartWebSocket();
    }
  }

  // Streams raw 16-kHz PCM bytes to the WebSocket as base64 payloads.
  void sendAudioChunk(Uint8List pcmChunk) {
    if (_channel == null) return;
    final encoded = base64Encode(pcmChunk);
    _channel!.sink.add(jsonEncode({
      'realtimeInput': {
        'media_chunks': [
          {
            'data': encoded,
            'mimeType': 'audio/pcm;rate=16000',
          }
        ]
      }
    }));
  }

  /// Performs non-streaming transcription using Vertex `generateContent`.
  ///
  /// The method converts the raw PCM into a WAV container and sends it alongside
  /// the provided [systemPrompt].  It returns the first textual candidate or
  /// `null` on failure.
  Future<String?> generateContentFromAudio(
    Uint8List pcm16kMono, {
    required String refinementModelName,
    required String systemPrompt,
  }) async {
    final Uint8List wavData = pcmToWav(pcm16kMono, sampleRate: 16000);
    final String base64Audio = base64Encode(wavData);

    final Map<String, dynamic> requestBody = {
      'contents': [
        {
          'role': 'user',
          'parts': [
            {'text': systemPrompt},
            {
              'inlineData': {
                'mimeType': 'audio/wav',
                'data': base64Audio,
              }
            }
          ]
        }
      ],
      'generationConfig': {
        'responseModalities': ['TEXT'],
        'temperature': 0.0,
        'topK': 1,
      },
    };

    final String url =
        'https://aiplatform.googleapis.com/v1/projects/$projectId/locations/$location/publishers/google/models/$refinementModelName:generateContent';

    final response = await http
        .post(Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $accessToken',
            },
            body: jsonEncode(requestBody))
        .timeout(const Duration(seconds: 30));

    if (response.statusCode != 200) {
      throw HttpException('Non-200 response: ${response.statusCode}',
          uri: Uri.parse(url));
    }

    final Map<String, dynamic> body = jsonDecode(response.body);
    final List<dynamic>? candidates = body['candidates'] as List<dynamic>?;
    if (candidates != null && candidates.isNotEmpty) {
      final Map<String, dynamic>? content =
          candidates.first['content'] as Map<String, dynamic>?;
      final List<dynamic>? parts = content?['parts'] as List<dynamic>?;
      if (parts != null && parts.isNotEmpty) {
        final String? text = parts.first['text'] as String?;
        return text?.trim();
      }
    }

    return null;
  }

  Future<void> close() async {
    await _channel?.sink.close();
  }

  // Determines whether we should emit a `readyForAudio` flag based on the first
  // piece of content or an explicit server acknowledgment. Ensures the flag is
  // sent exactly once per connection unless triggered again by a formal
  // `setupAck` from the backend.
  bool _shouldSignalReadyForAudio(bool setupAck, bool hasText) {
    if (setupAck) return true;

    if (!_initialReadyEventSent && hasText) {
      _initialReadyEventSent = true;
      return true;
    }

    return false;
  }

  /// Prints incoming Vertex/Gemini WebSocket events in a compact, human-readable
  /// form.
  ///
  /// • Non-textual events (e.g. `generationComplete`, `turnComplete`, usage
  ///   metadata) are logged on a **single line** prefixed by their primary key.
  ///
  /// • Text-bearing events (`inputTranscription` and `modelTurn`) are rendered on
  ///   **two lines**: the first line states the event type while the second line
  ///   shows the associated text payload.
  ///
  /// This keeps the console output succinct while still surfacing the most
  /// relevant information during development/debugging.
  void _compactPrintWsEvent(String? rawJson) {
    if (rawJson == null) return;
    try {
      final Map<String, dynamic> payload = jsonDecode(rawJson);

      // Vertex wraps the meaningful data under `serverContent`.
      final Map<String, dynamic>? serverContent =
          payload['serverContent'] as Map<String, dynamic>?;

      if (serverContent != null) {
        if (serverContent.containsKey('inputTranscription')) {
          final Map<String, dynamic> data =
              serverContent['inputTranscription'] as Map<String, dynamic>;
          final String? text = data['text'] as String?;
          log('[inputTranscription]\n$text');
          return;
        }

        if (serverContent.containsKey('modelTurn')) {
          final Map<String, dynamic> modelTurn =
              serverContent['modelTurn'] as Map<String, dynamic>;
          final List<dynamic>? parts = modelTurn['parts'] as List<dynamic>?;
          final String text = parts != null && parts.isNotEmpty
              ? parts
                  .map((e) =>
                      (e as Map<String, dynamic>)['text'] as String? ?? '')
                  .join('')
              : '';
          log('[modelTurn]\n$text');
          return;
        }

        // For any other non-textual events, print the available keys on one line.
        if (serverContent.isNotEmpty) {
          log('[${serverContent.keys.join(', ')}]');
          return;
        }
      }

      // Fallback – just print the top-level keys to avoid flooding the console.
      log('[event] ${payload.keys.join(', ')}');
    } catch (_) {
      // If parsing fails, fall back to raw printing.
      log('[raw] $rawJson');
    }
  }

  Future<void> _restartWebSocket() async {
    if (_isReconnecting) return;
    _isReconnecting = true;
    log('🔄 Vertex WebSocket reconnecting...');
    try {
      await _channel?.sink.close();
    } catch (_) {}
    _channel = null;
    _initialReadyEventSent = false;

    await Future.delayed(const Duration(milliseconds: 500));

    final model = _cachedModelName;
    final prompt = _cachedSystemPrompt;
    final evt = _cachedOnEvent;
    final err = _cachedOnError;

    if (model != null && prompt != null && evt != null && err != null) {
      unawaited(connectLive(
          modelName: model,
          systemPrompt: prompt,
          onEvent: evt,
          onError: err,
          onDone: _cachedOnDone));
    }

    _isReconnecting = false;
  }
}

/// Holds the outcome of the service-account authentication flow.
class _ServiceAccountAuthResult {
  const _ServiceAccountAuthResult(
      {required this.projectId, required this.accessToken});

  final String projectId;
  final String accessToken;
}

/// Parses a service-account JSON payload and exchanges it for an OAuth2
/// access token, returning both the token and the associated project ID.
Future<_ServiceAccountAuthResult> _authenticateServiceAccount(
    String serviceAccountJson, List<String> scopes) async {
  final Map<String, dynamic> jsonMap = jsonDecode(serviceAccountJson);

  // Convert into strongly-typed credentials understood by googleapis_auth.
  final auth.ServiceAccountCredentials credentials =
      auth.ServiceAccountCredentials.fromJson(jsonMap);

  // Perform the JWT → access-token exchange.
  final auth.AutoRefreshingAuthClient client =
      await auth.clientViaServiceAccount(credentials, scopes);

  final String accessToken = client.credentials.accessToken.data;
  final String projectId = jsonMap['project_id'] as String;

  client.close();

  return _ServiceAccountAuthResult(
    projectId: projectId,
    accessToken: accessToken,
  );
}

import 'dart:convert';
import 'dart:developer';

import 'package:shared_preferences/shared_preferences.dart';

Future<String> getModel(
  String model, [
  int numberOfModels = 3,
]) async {
  SharedPreferences prefs = await SharedPreferences.getInstance();

  Map<String, dynamic> lastModelCalledMap = {};
  String? lastModelCalledJson = prefs.getString('lastModelCalled');
  if (lastModelCalledJson != null) {
    lastModelCalledMap = jsonDecode(lastModelCalledJson);
  }

  int lastModelCalled = lastModelCalledMap[model] ?? -1;

  if (lastModelCalled < numberOfModels - 1) {
    lastModelCalled++;
  } else {
    lastModelCalled = 0;
  }

  lastModelCalledMap[model] = lastModelCalled;
  await prefs.setString('lastModelCalled', jsonEncode(lastModelCalledMap));

  final provider = "${model.split("/").first}$lastModelCalled";
  return "$provider/${model.split("/").last}";
}

String getLastLines(String text, int lines) {
  List<String> linesList = text.split('\n');
  int start = linesList.length - lines;
  if (start < 0) {
    start = 0;
  }
  return linesList.sublist(start).join('\n');
}

String getLastTranscriptByDuration(String text, int durationSeconds) {
  // Approximate characters per second for speech transcription
  // Based on average speaking rate of 150-160 words per minute
  // With average word length of 5 characters plus spaces
  const avgCharsPerSecond = 15;

  final characterCount = durationSeconds * avgCharsPerSecond;
  return getLastMinute(text, characterCount);
}

String getLastMinute(String text, int lastMinuteDuration) {
  String last1000;
  if (text.length >= lastMinuteDuration) {
    int cutPoint = text.length - lastMinuteDuration;
    int lastSpace = text.indexOf(' ', cutPoint);
    if (lastSpace == -1) {
      last1000 = text.substring(cutPoint);
    } else {
      last1000 = text.substring(lastSpace + 1);
    }
  } else {
    last1000 = text;
  }

  return last1000.trim();
}

dynamic extractJson(String input) {
  String startDelimiter = "```json";
  String endDelimiter = "```";

  int startIndex = input.indexOf(startDelimiter);
  int endIndex =
      input.indexOf(endDelimiter, startIndex + startDelimiter.length);

  String jsonString;
  if (startIndex == -1 || endIndex == -1) {
    // If the delimiters are not found, assume the input is plain JSON
    jsonString = input.trim();
  } else {
    jsonString =
        input.substring(startIndex + startDelimiter.length, endIndex).trim();
  }

  try {
    return jsonDecode(jsonString);
  } catch (e) {
    log('Invalid JSON: $input');
    throw FormatException('Invalid JSON: $e');
  }
}

dynamic extractJsonFromLLMOutput(String input) {
  String startDelimiter = "<format>";
  String endDelimiter = "</format>";

  int startIndex = input.indexOf(startDelimiter);
  int endIndex =
      input.indexOf(endDelimiter, startIndex + startDelimiter.length);

  String jsonString;
  if (startIndex == -1 || endIndex == -1) {
    // If the delimiters are not found, assume the input is plain JSON
    jsonString = input.trim();
  } else {
    jsonString =
        input.substring(startIndex + startDelimiter.length, endIndex).trim();
  }

  try {
    return jsonDecode(jsonString);
  } catch (e) {
    log('Invalid JSON: $input');
    throw FormatException('Invalid JSON: $e');
  }
}

dynamic extractLastJsonObjectFromString(String input) {
  RegExp regExp = RegExp(r'\{[^{}]*\}', multiLine: true);
  Iterable<Match> matches = regExp.allMatches(input);

  if (matches.isEmpty) {
    log('input: $input');
    throw FormatException('No JSON object found');
  }

  String jsonString = matches.last.group(0)!.trim();

  try {
    return jsonDecode(jsonString);
  } catch (e) {
    log('input: $input');
    throw FormatException('Invalid JSON: $e');
  }
}

extension PrettyJson on Object {
  String toPrettyString() {
    var jsonObject = jsonDecode(jsonEncode(this));
    var encoder = JsonEncoder.withIndent("     ");
    return encoder.convert(jsonObject);
  }
}

void logList(List<String> list) {
  String output = 'const examples = [\n';
  for (var i = 0; i < list.length; i++) {
    output += '// ${i + 1}\n';
    output += '  """\n${list[i]}\n""",\n';
  }
  output += '];';
  log(output);
}

/// Attempts to parse a JSON string, fixing it if it's incomplete
/// Returns a Map of the parsed JSON or empty Map if parsing fails
dynamic parseIncompleteJson(String input) {
  try {
    // First attempt to parse as-is
    return jsonDecode(input);
  } catch (e) {
    if (e is FormatException) {
      // If it's a format error, try to fix and parse again
      return jsonDecode(_fixIncompleteJson(input));
    }
    return {};
  }
}

/// Attempts to fix incomplete JSON strings
/// Handles cases like missing quotes, missing braces, and incomplete values
String _fixIncompleteJson(String input) {
  // Clean up whitespace
  input = input.trim();

  if (input.isEmpty) {
    return '{}';
  }

  // Ensure the input starts with an opening brace
  if (!input.startsWith('{')) {
    input = '{$input}';
  }

  // Handle different ending scenarios
  if (!input.endsWith('}') && !input.endsWith('"')) {
    if (input.trim().endsWith(':')) {
      // If it ends with a colon, add an empty string value
      input = '$input""}';
    } else {
      // If it ends with an incomplete value, close the quote and object
      input = '$input"}';
    }
  } else if (!input.endsWith('}')) {
    // If it ends with a quote but no brace, add the closing brace
    input = '$input}';
  }

  // This regex handles key-value pairs in JSON with the following pattern:
  // Group 1 ("\w+"): Matches the key in quotes (e.g., "key")
  // Group 2 (:): Matches the colon separator
  // Group 3 (\s*): Matches any whitespace after the colon (optional)
  // Group 4 ([^",}\s][^,}]*)?: Matches the value if present, excluding quotes and delimiters
  // Group 5 ([,}]|$): Matches the delimiter (comma or closing brace) or end of string
  //
  // Examples of what it matches:
  // "key":"value"    → groups: ("key"), :, (), value, ""
  // "key": "value"   → groups: ("key"), :, ( ), value, ""
  // "key":           → groups: ("key"), :, (), null, ""
  // "key": 123       → groups: ("key"), :, ( ), 123, ""
  input = input.replaceAllMapped(
    RegExp(r'("\w+"):(\s*)([^",}\s][^,}]*)?([,}]|$)'),
    (match) {
      final key = match.group(1); // The key with quotes
      final spaces = match.group(2) ?? ''; // Any spaces after the colon
      final value = match.group(3); // The value if present
      final delimiter = match.group(4) ?? '}'; // The delimiter or end

      // Case 1: No value or empty value after colon
      if (value == null || value.isEmpty) {
        return '$key:$spaces""$delimiter';
      }

      // Case 2: Value exists but isn't properly quoted
      if (!value.startsWith('"')) {
        return '$key:$spaces"$value"$delimiter';
      }

      // Case 3: Value is already properly formatted
      return match.group(0)!;
    },
  );

  // Ensure proper closing of the JSON object
  if (!input.endsWith('}')) {
    input = '$input}';
  }

  // Validate the fixed JSON
  try {
    jsonDecode(input);
    return input;
  } catch (e) {
    return '{}';
  }
}

Future<void> retryOperation(Future<void> Function() operation,
    {required int retries, required Duration delay}) async {
  for (var i = 0; i < retries; i++) {
    try {
      await operation();
      break; // If operation is successful, break the loop
    } catch (e) {
      if (i == retries - 1) {
        log('Operation failed after $retries retries.');
      } else {
        log('Retrying in ${delay.inSeconds} seconds...');
        await Future.delayed(delay);
      }
    }
  }
}

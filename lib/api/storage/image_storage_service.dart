import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/api/management/user_profile_manager.dart';

import '../../utils/events/events.dart';

class ImageStorageService {
  static final ImageStorageService _instance = ImageStorageService._internal();

  static ImageStorageService get instance => _instance;

  ImageStorageService._internal();

  // Private members
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Cache to store image URLs to avoid redundant uploads
  final Map<String, String> _imageUrlCache = {};

  // Track ongoing uploads to prevent duplicate upload attempts
  final Set<String> _ongoingUploads = {};

  // Public methods
  /// Gets the best available URL for an image and initiates background upload if needed.
  /// Returns either a cached storage URL if available, or a base64 data URL.
  String getBestImageUrl(Uint8List imageBytes) {
    final imageHash = _calculateImageHash(imageBytes);

    // If we have a cached URL, use it
    if (_isImageInCache(imageHash)) {
      return _getCachedImageUrl(imageHash)!;
    }

    // Otherwise use base64 and start background upload if needed
    _uploadImageIfNeeded(imageBytes, imageHash);
    return getBase64Image(imageBytes);
  }

  String getBase64Image(Uint8List imageBytes) {
    return 'data:image/png;base64,${base64Encode(imageBytes)}';
  }

  /// Uploads an image and returns its download URL.
  /// Returns null if upload fails.
  Future<String?> uploadScreenshot(Uint8List imageBytes) async {
    final imageHash = _calculateImageHash(imageBytes);

    // Check cache first
    if (_isImageInCache(imageHash)) {
      return _getCachedImageUrl(imageHash);
    }

    try {
      final userId = userProfileManager.userProfile?.uid ?? 'anonymous';
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final imageId = '${timestamp}_${imageHash.substring(0, 16)}';
      final storageRef =
          _storage.ref().child('userImages/$userId/$imageId.png');

      // Set metadata with proper content type
      final metadata = SettableMetadata(
        contentType: 'image/png',
        customMetadata: {
          'uploaded': DateTime.now().toString(),
          'userHash': imageHash.substring(0, 8),
        },
      );

      // Upload the image with metadata
      await storageRef.putData(imageBytes, metadata);

      // Get the download URL and cache it for future use
      final downloadUrl = await storageRef.getDownloadURL();
      _imageUrlCache[imageHash] = downloadUrl;

      return downloadUrl;
    } catch (e) {
      logError('Screenshot upload failed: $e');
      return null;
    }
  }

  // Private methods
  String _calculateImageHash(Uint8List imageBytes) {
    final digest = sha256.convert(imageBytes);
    return digest.toString();
  }

  bool _isImageInCache(String imageHash) {
    return _imageUrlCache.containsKey(imageHash);
  }

  String? _getCachedImageUrl(String imageHash) {
    return _imageUrlCache[imageHash];
  }

  bool _isUploading(String imageHash) {
    return _ongoingUploads.contains(imageHash);
  }

  void _uploadImageIfNeeded(Uint8List imageBytes, String imageHash) {
    if (!_isUploading(imageHash) && !_isImageInCache(imageHash)) {
      _uploadImageInBackground(imageBytes, imageHash);
    }
  }

  void _uploadImageInBackground(Uint8List imageBytes, String imageHash) {
    // Add to ongoing uploads set
    _ongoingUploads.add(imageHash);

    // Fire and forget upload
    Future(() async {
      try {
        final userId = userProfileManager.userProfile?.uid ?? 'anonymous';
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final imageId = '${timestamp}_${imageHash.substring(0, 16)}';
        final storageRef =
            _storage.ref().child('userImages/$userId/$imageId.png');

        // Set metadata with proper content type
        final metadata = SettableMetadata(
          contentType: 'image/png',
          customMetadata: {
            'uploaded': DateTime.now().toString(),
            'userHash': imageHash.substring(0, 8),
          },
        );

        // Upload the image with metadata
        await storageRef.putData(imageBytes, metadata);

        // Get the download URL and cache it for future use
        final downloadUrl = await storageRef.getDownloadURL();
        _imageUrlCache[imageHash] = downloadUrl;

        d("Background upload took ${DateTime.now().millisecondsSinceEpoch - timestamp} ms");
      } catch (e) {
        logError('Background image upload failed: $e');
        // No fallback needed since we already used base64
      } finally {
        // Remove from ongoing uploads set
        _ongoingUploads.remove(imageHash);
      }
    });
  }
}

import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/api/management/remote_config.dart';
import 'package:record/record.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class DeepgramTranscriptionService {
  final apiKey = remoteConfig.getString(KEY_DG_API_KEY);

  final List<String> transcriptionSteps = [];
  final List<Map<int, String>> _whispersDiarized = [{}];

  final _recorder = AudioRecorder();
  WebSocketChannel? _deepgramChannel;
  StreamSubscription? _recorderSubscription;

  DeepgramTranscriptionService();

  Future<void> setupDeepgram({
    required Function(String) interimCallback,
    required Function(String) endpointCallback,
    required Function(dynamic) onError,
    required Function(int? closeCode, String? closeReason) onDone,
    List<String>? languages,
  }) async {
    final serverUrl = _buildServerUrl(languages);
    d("apiKey = ${apiKey}");
    _deepgramChannel = WebSocketChannel.connect(
      Uri.parse(serverUrl),
      protocols: ['token', apiKey],
    );

    await _deepgramChannel!.ready;

    _deepgramChannel!.stream.listen(
      (event) {
        _parseTranscript(
          event,
          interimCallback: interimCallback,
          endpointCallback: endpointCallback,
        );
      },
      onError: onError,
      onDone: () =>
          onDone(_deepgramChannel?.closeCode, _deepgramChannel?.closeReason),
      cancelOnError: false,
    );

    // must match the config in the deepgram api in [serverUrl]
    final recorderStream = await _recorder.startStream(RecordConfig(
      encoder: AudioEncoder.pcm16bits,
      numChannels: 1,
      sampleRate: 16000,
      bitRate: 32000,
    ));

    _recorderSubscription = recorderStream.listen(
      (data) => _deepgramChannel?.sink.add(data),
      onError: onError,
    );
  }

  void _parseTranscript(
    dynamic event, {
    required void Function(String) interimCallback,
    required void Function(String) endpointCallback,
  }) {
    // log('Event from Stream: $event');
    final parsedJson = jsonDecode(event);

    if (parsedJson['channel'] == null ||
        parsedJson['channel']['alternatives'] == null) {
      return;
    }

    final data = parsedJson['channel']['alternatives'][0];
    final transcript = data['transcript'];
    final speechFinal = parsedJson['is_final']; // Interim Results
    final isEndpoint = parsedJson['speech_final']; // Endpoint

    // construct diarized transcript
    if (transcript.length > 0) {
      // d('~~Transcript: $transcript ~ speechFinal: $speechFinal');
      Map<int, String> bySpeaker = {};
      data['words'].forEach((word) {
        int speaker = word['speaker'];
        var currentSpeakerTranscript = bySpeaker[speaker] ?? '';
        bySpeaker[speaker] =
            '${currentSpeakerTranscript + word['punctuated_word']} ';
      });

      // This is step 1 for diarization, but, sometimes "Speaker 1: Hello how"
      //   but it says it's the previous speaker (e.g. speaker 0), but in the next stream it fixes the transcript, and says it's speaker 1.
      // d(bySpeaker.toString());
      if (speechFinal) {
        _whispersDiarized.last.addAll(bySpeaker);
        _whispersDiarized.add({});

        final buildTranscript = _buildDiarizedTranscriptMessage();
        interimCallback(buildTranscript);
        // d('~~Transcript: $transcript ~ speechFinal: $speechFinal');

        if (isEndpoint) {
          endpointCallback(buildTranscript);

          final lastSpeaker = data['words'].last['speaker'];
          if (lastSpeaker == 0) {
            transcriptionSteps.add(buildTranscript);
          }
          // log('[lastSpeaker: $lastSpeaker] Transcript: ${getLastMinute(buildTranscript, 100)}');
        }
      } else {
        // interimCallback(transcript, bySpeaker);
      }
    }
  }

  String _buildDiarizedTranscriptMessage() {
    int totalSpeakers = _whispersDiarized
        .map((e) => e.keys.isEmpty ? 0 : ((e.keys).max + 1))
        .reduce((value, element) => value > element ? value : element);

    // d('Speakers count: $totalSpeakers');

    String transcript = '';
    for (int partIdx = 0; partIdx < _whispersDiarized.length; partIdx++) {
      var part = _whispersDiarized[partIdx];
      if (part.isEmpty) continue;
      for (int speaker = 0; speaker < totalSpeakers; speaker++) {
        if (part.containsKey(speaker)) {
          // This part and previous have only 1 speaker, and is the same
          if (partIdx > 0 &&
              _whispersDiarized[partIdx - 1].containsKey(speaker) &&
              _whispersDiarized[partIdx - 1].length == 1 &&
              part.length == 1) {
            transcript += '${part[speaker]!} ';
          } else {
            transcript += 'Speaker $speaker: ${part[speaker]!} ';
          }
        }
      }
      transcript += '\n';
    }
    return transcript;
  }

  Future stop() {
    return _recorder.stop();
  }

  void clear() {
    transcriptionSteps.clear();
    _whispersDiarized.clear();
    _whispersDiarized.add({});
  }

  Future<void> close() async {
    _deepgramChannel?.sink.close();
    _recorderSubscription?.cancel();

    // dispose throws MissingPluginException, this is an issue in the plugin itself and is not harmful as far as I can tell.
    await _recorder.dispose();
  }

  /// Builds Deepgram websocket URL based on the selected languages.
  ///
  /// If the user selected multiple languages or the explicit MULTI option,
  /// we send `language=multi` to enable multilingual code switching (nova-2/nova-3).
  /// Otherwise we send the single language code (defaulting to `en`).
  String _buildServerUrl(List<String>? languages) {
    // Languages supported by Nova-3 model (for single language mode)
    final nova3SupportedLanguages = {'en', 'en-US'};

    // Base URL with audio settings (must match RecordConfig below)
    String url = "wss://api.deepgram.com/v1/listen?"
        "encoding=linear16&sample_rate=16000&bit_rate=32000&";

    // Add language parameter
    if (languages != null && languages.length > 1) {
      // Multiple languages selected - use multilingual mode
      url += "language=multi&";
    } else if (languages != null &&
        languages.isNotEmpty &&
        languages.first == 'multi') {
      // Explicit multilingual option selected
      url += "language=multi&";
    } else {
      // Single language (or default to English)
      final langCode =
          (languages?.isNotEmpty == true) ? languages!.first : 'en';

      // Check if the language is supported by Nova-3 for single language mode
      if (nova3SupportedLanguages.contains(langCode)) {
        url += "language=$langCode&";
      } else {
        // Language not supported by Nova-3 in single mode, use multi mode
        url += "language=multi&";
      }
    }

    // Add model and other parameters
    url += "model=nova-3&"
        "no_delay=true&"
        "interim_results=true&"
        "smart_format=true&"
        "diarize=true";

    return url;
  }
}

import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/api/transcription/deepgram_transcription_service.dart';
import 'package:interview_hammer/api/transcription/transcription_service/transcription_service.dart';

class DeepgramTranscriptionServiceImpl implements TranscriptionService {
  final DeepgramTranscriptionService _deepgramService =
      DeepgramTranscriptionService();

  @override
  Future<void> initialize({
    required Function(String) onInterim,
    required Function(String) onEndpoint,
    required Function(dynamic) onError,
    List<String>? languages,
  }) async {
    await _deepgramService.setupDeepgram(
      interimCallback: onInterim,
      endpointCallback: onEndpoint,
      onError: onError,
      onDone: (closeCode, closeReason) {
        d('Deepgram connection closed: closeCode: $closeCode, closeReason: $closeReason');
      },
      languages: languages,
    );
  }

  @override
  Future<void> stop() async {
    await _deepgramService.stop();
  }

  @override
  void clear() {
    _deepgramService.clear();
  }

  @override
  void dispose() {
    _deepgramService.stop();
    _deepgramService.close();
  }
}

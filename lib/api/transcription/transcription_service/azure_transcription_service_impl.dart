import 'package:interview_hammer/api/transcription/azure/azure_transcription_service.dart';
import 'package:interview_hammer/api/transcription/transcription_service/transcription_service.dart';

class AzureTranscriptionServiceImpl implements TranscriptionService {
  late final AzureTranscriptionService _azureService;

  AzureTranscriptionServiceImpl() {
    _azureService = AzureTranscriptionService.create();
  }

  @override
  Future<void> initialize({
    required Function(String) onInterim,
    required Function(String) onEndpoint,
    required Function(dynamic) onError,
    List<String>? languages,
  }) async {
    await _azureService.startListening(
      languages: languages ?? ['en-US'],
      onTranscribing: onInterim,
      onTranscribed: onEndpoint,
      onNoMatch: () {},
      onCanceled: (cancelReason) {},
      onSessionStopped: () {},
    );
  }

  @override
  Future<void> stop() async {
    _azureService.stopListening();
  }

  @override
  void clear() {
    _azureService.clear();
  }

  @override
  void dispose() {
    _azureService.stopListening();
  }
}

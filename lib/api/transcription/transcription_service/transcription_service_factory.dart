import 'package:interview_hammer/api/transcription/transcription_service/azure_transcription_service_impl.dart';
import 'package:interview_hammer/api/transcription/transcription_service/deepgram_transcription_service_impl.dart';
import 'package:interview_hammer/api/transcription/transcription_service/gemini_transcription_service_impl.dart';
import 'package:interview_hammer/api/transcription/transcription_service/transcription_service.dart';

import '../../../model/management/language.dart';

class TranscriptionServiceFactory {
  static TranscriptionService create(TranscriptionProvider provider) {
    switch (provider) {
      case TranscriptionProvider.DEEPGRAM:
        return DeepgramTranscriptionServiceImpl();
      case TranscriptionProvider.AZURE:
        return AzureTranscriptionServiceImpl();
      case TranscriptionProvider.GEMINI:
        return GeminiTranscriptionServiceImpl();
    }
  }
}

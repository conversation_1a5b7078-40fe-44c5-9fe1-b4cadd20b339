/// Abstract class for transcription services
abstract class TranscriptionService {
  /// Initialize and start the transcription service
  Future<void> initialize({
    required Function(String) onInterim,
    required Function(String) onEndpoint,
    required Function(dynamic) onError,
    List<String>? languages,
  });

  /// Stop the transcription service
  Future<void> stop();

  /// Clear internal state without stopping the service
  void clear();

  /// Clean up resources
  void dispose();
}

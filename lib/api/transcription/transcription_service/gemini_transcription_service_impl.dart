import 'package:interview_hammer/api/transcription/transcription_service/transcription_service.dart';

import '../socket_transcription/socket_transcription_service.dart';

class GeminiTranscriptionServiceImpl implements TranscriptionService {
  final _socketService = SocketTranscriptionService();

  @override
  Future<void> initialize({
    required Function(String) onInterim,
    required Function(String) onEndpoint,
    required Function(dynamic) onError,
    List<String>? languages,
  }) async {
    await _socketService.initialize(
      onInterim: onInterim,
      onEndpoint: onEndpoint,
      onError: onError,
    );
  }

  @override
  Future<void> stop() async {
    await _socketService.stop();
  }

  @override
  void clear() {
    _socketService.clear();
  }

  @override
  void dispose() {
    _socketService.dispose();
  }
}

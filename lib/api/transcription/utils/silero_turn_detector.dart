import 'dart:async';
import 'dart:developer';
import 'dart:io' as io;
import 'dart:math' as math;
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:fonnx/models/sileroVad/silero_vad.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart' as path_provider;

/// Result returned for each processed microphone segment.
class VadProcessingResult {
  VadProcessingResult({
    required this.voicedWindows,
    required this.speechEnded,
  });

  /// All 96 ms (1536-sample) windows that were classified as speech and should
  /// be forwarded to the server.
  final List<Uint8List> voicedWindows;

  /// True when the detector has observed the required amount of trailing
  /// silence and the current speech turn is considered finished.
  final bool speechEnded;
}

/// Higher-level voice-activity detector that builds on the Silero ONNX model
/// to provide more accurate turn detection.
///
/// Compared with the original [SileroVADWrapper] it implements the same
/// default parameters exposed by the LiveKit Silero VAD plugin:
///   * `minSpeechDuration`  – 0.05 s
///   * `minSilenceDuration` – 0.55 s
///   * `prefixPadding`      – 0.50 s (sent before the first voiced frame)
///   * `activationThreshold`– 0.5
///   * `sampleRate`         – 16 kHz (fixed by the surrounding pipeline)
class SileroTurnDetector {
  static const double _minSpeechDuration = 0.05; // s
  static const double _minSilenceDuration = 0.55; // s
  static const double _prefixPaddingDuration = 0.50; // s
  static const double _activationThreshold = 0.5;
  static const double _maxSpeechTurnDuration = 30.0; // s
  static const int _sampleRate = 16000;

  // Window: 1 536 samples (96 ms)
  static const int _windowSamples = 1536;
  static const int _windowBytes = _windowSamples * 2; // 16-bit PCM
  static const double _windowDuration =
      _windowSamples / _sampleRate; // ≈0.096 s

  // Pre-computed helper counts.
  static final int _minSpeechWindows =
      math.max(1, (_minSpeechDuration / _windowDuration).ceil());
  static final int _minSilenceWindows =
      (_minSilenceDuration / _windowDuration).ceil();
  static final int _prefixPaddingWindows =
      (_prefixPaddingDuration / _windowDuration).ceil();

  SileroVad? _model;
  Map<String, dynamic>? _rnnState;
  final BytesBuilder _pendingBuffer = BytesBuilder();

  bool _inSpeech = false;
  int _consecutiveSpeechWindows = 0;
  int _consecutiveSilenceWindows = 0;

  // Ring-buffer that retains the most recent windows such that they can be
  // prepended to the beginning of a turn ("prefix padding").
  final List<Uint8List> _prefixBuffer = [];

  // Wall-clock timestamp when the current speech turn started.
  DateTime? _speechStartTime;

  // region Public API
  Future<VadProcessingResult> process(Uint8List micSegment) async {
    if (micSegment.isEmpty) {
      return VadProcessingResult(voicedWindows: const [], speechEnded: false);
    }

    // Buffer the incoming bytes so that they can be consumed in fixed-size windows later.
    _pendingBuffer.add(micSegment);

    final List<Uint8List> voicedOut = [];

    // Lazily load and cache the VAD model.
    final model = await _loadModel();

    // Drain the pending buffer and detect whether the current speech turn ended.
    final speechTurnJustEnded =
        await _processAvailableWindows(model, voicedOut);

    return VadProcessingResult(
      voicedWindows: voicedOut,
      speechEnded: speechTurnJustEnded,
    );
  }

  void reset() {
    _pendingBuffer.clear();
    _rnnState = null;
    _inSpeech = false;
    _consecutiveSpeechWindows = 0;
    _consecutiveSilenceWindows = 0;
    _prefixBuffer.clear();

    // Clear timeout timer.
    _speechStartTime = null;
  }

  // endregion Public API

  // region Private helpers
  // region Timeout helper
  /// Returns true when an active speech turn timed out and has been force-ended.
  bool _checkSpeechTurnTimeout() {
    if (!_inSpeech || _speechStartTime == null) return false;

    final elapsedSeconds =
        DateTime.now().difference(_speechStartTime!).inMilliseconds / 1000.0;

    if (elapsedSeconds < _maxSpeechTurnDuration) {
      return false;
    }

    log('[SileroTurnDetector] speech turn timeout after ${elapsedSeconds.toStringAsFixed(2)} s');

    _inSpeech = false;
    _prefixBuffer.clear();
    _consecutiveSilenceWindows = 0;
    _consecutiveSpeechWindows = 0;
    _speechStartTime = null;

    return true;
  }

  // endregion Timeout helper

  /// Lazily loads the Silero VAD model and caches the instance.
  Future<SileroVad> _loadModel() async {
    return _model ??= SileroVad.load(await _ensureModelPath());
  }

  /// Processes all complete windows currently stored in [_pendingBuffer].
  ///
  /// The voiced windows are appended to [voicedOut]. The method returns
  /// `true` if the speech turn ended while draining the buffer or because of
  /// a timeout.
  Future<bool> _processAvailableWindows(
    SileroVad model,
    List<Uint8List> voicedOut,
  ) async {
    var speechTurnEnded = false;

    while (_pendingBuffer.length >= _windowBytes) {
      final windowResult = await _processWindow(model);

      final transitionResult = _handleSpeechStateTransition(
        windowResult.window,
        windowResult.isSpeech,
        voicedOut,
      );

      if (transitionResult || _checkSpeechTurnTimeout()) {
        speechTurnEnded = true;
      }
    }

    return speechTurnEnded;
  }

  /// Extracts a single window from the pending buffer and returns the result
  /// of the VAD inference along with the window itself.
  Future<_WindowProcessingResult> _processWindow(SileroVad model) async {
    final bytes = _pendingBuffer.toBytes();
    final window = bytes.sublist(0, _windowBytes);

    // Remove processed window.
    final remaining = Uint8List.sublistView(bytes, _windowBytes);
    _pendingBuffer.clear();
    if (remaining.isNotEmpty) _pendingBuffer.add(remaining);

    // Run inference.
    final result =
        await model.doInference(window, previousState: _rnnState ?? const {});
    _rnnState = {
      'hn': result['hn'],
      'cn': result['cn'],
    };
    final prob = (result['output'] as Float32List).first;
    final isSpeech = prob >= _activationThreshold;

    return _WindowProcessingResult(window: window, isSpeech: isSpeech);
  }

  /// Handles the transition between speech and silence states based on the
  /// result of the VAD inference on a single window.
  ///
  /// Returns `true` if a speech turn ended due to a transition.
  bool _handleSpeechStateTransition(
    Uint8List window,
    bool isSpeech,
    List<Uint8List> voicedOut,
  ) {
    // Maintain prefix ring-buffer.
    _prefixBuffer.add(window);
    if (_prefixBuffer.length > _prefixPaddingWindows) {
      _prefixBuffer.removeAt(0);
    }

    if (isSpeech) {
      _consecutiveSpeechWindows++;
      _consecutiveSilenceWindows = 0;

      if (!_inSpeech && _consecutiveSpeechWindows >= _minSpeechWindows) {
        // Speech starts –- flush prefix padding.
        voicedOut.addAll(_prefixBuffer);
        _prefixBuffer.clear();
        _inSpeech = true;

        // Start timeout timer.
        _speechStartTime = DateTime.now();
      }

      if (_inSpeech) {
        voicedOut.add(window);
      }

      return false;
    } else {
      _consecutiveSilenceWindows++;
      _consecutiveSpeechWindows = 0;

      if (_inSpeech) {
        if (_consecutiveSilenceWindows >= _minSilenceWindows) {
          // Detected speech → silence transition.
          _inSpeech = false;
          _prefixBuffer.clear();
          _consecutiveSilenceWindows = 0;

          // Reset timeout timer.
          _speechStartTime = null;

          return true;
        } else if (_consecutiveSilenceWindows == 1) {
          // Keep the very first trailing-silence window so the server gets a
          // cleanly framed chunk right after speech.
          voicedOut.add(window);
        }
      }

      return false;
    }
  }

  Future<String> _ensureModelPath() async {
    if (kIsWeb) {
      // On web we can reference the asset directly.
      return 'assets/assets/models/sileroVad/silero_vad.onnx';
    }

    final dir = await path_provider.getApplicationSupportDirectory();
    final destPath = p.join(dir.path, 'silero_vad.onnx');

    final io.File file = io.File(destPath);
    final exists = await file.exists();

    const assetPath = 'assets/models/sileroVad/silero_vad.onnx';
    final assetData = await rootBundle.load(assetPath);

    if (!exists || (await file.length()) != assetData.lengthInBytes) {
      if (kDebugMode) {
        debugPrint('[SileroTurnDetector] copying model to $destPath');
      }
      await file.create(recursive: true);
      await file.writeAsBytes(assetData.buffer.asUint8List(), flush: true);
    }

    return destPath;
  }
// endregion Private helpers
}

/// Helper class for storing the result of processing a single window.
class _WindowProcessingResult {
  _WindowProcessingResult({
    required this.window,
    required this.isSpeech,
  });

  final Uint8List window;
  final bool isSpeech;
}

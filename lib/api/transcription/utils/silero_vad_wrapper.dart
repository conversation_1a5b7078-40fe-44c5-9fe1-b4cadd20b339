import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:fonnx/models/sileroVad/silero_vad.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart' as path_provider;

/// Wrapper for the Silero ONNX Voice-Activity-Detection model shipped with
/// the _fonnx_ package. Used to detect speech segments in audio and strip out
/// silence.
///
/// The model processes 16 kHz PCM audio in 1536-sample windows (96 ms) and
/// outputs a probability (0‥1) for speech presence in each window. Values
/// below [_speechThreshold] are considered silence.
class SileroVADWrapper {
  SileroVADWrapper();

  /// Windows whose speech probability is below this threshold are considered
  /// silence and are discarded.
  static const double _speechThreshold = 0.5;

  SileroVad? _vad;
  Map<String, dynamic>? _state;

  static const int _windowBytes = 1536 * 2; // 96 ms at 16 kHz
  final BytesBuilder _pendingBuffer = BytesBuilder();

  /// Appends [segment] to an internal buffer and returns a list of *speech*
  /// windows (each 1536-sample/3072-byte chunk) extracted from the front of
  /// the buffer as they become available.  Windows classified as silence are
  /// dropped.  Call this repeatedly with back-to-back microphone chunks to
  /// incrementally build a speech-only stream.
  ///
  /// Example usage:
  /// ```dart
  /// final speechBytesBuilder = BytesBuilder();
  /// final windows = await vad.extractSpeechWindows(micSegment);
  /// for (final w in windows) speechBytesBuilder.add(w);
  /// ```
  Future<List<Uint8List>> extractSpeechWindows(Uint8List segment) async {
    if (segment.isEmpty) return const [];

    _pendingBuffer.add(segment);
    final List<Uint8List> speechWindows = [];

    final vad = _vad ??= SileroVad.load(await _ensureModelPath());

    // Process as many complete windows as we have.
    while (_pendingBuffer.length >= _windowBytes) {
      final Uint8List bytes = _pendingBuffer.toBytes();
      final Uint8List window = bytes.sublist(0, _windowBytes);

      // Remove processed window (zero-copy view for remainder)
      final Uint8List remaining = Uint8List.sublistView(bytes, _windowBytes);
      _pendingBuffer.clear();
      if (remaining.isNotEmpty) _pendingBuffer.add(remaining);

      final result =
          await vad.doInference(window, previousState: _state ?? const {});

      // Persist the updated hidden state so the next window benefits from
      // the model’s temporal memory.  We only keep the pieces relevant for
      // stateful operation (hn & cn).
      _state = {
        'hn': result['hn'],
        'cn': result['cn'],
      };

      final prob = (result['output'] as Float32List).first;

      // debugPrint('[SileroVAD] speech window prob=$prob');
      if (prob >= _speechThreshold) {
        speechWindows.add(window);
      }
    }

    return speechWindows;
  }

  /// Clears the internal audio buffer **and** resets the recurrent state so
  /// the very next [`extractSpeechWindows`] call starts fresh.  Call this at
  /// logical boundaries in the audio stream (e.g. end of a “turn”).
  void resetState() {
    _pendingBuffer.clear();
    _state = null;
  }

  // ---------------- Private helpers ----------------

  Future<String> _ensureModelPath() async {
    if (kIsWeb) {
      // On web we can reference the asset directly.
      return 'assets/assets/models/sileroVad/silero_vad.onnx';
    }

    final dir = await path_provider.getApplicationSupportDirectory();
    final destPath = path.join(dir.path, 'silero_vad.onnx');

    final file = File(destPath);
    final exists = await file.exists();

    // Asset path inside the Flutter app bundle.
    const assetPath = 'assets/models/sileroVad/silero_vad.onnx';
    final assetData = await rootBundle.load(assetPath);

    if (!exists || (await file.length()) != assetData.lengthInBytes) {
      if (kDebugMode) {
        debugPrint('[SileroVAD] copying model to $destPath');
      }
      await file.create(recursive: true);
      await file.writeAsBytes(assetData.buffer.asUint8List(), flush: true);
    }

    return destPath;
  }
}

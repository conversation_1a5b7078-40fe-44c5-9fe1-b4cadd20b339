import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/utils/string_utils.dart';
import 'package:rxdart/rxdart.dart';

/// TranscriptionManager
///
/// This class manages the flow of transcription data from two sources:
/// interim transcriptions and endpoint transcriptions.
///
/// Key features:
/// 1. Handles both interim and endpoint transcriptions.
/// 2. Prioritizes endpoint transcriptions over interim ones.
/// 3. Implements a throttle to limit the frequency of emitted events.
/// 4. Ignores interim transcriptions for a set period after an endpoint transcription.
/// 5. Waits for a short period after receiving an interim event to see if an endpoint event arrives.
/// 6. Implements intelligent event handling:
///    - Emits endpoint events immediately, resetting cooldown timers
///    - Delays interim events briefly to check for incoming endpoint events
///    - Uses separate throttles for interim and endpoint events to balance responsiveness and efficiency
///
/// Usage:
/// - Create an instance of TranscriptionManager.
/// - Use addInterimTranscription() and addEndpointTranscription() to input data.
/// - Implement custom logic by calling the listen() method and providing a callback.
/// - Remember to call dispose() when the manager is no longer needed.
///
/// The reason we need this is deepgram don't always provide endpoint transcriptions.

// Only used outside of this file for testing.
const endpointThrottleDuration = Duration(seconds: 2);
const interimThrottleDuration = Duration(seconds: 3);
const totalThrottleDuration = Duration(seconds: 2);

/// Duration for which interim transcriptions are ignored after an endpoint transcription
const endpointTimeout = Duration(seconds: 5);

/// Time to wait for potential endpoint transcription after receiving an interim transcription
/// It's too small because it was added to avoid the case where I get an interim transcription, and then an endpoint transcription right after.
const interimWaitDuration = Duration(milliseconds: 100);

/// Manages the flow of transcription data from interim and endpoint sources.
class TranscriptionManager {
  final _interimTranscriptionSubject = BehaviorSubject<String>();
  final _endpointTranscriptionSubject = BehaviorSubject<String>();
  final _eventSubject = PublishSubject<TranscriptionEvent>();

  bool _endpointFiredRecently = false;
  Timer? _endpointTimer;
  Timer? _interimWaitTimer;
  late StreamSubscription _internalSubscription;
  late StreamSubscription<TranscriptionEvent> _eventSubscription;
  DateTime? _lastEndpointTime;
  DateTime? _lastInterimTime;
  String? _pendingInterimTranscription;

  TranscriptionManager() {
    _setupInternalStream();
    _eventSubscription = _eventSubject
        .throttleTime(totalThrottleDuration)
        .listen(_emitEventImmediately);
  }

  void _setupInternalStream() {
    final endpointStream = _endpointTranscriptionSubject
        .throttleTime(endpointThrottleDuration, leading: false, trailing: true)
        .map((value) => TranscriptionEvent(TranscriptionType.endpoint, value));

    final interimStream = _interimTranscriptionSubject
        .throttleTime(interimThrottleDuration, leading: false, trailing: true)
        .map((value) => TranscriptionEvent(TranscriptionType.interim, value));

    final mergedStream = Rx.merge([endpointStream, interimStream]);
    _internalSubscription = mergedStream.listen((event) {
      final now = DateTime.now();
      if (event.type == TranscriptionType.endpoint) {
        _handleEndpointEvent(event, now);
      } else {
        _handleInterimEvent(event, now);
      }
    });

    _logDroppedEvents();
  }

  void _handleEndpointEvent(TranscriptionEvent event, DateTime now) {
    _endpointFiredRecently = true;
    _endpointTimer?.cancel();
    _endpointTimer = Timer(endpointTimeout, () {
      _endpointFiredRecently = false;
      _d('⏱️🔓 Endpoint cooldown period ended after ${endpointTimeout.inSeconds}s');
    });
    _interimWaitTimer?.cancel();
    _pendingInterimTranscription = null;
    _lastEndpointTime = now;
    _emitEvent(event, now);
  }

  void _handleInterimEvent(TranscriptionEvent event, DateTime now) {
    if (!_endpointFiredRecently) {
      _pendingInterimTranscription = event.value;
      _interimWaitTimer?.cancel();
      _interimWaitTimer = Timer(interimWaitDuration, () {
        if (_pendingInterimTranscription != null) {
          _lastInterimTime = now;
          _emitEvent(event, now);
        }
      });
    } else {
      final remainingCooldown =
          endpointTimeout - now.difference(_lastEndpointTime!);
      _d('🚫💬 Dropped interim transcription due to recent endpoint. Remaining cooldown: ${remainingCooldown.inMilliseconds}ms: ${event.value.slice(-50)}');
    }
  }

  void _emitEvent(TranscriptionEvent event, DateTime now) {
    _eventSubject.add(event);
  }

  void _emitEventImmediately(TranscriptionEvent event) {
    _externalListener?.call(event);
    _d('✅${event.type == TranscriptionType.endpoint ? '🎤' : '💬'} Emitted ${event.type}: ${event.value.slice(-50)}');
  }

  void addInterimTranscription(String value) {
    _interimTranscriptionSubject.add(value);
    _d('📥💬 Received interim transcription: ${value.slice(-50)}');
  }

  void addEndpointTranscription(String value) {
    _endpointTranscriptionSubject.add(value);
    _d('📥🎤 Received endpoint transcription: ${value.slice(-50)}');
  }

  Function(TranscriptionEvent)? _externalListener;

  void listen(Function(TranscriptionEvent) onData) {
    _externalListener = onData;
  }

  void dispose() {
    _endpointTimer?.cancel();
    _interimWaitTimer?.cancel();
    _internalSubscription.cancel();
    _eventSubscription.cancel();
    _interimTranscriptionSubject.close();
    _endpointTranscriptionSubject.close();
    _eventSubject.close();
    _d('🔚🔧 TranscriptionManager disposed');
  }

  void _logDroppedEvents() {
    // Log dropped events due to throttling
    _endpointTranscriptionSubject.listen((value) {
      final now = DateTime.now();
      if (_lastEndpointTime != null) {
        final remainingTime =
            endpointThrottleDuration - now.difference(_lastEndpointTime!);
        if (remainingTime > Duration.zero) {
          _d('🚫🎤 Dropped endpoint transcription due to throttling. Remaining time: ${remainingTime.inMilliseconds}ms: ${value.slice(-50)}');
        } else {
          _lastEndpointTime = now;
        }
      } else {
        _lastEndpointTime = now;
      }
    });

    _interimTranscriptionSubject.listen((value) {
      final now = DateTime.now();
      if (_lastInterimTime != null) {
        final remainingTime =
            interimThrottleDuration - now.difference(_lastInterimTime!);
        if (remainingTime > Duration.zero) {
          _d('🚫💬 Dropped interim transcription due to throttling. Remaining time: ${remainingTime.inMilliseconds}ms: ${value.slice(-50)}');
        } else {
          _lastInterimTime = now;
        }
      } else {
        _lastInterimTime = now;
      }
    });
  }
}

enum TranscriptionType { interim, endpoint }

class TranscriptionEvent {
  final TranscriptionType type;
  final String value;

  TranscriptionEvent(this.type, this.value);
}

void _d(Object object) {
  const shouldLog = kDebugMode && false;
  // ignore: dead_code
  if (shouldLog) d2(object);
}

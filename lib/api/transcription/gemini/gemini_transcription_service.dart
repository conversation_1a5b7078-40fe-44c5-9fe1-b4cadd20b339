import 'dart:async';
import 'dart:developer';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:interview_hammer/api/gemini/gemini_client.dart';
import 'package:interview_hammer/api/management/user_profile_manager.dart';
import 'package:record/record.dart';

import '../../gemini/live_event.dart';
import '../utils/silero_vad_wrapper.dart';

/// GeminiTranscriptionService – streams microphone audio to Google Gemini Live
/// and returns rolling transcripts.
///
/// High-level overview
/// • Opens a WebSocket to "Gemini 2.0 Flash Live" (`models/gemini-2.0-flash-live-001`), sends a system prompt and
///   keeps the socket alive.
/// • Captures microphone audio (16 kHz, 16-bit, mono) and streams it in small
///   chunks while buffering a copy locally.
/// • Fires two user-supplied callbacks:
///   – `interimCallback`  : draft transcript updated in near real-time.
///   – `endpointCallback`: final transcript for each "turn" (pause in speech).
/// • After every turn it launches a second-pass, higher-accuracy refinement on
///   the buffered audio with "Gemini 2.5 Flash" (`models/gemini-2.5-flash-preview-04-17`), replacing the draft text when
///   it lands.
/// • Every 3 seconds a periodic refinement timer runs:
///   – If new speech has arrived since the previous tick it performs an
///     **early** refinement on the accumulated speech-only buffer with
///     "Gemini 2.5 Flash" (internal transcript).
///   – If the last 3-second window contained only silence, the refinement is
///     skipped so we avoid unnecessary requests.
/// • When the server eventually emits `turnComplete`, a **final** refinement is
///   issued on the full speech of that turn, replacing any earlier
///   3-second internal transcript with the definitive text.
/// • Turn texts are accumulated internally so the callbacks always receive the
///   full conversation so far – each turn appears exactly once, first as draft
///   (optional) and then refined.
///
/// Public surface:
/// • `setupGemini` – establish connection and start streaming.
/// • `stop`        – pause microphone capture.
/// • `close`       – close WebSocket, microphone, and timers.
/// • `clear`       – reset all internal buffers and state without stopping the audio processing.
///
/// All timers, buffering, and WebSocket framing are handled internally; the
/// caller only deals with the two callbacks and lifecycle methods listed
/// above.
class GeminiTranscriptionService {
  static const _systemPrompt = '''
Transcribe the following audio from an interview verbatim.
The interview topic is: {{INTERVIEW_TOPIC}} 
{{INTERVIEW_LANGUAGE}}
Keep any English technical terms in English as spoken. 
Do not include any timestamps in the output. 
Output ONLY the transcript text, nothing else.
If the input audio contains no voices (silence or noise only), respond with an empty string.
''';

  static const _periodicRefinementDuration = Duration(seconds: 3);
  static const _sampleRate = 16000;
  static const _segmentBytes = 1024;
  static const _bytesPerSecond = _sampleRate * 2;
  static const _maxBufferSeconds = 10;

  // static const _liveModelName = 'gemini-2.0-flash-live-preview-04-09';
  // static const _refinementModelName = 'gemini-2.5-flash-preview-04-17';
  // final _geminiClient = VertexClient.instance;

  static const _liveModelName = 'models/gemini-2.0-flash-live-001';
  static const _refinementModelName = 'models/gemini-2.5-flash-preview-04-17';
  final _geminiClient = GeminiClient.instance;

  final _recorder = AudioRecorder();
  StreamSubscription<Uint8List>? _micSubscription;
  bool _micStarted = false;

  // Buffer that keeps ONLY the segments classified as speech by Silero VAD.
  // Used exclusively for Gemini-2.5 refinement requests so we don't waste
  // bandwidth or model quota on long silent stretches.
  final _speechOnlyBuffer = BytesBuilder();

  // Buffer to accumulate current interim pieces.
  final _currentBuffer = StringBuffer();

  // Buffer for full transcript across turns.
  final _fullTranscript = StringBuffer();

  // Buffer for model turn texts.
  final _modelCurrentBuffer = StringBuffer();
  final _modelFullTranscript = StringBuffer();

  final List<String> _turnTexts = [];

  Timer? _periodicRefinementTimer;

  final _sileroVAD = SileroVADWrapper();

  int _speechBytesSeen = 0; // Total voiced bytes ever appended in current turn
  int _speechBytesRefined =
      0; // Bytes count covered by the most recent refinement

  /// Returns the index of the currently active turn (the last list entry).
  /// When the list is empty we treat the first turn as index 0.
  int get _activeTurnIndex => _turnTexts.isEmpty ? 0 : _turnTexts.length - 1;

  // ----------------- Public API Methods -----------------

  Future<void> setupGemini({
    required Function(String) interimCallback,
    required Function(String) endpointCallback,
    required Function(dynamic) onError,
    Function(int? closeCode, String? closeReason)? onDone,
  }) async {
    _d('🔗 Connecting to Gemini Live WebSocket…');

    await _geminiClient.connectLive(
      modelName: _liveModelName,
      systemPrompt: _buildSystemPrompt(),
      onEvent: (e) =>
          _handleEvent(e, interimCallback, endpointCallback, onError),
      onError: onError,
      onDone: onDone,
    );

    // Start microphone streaming right away; server will ignore audio until ready.
    _startMicStream(onError);
    _schedulePeriodicRefinementTimer(interimCallback, endpointCallback);

    // Timeout: if no server message within 3s, print warning.
    Timer(const Duration(seconds: 3), () {
      if (!_micStarted) {
        _d('⚠️ No server response after 3s – your API key may not have Live API access');
      }
    });
  }

  Future<void> stop() async {
    await _recorder.stop();
    await _micSubscription?.cancel();
    _micSubscription = null;
    _micStarted = false;

    // Clear any buffered audio from an unfinished turn – it is no longer
    // relevant once the mic is muted/paused.
    _speechOnlyBuffer.clear();

    // Reset VAD so we don't carry state across pauses.
    _sileroVAD.resetState();

    // Reset refinement counters for the next turn.
    _speechBytesSeen = 0;
    _speechBytesRefined = 0;

    // No need for periodic refinement timer when the mic is off.
    _cancelPeriodicRefinementTimer();
  }

  void clear() {
    _turnTexts.clear();
    _fullTranscript.clear();
    _modelFullTranscript.clear();
  }

  Future<void> close() async {
    await stop();
    await _geminiClient.close();

    // dispose throws MissingPluginException on some platforms; this is benign.
    await _recorder.dispose();
  }

  // ----------------- Audio Recording Methods -----------------

  Future<void> _startMicStream(Function(dynamic) onError) async {
    if (_micStarted) return;
    _micStarted = true;
    _d('🎤 Starting microphone streaming');
    final micStream = await _recorder.startStream(const RecordConfig(
      encoder: AudioEncoder.pcm16bits,
      sampleRate: _sampleRate,
      numChannels: 1,
    ));

    _micSubscription = micStream.listen(
      (Uint8List chunk) {
        for (var offset = 0; offset < chunk.length; offset += _segmentBytes) {
          final end = (offset + _segmentBytes) < chunk.length
              ? offset + _segmentBytes
              : chunk.length;
          final segment = chunk.sublist(offset, end);
          _geminiClient.sendAudioChunk(segment);

          // Classify this segment in the background; if it contains speech
          // keep it in the speech-only buffer for refinement.
          _sileroVAD.extractSpeechWindows(segment).then((ws) {
            for (final w in ws) {
              _speechOnlyBuffer.add(w);
              _speechBytesSeen += w.length;
            }
            _enforceSpeechBufferCap();
          });
        }
      },
      onError: onError,
    );
  }

  // ----------------- Event Handling Methods -----------------

  void _handleEvent(
    GeminiLiveEvent evt,
    void Function(String) interimCallback,
    void Function(String) endpointCallback,
    Function(dynamic) onError,
  ) {
    try {
      _accumulateText(evt);

      if (evt.turnComplete) {
        _handleTurnComplete(evt, interimCallback, endpointCallback);
      }
    } catch (e) {
      _d('🚨 Failed to parse message: $e');
    }
  }

  void _accumulateText(GeminiLiveEvent evt) {
    if (evt.recognizerText?.isNotEmpty == true) {
      _currentBuffer.write('${evt.recognizerText} ');
    }

    if (evt.modelText?.isNotEmpty == true) {
      _modelCurrentBuffer.write(evt.modelText);
    }
  }

  void _handleTurnComplete(
    GeminiLiveEvent evt,
    Function(String) interimCallback,
    Function(String) endpointCallback,
  ) {
    _d('🏁 Turn complete – triggering refinement and endpoint callbacks');

    // Capture a copy of the *speech-only* audio bytes that belong to this turn
    final Uint8List audioForRefinement = _speechOnlyBuffer.toBytes();
    _speechOnlyBuffer.clear(); // reset speech-only buffer for next turn

    // Reset VAD recurrent state so next turn starts clean.
    _sileroVAD.resetState();

    // Reset refinement counters for the next turn.
    _speechBytesSeen = 0;
    _speechBytesRefined = 0;

    // Fire an asynchronous refined transcription request
    _processAudioRefinement(
      audioBytes: audioForRefinement,
      currentTurnId: _activeTurnIndex,
      interimCallback: interimCallback,
      endpointCallback: endpointCallback,
      isEarly: false,
    );

    // 🔚 Endpoint logic
    final recognizerTurn = _currentBuffer.toString().trim();
    final modelTurn = _modelCurrentBuffer.toString().trim();

    if (recognizerTurn.isNotEmpty) _fullTranscript.write('$recognizerTurn\n');
    if (modelTurn.isNotEmpty) _modelFullTranscript.write('$modelTurn\n');

    final String output = _fullTranscript.toString();
    final String modelOutput = _modelFullTranscript.toString();

    final String preferred = modelOutput.isNotEmpty ? modelOutput : output;

    _d('🏁 Endpoint text: $preferred');

    // Ensure we always have an *empty* placeholder ready for the next turn.
    if (_turnTexts.isEmpty || _turnTexts.last.isNotEmpty) {
      _turnTexts.add('');
    }

    // Reset current turn buffers
    _currentBuffer.clear();
    _modelCurrentBuffer.clear();
  }

  /// Ensures [_speechOnlyBuffer] never grows beyond [_maxBufferSeconds].
  ///
  /// If the buffer exceeds the allowed size, the oldest bytes are discarded
  /// and only the most recent [_maxBufferSeconds] seconds of audio are kept.
  void _enforceSpeechBufferCap() {
    final int maxBytes = _maxBufferSeconds * _bytesPerSecond;
    if (_speechOnlyBuffer.length <= maxBytes) return;

    // Copy the last [maxBytes] bytes and rebuild the buffer with that tail.
    final Uint8List bytes = _speechOnlyBuffer.toBytes();
    final Uint8List tail = bytes.sublist(bytes.length - maxBytes, bytes.length);

    _speechOnlyBuffer.clear();
    _speechOnlyBuffer.add(tail);
  }

  // ----------------- Timer Management Methods -----------------

  void _schedulePeriodicRefinementTimer(
      Function(String)? interimCb, Function(String)? endpointCb) {
    if (_periodicRefinementTimer == null) {
      _d('⏱️ Starting 3-second periodic refinement timer');
      _periodicRefinementTimer =
          Timer.periodic(_periodicRefinementDuration, (_) async {
        _d('⏱️ Periodic timer tick – performing early refinement');
        await _performScheduledRefinement(interimCb, endpointCb);
      });
    }
  }

  Future<void> _performScheduledRefinement(
      Function(String)? interimCb, Function(String)? endpointCb) async {
    if (!_micStarted) return; // mic muted – no live audio to process

    // Debug: print the current buffer length (speech-only) and its duration.
    final double bufferSeconds = _speechOnlyBuffer.length / _bytesPerSecond;
    _d('📦 Buffer length before early refinement: ${_speechOnlyBuffer.length} bytes (~${bufferSeconds.toStringAsFixed(2)} s)');

    // If no new speech since last refinement, skip.
    if (_speechBytesSeen == _speechBytesRefined) {
      _d('⏸️ No new speech since last refinement – skipping');
      return;
    }

    // If we have already refined (buffer cleared) or there is no speech, bail.
    if (_speechOnlyBuffer.isEmpty) {
      return;
    }

    // Copy current speech-only audio (up to latest); keep accumulating so we
    // still have the full audio available when the turn finally ends.
    final Uint8List audioCopy = _speechOnlyBuffer.toBytes();

    // Update cursor so subsequent ticks know we have already refined these bytes.
    _speechBytesRefined = _speechBytesSeen;

    _processAudioRefinement(
      audioBytes: audioCopy,
      currentTurnId: _activeTurnIndex,
      interimCallback: interimCb,
      endpointCallback: endpointCb,
      isEarly: true,
    );
  }

  Future<void> _processAudioRefinement({
    required Uint8List audioBytes,
    required int currentTurnId,
    required Function(String)? interimCallback,
    required Function(String)? endpointCallback,
    bool isEarly = false,
  }) async {
    if (audioBytes.isEmpty) {
      _d('⚠️ Refinement skipped: empty audio buffer');
      return;
    }

    final refinedText = await _geminiClient.generateContentFromAudio(
      audioBytes,
      refinementModelName: _refinementModelName,
      systemPrompt: _buildSystemPrompt(),
    );

    if (refinedText?.isNotEmpty == true) {
      final tag = isEarly ? '⏱️' : '🔄';
      _d('$tag Refined transcription${isEarly ? ' (early)' : ''}: $refinedText');

      // Update or add refined text to the turn texts list
      _turnTexts.length <= currentTurnId
          ? _turnTexts.add(refinedText!)
          : _turnTexts[currentTurnId] = refinedText!;

      final String accumulated = _turnTexts.join('\n');

      // Call both callbacks with the accumulated text
      interimCallback?.call(accumulated);
      endpointCallback?.call(accumulated);
    }
  }

  void _cancelPeriodicRefinementTimer() {
    _periodicRefinementTimer?.cancel();
    _periodicRefinementTimer = null;
  }

  String _buildSystemPrompt() {
    final userProfile = userProfileManager.userProfile;

    final interviewLanguages = userProfile?.interviewLanguages;
    final interviewTopic = userProfile?.interviewTopic ?? 'Not specified';

    String interviewLanguageLine = '';
    if (interviewLanguages != null && interviewLanguages.isNotEmpty) {
      if (interviewLanguages.length == 1) {
        interviewLanguageLine =
            'The interview language is: ${interviewLanguages.first.aiName}';
      } else {
        final languagesStr = interviewLanguages.map((l) => l.aiName).join(', ');
        interviewLanguageLine = 'The interview languages are: $languagesStr';
      }
    }

    return _systemPrompt
        .replaceAll("{{INTERVIEW_TOPIC}}", interviewTopic)
        .replaceAll("{{INTERVIEW_LANGUAGE}}", interviewLanguageLine);
  }
}

void _d(Object object) {
  if (kDebugMode) {
    log('[Gemini] $object');
  }
}

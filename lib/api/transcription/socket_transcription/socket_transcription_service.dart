import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';

import 'package:interview_hammer/api/management/remote_config.dart';
import 'package:interview_hammer/api/management/user_profile_manager.dart';
import 'package:interview_hammer/utils/events/events.dart';
import 'package:record/record.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

import '../transcription_service/transcription_service.dart';
import '../utils/silero_turn_detector.dart';

/// SocketTranscriptionService – streams voiced PCM audio to a backend
/// WebSocket endpoint and receives interim/endpoint transcripts in return.
///
/// The service detects speech locally using Silero VAD so only voiced
/// windows are transmitted, reducing bandwidth and protecting user privacy.
///
/// WebSocket protocol (JSON frames):
///   {"type": "interim",  "turnId": 3, "text": "draft transcript" }
///   {"type": "endpoint", "turnId": 3, "text": "final transcript" }
///   {"type": "error",   "message": "description" }
/// Binary frames are treated as raw 16-bit PCM (16 kHz, mono) audio.
class SocketTranscriptionService implements TranscriptionService {
  static const _sampleRate = 16000;

  final _recorder = AudioRecorder();
  final _turnDetector = SileroTurnDetector();
  final List<String> _turns = [];

  WebSocketChannel? _ws;
  StreamSubscription<Uint8List>? _micSubscription;

  bool _micStarted = false;
  bool _inSpeech = false;

  // Reconnection state & cached callbacks
  Function(String)? _cachedOnInterim;
  Function(String)? _cachedOnEndpoint;
  Function(dynamic)? _cachedOnError;
  List<String>? _cachedLanguages;

  bool _isReconnecting = false;
  bool _manuallyClosed = false;
  int _reconnectAttempts = 0;
  static const _initialBackoffMs = 500;
  static const _maxBackoffMs = 10000; // 10 seconds cap

  // region TranscriptionService API

  @override
  Future<void> initialize({
    required Function(String) onInterim,
    required Function(String) onEndpoint,
    required Function(dynamic) onError,
    List<String>? languages, // ignored for now – server handles language
  }) async {
    // Cache callbacks for potential reconnection attempts.
    _cachedOnInterim = onInterim;
    _cachedOnEndpoint = onEndpoint;
    _cachedOnError = onError;
    _cachedLanguages = languages;

    _manuallyClosed = false;

    final wsUrl = remoteConfig.getString(KEY_TRANSCRIPTION_WS_URL);
    // final wsUrl = 'ws://localhost:8086/transcribe';

    // Establish WebSocket connection.
    log('[SocketTranscriptionService] 🔌 Connecting to $wsUrl');
    _ws = WebSocketChannel.connect(Uri.parse(wsUrl));

    // Send initial configuration with system prompt right after connection.
    _ws?.sink.add(jsonEncode({
      'type': 'config',
      'systemPrompt': _buildSystemPrompt(),
    }));

    // Listen for messages from the server.
    _ws!.stream.listen(
      (dynamic msg) {
        // GeminiSession may send text frames that arrive as either String or
        // binary; accept both.
        String? jsonStr;
        if (msg is String) {
          jsonStr = msg;
        } else if (msg is List<int>) {
          try {
            jsonStr = utf8.decode(msg);
          } catch (_) {
            // non-UTF8 binary (probably audio echo) – ignore.
          }
        }

        if (jsonStr != null) {
          log('[SocketTranscriptionService] 📥 Incoming (${jsonStr.length} chars)');
          _handleServerMessage(jsonStr, onInterim, onEndpoint, onError);
        } else if (msg is Uint8List) {
          logError(
              '[SocketTranscriptionService] ⚠️ Received unexpected binary (${msg.length} bytes)');
        }
      },
      onError: (dynamic e) {
        onError(e);
        if (!_manuallyClosed) _restartWebSocket();
      },
      onDone: () {
        // Ensure mic is stopped when socket closes unexpectedly.
        stop();
        log('[SocketTranscriptionService] 🌐 WebSocket closed');
        if (!_manuallyClosed) _restartWebSocket();
      },
    );

    // Open microphone stream.
    final micStream = await _recorder.startStream(const RecordConfig(
      encoder: AudioEncoder.pcm16bits,
      sampleRate: _sampleRate,
      numChannels: 1,
    ));

    _micStarted = true;
    log('[SocketTranscriptionService] 🎤 Microphone stream started ($_sampleRate Hz)');

    _micSubscription = micStream.listen(
      (Uint8List segment) async {
        final result = await _turnDetector.process(segment);

        if (result.voicedWindows.isNotEmpty) {
          if (!_inSpeech) {
            log('[SocketTranscriptionService] 🗣️ Voice activity detected – streaming audio to server');
          }
          _inSpeech = true;
          for (final w in result.voicedWindows) {
            _ws?.sink.add(w); // send as binary frame
          }
        }

        if (result.speechEnded && _inSpeech) {
          log('[SocketTranscriptionService] 🔇 Speech ended – sending audioEnd');
          _ws?.sink.add(jsonEncode({'type': 'audioEnd'}));
          _inSpeech = false;
        }
      },
      onError: (dynamic e) {
        log('[SocketTranscriptionService] ❌ Malformed server message: $e');
        onError('Malformed server message: $e');
      },
    );
  }

  @override
  Future<void> stop() async {
    if (!_micStarted) return;

    await _recorder.stop();
    await _micSubscription?.cancel();
    _micSubscription = null;
    _micStarted = false;

    _turnDetector.reset();
    _inSpeech = false;

    // Notify backend that audio streaming has stopped.
    log('[SocketTranscriptionService] ⚙️ Sent stop control message');
    _ws?.sink.add(jsonEncode({'type': 'stop'}));
  }

  @override
  void clear() {
    _turns.clear();
  }

  @override
  void dispose() {
    log('[SocketTranscriptionService] 🗑️ Disposing');
    _manuallyClosed = true;
    _tearDownConnection();
  }

  // endregion TranscriptionService API

  // region Private helpers

  void _handleServerMessage(
    String msg,
    Function(String) onInterim,
    Function(String) onEndpoint,
    Function(dynamic) onError,
  ) {
    try {
      final decoded = jsonDecode(msg) as Map<String, dynamic>;
      final turnId = decoded['turnId'] as int?;
      final messageType = decoded['type'] as String?;

      log('[SocketTranscriptionService] ${_getMessageTypeEmoji(messageType)} Server message type=$messageType, turnId=$turnId');

      switch (decoded['type']) {
        case 'interim':
          final text = decoded['text'] as String?;
          if (text != null) {
            final fullTranscript = _updateTurnAndGetTranscript(turnId, text);
            onInterim(fullTranscript);
          }
          break;
        case 'endpoint':
          final text = decoded['text'] as String?;
          if (text != null) {
            final fullTranscript = _updateTurnAndGetTranscript(turnId, text);
            onEndpoint(fullTranscript);
          }
          break;
        case 'error':
          onError(decoded['message'] ?? 'Unknown error from server');
          break;
      }
    } catch (e) {
      // Non-JSON or unexpected format – treat as error.
      log('[SocketTranscriptionService] ❌ Malformed server message: $e');
      onError('Malformed server message: $e');
    }
  }

  String _getMessageTypeEmoji(String? messageType) {
    return switch (messageType) {
      'interim' => '⚡',
      'endpoint' => '🎯',
      'error' => '🚨',
      _ => '❓ (unknown type)'
    };
  }

  String _updateTurnAndGetTranscript(int? turnId, String text) {
    final actualTurnId = turnId ?? 0;
    // Ensure _turns list is long enough
    while (_turns.length <= actualTurnId) {
      _turns.add('');
    }
    // Update the specific turn
    _turns[actualTurnId] = text;
    // Generate and return full transcript
    return _turns.join('\n');
  }

  /// Builds a system prompt based on the current user profile.
  String _buildSystemPrompt() {
    const basePrompt = '''
Transcribe the following audio from an interview verbatim.
The interview topic is: {{INTERVIEW_TOPIC}}
{{INTERVIEW_LANGUAGE}}
Keep any English technical terms in English as spoken.
Do not include any timestamps in the output.
Output ONLY the transcript text, nothing else.
If the input audio contains no voices (silence or noise only), respond with an empty string.
''';

    final userProfile = userProfileManager.userProfile;

    final interviewLanguages = userProfile?.interviewLanguages;
    final interviewTopic = userProfile?.interviewTopic ?? 'Not specified';

    String interviewLanguageLine = '';
    if (interviewLanguages != null && interviewLanguages.isNotEmpty) {
      if (interviewLanguages.length == 1) {
        interviewLanguageLine =
            'The interview language is: ${interviewLanguages.first.aiName}';
      } else {
        final languagesStr = interviewLanguages.map((l) => l.aiName).join(', ');
        interviewLanguageLine = 'The interview languages are: $languagesStr';
      }
    }

    return basePrompt
        .replaceAll('{{INTERVIEW_TOPIC}}', interviewTopic)
        .replaceAll('{{INTERVIEW_LANGUAGE}}', interviewLanguageLine);
  }

  // endregion Private helpers

  // region Reconnection helpers

  Future<void> _restartWebSocket() async {
    if (_isReconnecting || _manuallyClosed) return;
    _isReconnecting = true;

    await _tearDownConnection();

    final computedDelayMs = (_initialBackoffMs * (1 << _reconnectAttempts))
        .clamp(_initialBackoffMs, _maxBackoffMs);

    log('[SocketTranscriptionService] 🔄 Reconnecting WebSocket in '
        '${computedDelayMs}ms (attempt $_reconnectAttempts)');

    await Future.delayed(Duration(milliseconds: computedDelayMs));

    final onInterim = _cachedOnInterim;
    final onEndpoint = _cachedOnEndpoint;
    final onErr = _cachedOnError;

    if (onInterim != null && onEndpoint != null && onErr != null) {
      try {
        await initialize(
          onInterim: onInterim,
          onEndpoint: onEndpoint,
          onError: onErr,
          languages: _cachedLanguages,
        );

        // Connection succeeded, reset attempts counter.
        _reconnectAttempts = 0;
      } catch (e, st) {
        log('[SocketTranscriptionService] ⚠️ Reconnect attempt failed: $e',
            stackTrace: st);

        // Exponential backoff: increment attempts but don't overflow.
        if (_reconnectAttempts < 31) {
          _reconnectAttempts += 1;
        }
      }
    }

    _isReconnecting = false;
  }

  Future<void> _tearDownConnection() async {
    try {
      await _micSubscription?.cancel();
    } catch (_) {}
    _micSubscription = null;

    try {
      if (_micStarted) await _recorder.stop();
    } catch (_) {}
    _micStarted = false;

    _turnDetector.reset();
    _inSpeech = false;

    try {
      await _ws?.sink.close();
    } catch (_) {}
    _ws = null;
  }
// endregion Reconnection helpers
}

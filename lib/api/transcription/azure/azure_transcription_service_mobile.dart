import '../../../utils/events/events.dart';
import 'azure_transcription_service.dart';

class AzureTranscriptionServiceImplementation
    implements AzureTranscriptionService {
  @override
  bool get isListening => false;

  @override
  Future<void> startListening({
    required List<String> languages,
    required Function(String transcription) onTranscribing,
    required Function(String transcription) onTranscribed,
    required Function() onNoMatch,
    required Function(String cancelReason) onCanceled,
    required Function() onSessionStopped,
  }) async {
    logError('Azure Speech SDK is not available for mobile platforms.');
  }

  @override
  void stopListening() {
    // No-op
  }

  @override
  void clear() {
    // No-op for mobile
  }
}

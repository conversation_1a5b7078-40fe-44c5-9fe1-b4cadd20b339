import 'dart:async';
// ignore: deprecated_member_use, avoid_web_libraries_in_flutter
import 'dart:js' as js;

import 'azure_transcription_service.dart';

class AzureTranscriptionServiceImplementation
    implements AzureTranscriptionService {
  bool _isListening = false;
  late js.JsObject _speechSDK;
  late js.JsObject _transcriber;
  js.JsObject? _audioStream;
  String _accumulatedEndpointText = '';

  List<String> _languages = [];

  @override
  bool get isListening => _isListening;

  AzureTranscriptionServiceImplementation() {
    _initializeSpeechSDK();
  }

  void _initializeSpeechSDK() {
    if (js.context.hasProperty('SpeechSDK')) {
      _speechSDK = js.context['SpeechSDK'];
    } else {
      throw Exception('Failed to load Speech SDK');
    }
  }

  @override
  Future<void> startListening({
    required List<String> languages,
    required Function(String transcription) onTranscribing,
    required Function(String transcription) onTranscribed,
    required Function() onNoMatch,
    required Function(String cancelReason) onCanceled,
    required Function() onSessionStopped,
  }) async {
    if (_isListening) return;

    _languages = languages;

    final speechConfig = _createSpeechConfig();
    final audioConfig = await _createAudioConfig();
    final languageConfig = _createLanguageConfig();

    _transcriber =
        _createTranscriber(speechConfig, languageConfig, audioConfig);

    _setupEventHandlers(
      onTranscribing: onTranscribing,
      onTranscribed: onTranscribed,
      onNoMatch: onNoMatch,
      onCanceled: onCanceled,
      onSessionStopped: onSessionStopped,
    );

    _transcriber.callMethod('startTranscribingAsync');
    _isListening = true;
  }

  @override
  void stopListening() {
    if (!_isListening) return;

    _transcriber.callMethod('stopTranscribingAsync');
    _transcriber.callMethod('close');
    _audioStream?.callMethod('getTracks').forEach((track) {
      track.callMethod('stop');
    });
    _isListening = false;
    _accumulatedEndpointText = '';
  }

  @override
  void clear() {
    _accumulatedEndpointText = '';
  }

  js.JsObject _createSpeechConfig() {
    final speechConfig = _speechSDK['SpeechConfig'].callMethod(
      'fromSubscription',
      [
        AzureTranscriptionService.subscriptionKey,
        AzureTranscriptionService.region
      ],
    );
    _configureProperties(speechConfig);
    return speechConfig;
  }

  void _configureProperties(js.JsObject speechConfig) {
    // Enable speaker diarization
    speechConfig.callMethod(
        'setProperty', ['ConversationTranscriptionInRoomAndOnline', 'true']);
    // Differentiate speakers without providing voice samples
    speechConfig
        .callMethod('setProperty', ['DifferentiateGuestSpeakers', 'true']);

    // When there is only one language, setting LanguageIdMode causes some languages like ar-EG to not be recognized.
    if (_languages.length > 1) {
      speechConfig.callMethod('setProperty',
          ['SpeechServiceConnection_LanguageIdMode', 'Continuous']);
    }
  }

  Future<js.JsObject> _createAudioConfig() async {
    _audioStream = await _startAudioStream();
    return _speechSDK['AudioConfig']
        .callMethod('fromStreamInput', [_audioStream]);
  }

  js.JsObject _createLanguageConfig() {
    return _speechSDK['AutoDetectSourceLanguageConfig'].callMethod(
      'fromLanguages',
      [js.JsArray.from(_languages)],
    );
  }

  js.JsObject _createTranscriber(js.JsObject speechConfig,
      js.JsObject languageConfig, js.JsObject audioConfig) {
    return _speechSDK['ConversationTranscriber'].callMethod('FromConfig', [
      speechConfig,
      languageConfig,
      audioConfig,
    ]);
  }

  void _setupEventHandlers({
    required Function(String transcription) onTranscribing,
    required Function(String transcription) onTranscribed,
    required Function() onNoMatch,
    required Function(String cancelReason) onCanceled,
    required Function() onSessionStopped,
  }) {
    _transcriber['transcribing'] = js.allowInterop((_, event) {
      final transcription = _formatTranscription(
        event['result']['speakerId'],
        event['result']['text'],
        event['result']['language'],
      );
      final accumulatedInterimText = _accumulatedEndpointText + transcription;
      onTranscribing(accumulatedInterimText);
    });

    _transcriber['transcribed'] = js.allowInterop((_, event) {
      final resultReason = _speechSDK['ResultReason'];
      if (event['result']['reason'] == resultReason['RecognizedSpeech']) {
        final transcription = _formatTranscription(
          event['result']['speakerId'],
          event['result']['text'],
          event['result']['language'],
        );
        _accumulatedEndpointText += '$transcription\n';
        onTranscribed(_accumulatedEndpointText);
      } else if (event['result']['reason'] == resultReason['NoMatch']) {
        onNoMatch();
      }
    });

    _transcriber['canceled'] = js.allowInterop((_, event) {
      onCanceled(event['reason']);
      stopListening();
    });

    _transcriber['sessionStopped'] = js.allowInterop((_, event) {
      onSessionStopped();
      stopListening();
    });
  }

  String _formatTranscription(
      String? speakerId, String? text, String? language) {
    return '${speakerId ?? ''} (${language ?? ''}): ${text ?? ''}';
  }

  Future<js.JsObject> _startAudioStream() async {
    final mediaDevices = js.context['navigator']['mediaDevices'];
    final constraints = js.JsObject.jsify({
      'audio': {'echoCancellation': false}
    });
    return await jsPromiseToFuture(
        mediaDevices.callMethod('getUserMedia', [constraints]));
  }

  Future<T> jsPromiseToFuture<T>(dynamic jsPromise) {
    final completer = Completer<T>();
    jsPromise.callMethod(
      'then',
      [js.allowInterop((result) => completer.complete(result as T))],
    ).callMethod(
      'catch',
      [js.allowInterop((error) => completer.completeError(error))],
    );
    return completer.future;
  }
}

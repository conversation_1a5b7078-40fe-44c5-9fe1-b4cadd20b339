import 'package:interview_hammer/api/management/remote_config.dart';

import 'azure_transcription_service_web.dart'
    if (dart.library.io) 'azure_transcription_service_mobile.dart'
    as azure_platform;

abstract class AzureTranscriptionService {
  static final String subscriptionKey = remoteConfig.getString(KEY_AZURE_KEY);
  static final String region = remoteConfig.getString(KEY_AZURE_REGION);

  bool get isListening;

  static AzureTranscriptionService create() {
    return azure_platform.AzureTranscriptionServiceImplementation();
  }

  Future<void> startListening({
    required List<String> languages,
    required Function(String transcription) onTranscribing,
    required Function(String transcription) onTranscribed,
    required Function() onNoMatch,
    required Function(String cancelReason) onCanceled,
    required Function() onSessionStopped,
  });

  void stopListening();

  void clear();
}

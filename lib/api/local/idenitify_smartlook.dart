import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_smartlook/flutter_smartlook.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';

import '../management/management_api.dart';

const _DEBUG_COLLECTION = 'debug_smartlook';
const _PROCESSED_USERS_DOC = 'processed_users';

// Only run in debug to identify smartlook users
// need it since this is a premium feature on web
// if identified on the mobile app, it will be identified on the web as well
// !! make sure to comment if (kDebugMode) return; from [initSmartlook] in lib/utils/smartlook/smartlook.dart
// also close the app and start a new session
Future<void> debugIdentifySmartUsers() async {
  final debugRef = firestore.collection(_DEBUG_COLLECTION);
  final processedUsersDoc = debugRef.doc(_PROCESSED_USERS_DOC);

  // Get the list of processed users
  final processedUsersSnapshot = await processedUsersDoc.get();
  final processedUsers =
      (processedUsersSnapshot.data()?['uids'] as List<dynamic>?) ?? [];

  // Initialize the counters
  int successfulUpdates = 0;
  int skippedUsers = 0;
  const int limit = 5; // Set the desired limit here

  try {
    final querySnapshot = await firestore.collection("users_profiles").get();

    final totalUsers = querySnapshot.docs.length;
    d("Number of users fetched: $totalUsers");

    for (var result in querySnapshot.docs) {
      if (successfulUpdates >= limit) break; // Stop if we've reached the limit

      var email = result['email'] as String?;
      var uid = result.id;

      // Uncomment these lines if you want to use hardcoded values for testing
      // var email = "<EMAIL>";
      // var uid = "8TulECwlmGfEh50WpwSPyzu14913";

      // Skip if the user has already been processed
      if (processedUsers.contains(uid)) {
        d('Skipping already processed user: $email (UID: $uid)');
        skippedUsers++;
        continue;
      }

      final smartlook = Smartlook.instance;
      if (email != null) {
        await smartlook.user.openNew();
        await smartlook.user.setIdentifier(uid);
        await smartlook.user.setEmail(email);

        successfulUpdates++;

        // Add the user to the processed list
        processedUsers.add(uid);

        // Log message with counter and limit
        d('[$successfulUpdates/$limit] Identified Smartlook user: $email (UID: $uid)');

        await Future.delayed(Duration(seconds: 10));
      } else {
        d('Email is null for user with UID: $uid');
      }
    }

    // Update the processed users list in Firestore
    await processedUsersDoc
        .set({'uids': processedUsers}, SetOptions(merge: true));

    // Calculate remaining users
    int remainingUsers = totalUsers - (successfulUpdates + skippedUsers);

    d('Finished identifying Smartlook users.');
    d('Successful updates: $successfulUpdates');
    d('Skipped users: $skippedUsers');
    d('Remaining users to process: $remainingUsers');
    d('Total users: $totalUsers');
  } catch (e) {
    d('Error in debugIdentifySmartUsers: $e');
  }
}

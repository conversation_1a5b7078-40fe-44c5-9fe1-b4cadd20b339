import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:interview_hammer/utils/events/events.dart';

final remoteConfig = FirebaseRemoteConfig.instance;

const KEY_DISCORD_LINK = "discord_link";
const KEY_UPDATE_LINK = "update_link";

const KEY_AVAILABLE_MINUTES = "available_minutes";
const KEY_SESSION_MAX_MINUETS = "session_max_minutes";

const KEY_MIN_SUPPORTED_VERSION = "min_supported_version";
const KEY_MIN_RECOMMENDED_VERSION = "min_recommended_version";

const KEY_AI_CONFIG = "ai_config1";

const KEY_AZURE_KEY = "azure_key";
const KEY_AZURE_REGION = "azure_region";
const KEY_DG_API_KEY = "dg_api_key1";
const KEY_TRANSCRIPTION_WS_URL = "transcription_ws_url";

const KEY_MOBILE_TOOLTIP = "mobile_tooltip";
const KEY_WEB_TOOLTIP = "web_tooltip";
const KEY_DESKTOP_DOWNLOAD_LINK = "desktop_download_link";
const KEY_TUTORIAL_VIDEO_URL = "tutorial_video_url";
const KEY_DELETE_ACCOUNT_URL = "delete_account_url";
const KEY_WEB_APP_URL = "web_app_url";

Future<void> initRemoteConfig() async {
  await remoteConfig.setConfigSettings(RemoteConfigSettings(
    fetchTimeout: const Duration(minutes: 1),
    minimumFetchInterval:
        kDebugMode ? const Duration(seconds: 10) : const Duration(minutes: 10),
  ));

  await remoteConfig.setDefaults(const {
    KEY_DISCORD_LINK: "https://discord.gg/64ksPyA9W2",
    KEY_UPDATE_LINK: "https://interviewhammer.com/download/android",
    KEY_AVAILABLE_MINUTES: 30,
    KEY_SESSION_MAX_MINUETS: 15,
    KEY_MIN_SUPPORTED_VERSION: '1.9.0',
    KEY_MIN_RECOMMENDED_VERSION: '1.9.0',
    KEY_AI_CONFIG: '''{
  "baseUrl": "https://litedev.meetingaitools.com",
  "apiKey": "sk-J--S5q2AN323UnA3mFSD4A",
  "imageModel": "gemini-2.5-flash",
  "questionDetectionModel": "gemini-2.5-flash-lite",
  "answerGenerationModel": "gemini-2.5-flash",
  "modelFallbacks": {
    "gemini-2.5-pro": "gpt-4.1",
    "gemini-2.5-flash": "gpt-4.1-mini",
    "gpt-4o": "gpt-4.1-mini"
  },
  "reasoningModels": ["o1", "o3-mini", "o3", "o4-mini"],
  "nonOpenAiModels": ["gemini"]
}''',
    KEY_AZURE_KEY: "c90301597855489dba501eaf7fdd6961",
    KEY_AZURE_REGION: "eastus",
    KEY_DG_API_KEY: "ed6184a53cc6d50f5ed0a2e57d87512370539548",
    KEY_TRANSCRIPTION_WS_URL: "wss://server2.meetingaitools.com/transcribe",
    KEY_MOBILE_TOOLTIP:
        "Important: Use a different device for the interview, not this {{platform}} device.\n"
            "• {{platform}} only allows one app to use the microphone at a time.\n"
            "• Don't use headphones - the app uses the device's mic, not system audio.",
    KEY_WEB_TOOLTIP:
        "Tip: You can use this device for the interview, but keep in mind:\n"
            "• If you experience audio issues, try using a different device.\n"
            "• Don't use headphones - the app uses the device's mic, not system audio.\n"
            "• Some devices only allow one app to use the mic at a time.",
    KEY_DESKTOP_DOWNLOAD_LINK: "https://interviewhammer.com/download/desktop",
    KEY_TUTORIAL_VIDEO_URL: "https://www.youtube.com/watch?v=_GFYSazIRHE",
    KEY_DELETE_ACCOUNT_URL:
        "https://docs.google.com/forms/d/e/1FAIpQLSfE8pi3lQ0BajJV9d5ygA_lRhKiivpIrqWV11zkMHUkp5kiGA/viewform",
    KEY_WEB_APP_URL: "https://app.interviewhammer.com",
  });

  try {
    await remoteConfig.fetchAndActivate();
  } catch (e) {
    logError(e);
  }
}

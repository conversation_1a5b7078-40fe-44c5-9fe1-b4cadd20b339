import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:interview_hammer/model/management/user_profile.dart';
import 'package:rxdart/rxdart.dart';

import 'management_api.dart';

// Global reference to the singleton instance
UserProfileManager userProfileManager = UserProfileManager.instance;

class UserProfileManager {
  static final UserProfileManager _instance = UserProfileManager._internal();

  static UserProfileManager get instance => _instance;

  UserProfileManager._internal();

  UserProfile? userProfile;

  final BehaviorSubject<UserProfile?> _userProfileSubject =
      BehaviorSubject<UserProfile?>();
  StreamSubscription<DocumentSnapshot>? _userProfileSubscription;

  Stream<UserProfile?> get userProfileStream => _userProfileSubject.stream;

  void initialize() {
    final userId = getCurrentUserUid();
    _userProfileSubscription?.cancel();
    _userProfileSubscription = FirebaseFirestore.instance
        .collection('users_profiles')
        .doc(userId)
        .snapshots()
        .listen((documentSnapshot) {
      if (documentSnapshot.exists) {
        final userProfile = UserProfile.fromFirestore(documentSnapshot, null);
        this.userProfile = userProfile;
        _userProfileSubject.add(userProfile);
      } else {
        _userProfileSubject.add(null);
      }
    });
  }

  void dispose() {
    _userProfileSubscription?.cancel();
    _userProfileSubject.close();
  }
}

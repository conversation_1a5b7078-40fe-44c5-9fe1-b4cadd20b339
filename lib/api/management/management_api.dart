import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:interview_hammer/api/management/remote_config.dart';

import '../../model/management/answer_settings.dart';
import '../../model/management/language.dart';
import '../../model/management/personal_info.dart';
import '../../model/management/user_profile.dart';
import 'user_profile_manager.dart';

final firestore = FirebaseFirestore.instance;

final colUserProfiles = firestore.collection('users_profiles');

//region user
User? getCurrentFirebaseUser() => FirebaseAuth.instance.currentUser;

String? getCurrentUserUid() => getCurrentFirebaseUser()?.uid;

Future<UserProfile?> getProfile() async {
  final userProfile = await colUserProfiles
      .doc(getCurrentUserUid())
      .withConverter(
        fromFirestore: UserProfile.fromFirestore,
        toFirestore: (UserProfile userProfile, _) =>
            userProfile.toFirestore(false),
      )
      .get();

  // d("userProfile = ${userProfile.data()}");
  return userProfile.data();
}

Future<void> createProfile(
  List<Language> interviewLanguages,
  Language answerLanguage,
  String marketingSource,
) async {
  await colUserProfiles
      .doc(getCurrentUserUid())
      .withConverter(
        fromFirestore: UserProfile.fromFirestore,
        toFirestore: (UserProfile userProfile, _) =>
            userProfile.toFirestore(true),
      )
      .set(
        UserProfile(
          uid: getCurrentUserUid(),
          email: getCurrentFirebaseUser()!.email,
          planType: PLAN_TYPE_FREE,
          availableMinutes: remoteConfig.getInt(KEY_AVAILABLE_MINUTES),
          usedMinutes: 0,
          sessionMaxMinutes: remoteConfig.getInt(KEY_SESSION_MAX_MINUETS),

          // setting up screen values
          interviewLanguages: interviewLanguages,
          answerLanguage: answerLanguage,
          marketingSource: marketingSource,

          // default values
          automaticQuestionAnswering: true,
          showTranscript: true,
          answerSettings: AnswerSettings(),
        ),
        SetOptions(merge: true),
      );
}

// endregion user

// region settings
Future<void> updateUser(UserProfile profile) async {
  await colUserProfiles
      .doc(getCurrentUserUid())
      .withConverter(
        fromFirestore: UserProfile.fromFirestore,
        toFirestore: (UserProfile userProfile, _) =>
            userProfile.toFirestore(false),
      )
      .set(profile, SetOptions(merge: true));
}

// [merge: true] don't work for nested objects, so we need to update the whole object.
void updatePersonalInfo(PersonalInfo Function(PersonalInfo) updateFunction) {
  final updatedPersonalInfo =
      updateFunction(userProfileManager.userProfile!.personalInfoData!);
  updateUser(UserProfile(personalInfoData: updatedPersonalInfo));
}

void updateAnswerSettings(
    AnswerSettings Function(AnswerSettings) updateFunction) {
  final updatedAnswerSettings =
      updateFunction(userProfileManager.userProfile!.answerSettings!);
  updateUser(UserProfile(answerSettings: updatedAnswerSettings));
}
// endregion settings

// region trail
Future<void> updateUsedMinuets() async {
  await colUserProfiles.doc(getCurrentUserUid()).update({
    'usedMinutes': FieldValue.increment(1),
  });
}
// endregion trail

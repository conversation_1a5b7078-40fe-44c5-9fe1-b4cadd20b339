import 'dart:math';

import 'package:flutter/material.dart';
import 'package:interview_hammer/model/management/user_profile.dart';
import 'package:interview_hammer/screens/plan/payment_status_chip.dart';
import 'package:interview_hammer/screens/plan/purchase_service.dart';
import 'package:interview_hammer/utils/string_utils.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../api/management/remote_config.dart';
import '../../widgets/custom_card.dart';
import '../../widgets/user_profile_stream_builder.dart';

class PlanDetailsScreen extends StatefulWidget {
  const PlanDetailsScreen({super.key});

  @override
  State<PlanDetailsScreen> createState() => _PlanDetailsScreenState();
}

class _PlanDetailsScreenState extends State<PlanDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Plan Details',
          style: textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
      ),
      body: UserProfileStreamBuilder(
        builder: (context, profile) {
          final planType = profile.planType ?? PLAN_TYPE_FREE;
          final paymentStatus = profile.paymentStatus ?? 'Not started';

          final isFree = planType.toLowerCase() == PLAN_TYPE_FREE;

          final usedMinutes = profile.usedMinutes?.toString() ?? "0";
          final sessionMaxMinutes = isFree
              ? profile.sessionMaxMinutes?.toString() ?? "0"
              : "Unlimited";
          final remainingMinutes = max(
              (profile.availableMinutes ?? 0) - (profile.usedMinutes ?? 0), 0);
          final remainingMinutesText =
              isFree ? remainingMinutes.toString() : "Unlimited";
          final expirationDate = profile.planExpirationDate != null
              ? DateFormat('MMMM d, yyyy')
                  .format(profile.planExpirationDate!.toDate())
              : 'N/A';

          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Your Current Plan',
                    style: textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '(One time payment, no subscription)',
                    style: textTheme.bodyLarge?.copyWith(
                      fontStyle: FontStyle.italic,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  SizedBox(height: 16),
                  PaymentStatusChip(status: paymentStatus),
                  SizedBox(height: 24),
                  CustomCard(
                    child: Column(
                      children: [
                        PlanDetailTile(
                          title: 'Plan Type',
                          value: planType.capitalizeEachWord(),
                          isPremium: !isFree,
                        ),
                        PlanDetailTile(
                          title: 'Used Minutes',
                          value: usedMinutes,
                          isPremium: !isFree,
                        ),
                        PlanDetailTile(
                          title: 'Remaining Minutes',
                          value: remainingMinutesText,
                          isPremium: !isFree,
                        ),
                        PlanDetailTile(
                          title: 'Session Max Minutes',
                          value: sessionMaxMinutes,
                          isPremium: !isFree,
                        ),
                        if (!isFree)
                          PlanDetailTile(
                            title: 'Expires On',
                            value: expirationDate,
                            isPremium: true,
                          ),
                      ],
                    ),
                  ),
                  SizedBox(height: 32),
                  if (isFree) buildUpgradeBenefits(),
                  SizedBox(height: 24),
                  if (isFree) _buildUpgradePlanButton(profile),
                  if (!isFree) _buildCancelPlanButton(profile),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget buildUpgradeBenefits() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Upgrade to Premium and Get:',
              style: textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.primary,
              ),
            ),
            SizedBox(height: 16),
            BenefitItem(text: 'Unlimited minutes'),
            BenefitItem(text: 'Unlimited session duration'),
            BenefitItem(text: 'Priority support'),
            BenefitItem(text: 'Highest quality AI responses'),
          ],
        ),
      ),
    );
  }

  Widget _buildUpgradePlanButton(UserProfile profile) {
    var textTheme = Theme.of(context).textTheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isDark
              ? [
                  Color(0xFF9C27B0), // Purple
                  Color(0xFF673AB7), // Deep Purple
                ]
              : [
                  Color(0xFF1976D2), // Light Blue
                  Color(0xFF2196F3), // Blue
                ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Color(0xFF9C27B0)
                    .withValues(alpha: 0.3) // Purple shadow for dark mode
                : Color(0xFF1976D2).withValues(alpha: 0.25),
            // Blue shadow for light mode
            blurRadius: isDark ? 8 : 6,
            offset: Offset(0, isDark ? 4 : 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => PurchaseService().handlePurchase(context, profile),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 20),
            child: Center(
              child: Text(
                'Upgrade to Premium',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                  letterSpacing: isDark ? 0.8 : 0.5,
                  shadows: [
                    Shadow(
                      color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.2),
                      offset: Offset(0, 1),
                      blurRadius: isDark ? 2 : 1,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCancelPlanButton(UserProfile profile) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text('Cancel Plan'),
                content: Text(
                    'Your Premium plan was a one-time purchase, not a subscription. You will not be charged again automatically.\n\n'
                    'If you would like to request a refund, please join our Discord server and contact support. '
                    'We typically process refund requests within 24 hours.'),
                actions: <Widget>[
                  TextButton(
                    child: Text('Close'),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                  TextButton(
                    child: Text('Join Discord'),
                    onPressed: () async {
                      final upgradeLink =
                          remoteConfig.getString(KEY_DISCORD_LINK);
                      await launchUrl(Uri.parse(upgradeLink));
                      if (context.mounted) Navigator.of(context).pop();
                    },
                  ),
                ],
              );
            },
          );
        },
        style: TextButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          foregroundColor: colorScheme.error,
        ),
        child: Text(
          'Cancel Plan',
          style: textTheme.bodyMedium,
        ),
      ),
    );
  }
}

class PlanDetailTile extends StatelessWidget {
  final String title;
  final String value;
  final bool isPremium;

  const PlanDetailTile({
    super.key,
    required this.title,
    required this.value,
    required this.isPremium,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return ListTile(
      title: Text(
        title,
        style: textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      trailing: Text(
        value,
        style: textTheme.titleMedium?.copyWith(
          color: isPremium ? colorScheme.primary : colorScheme.onSurface,
          fontWeight: isPremium ? FontWeight.bold : null,
        ),
      ),
    );
  }
}

class BenefitItem extends StatelessWidget {
  final String text;

  const BenefitItem({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: colorScheme.primary, size: 20),
          SizedBox(width: 8),
          Text(
            text,
            style: textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}

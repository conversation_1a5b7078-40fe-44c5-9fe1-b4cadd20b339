import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/screens/plan/plan_details_screen.dart';
import 'package:interview_hammer/utils/events/events.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../api/management/remote_config.dart';
import '../../model/management/user_profile.dart';
import '../../utils/platform_utils.dart';
import '../home/<USER>';
import 'checkout_webview_screen.dart';

class PurchaseService {
  static const String _androidApiKey = "goog_TWMdgWpipCLZXGsTyfDSylkBnfL";
  static const String _iosApiKey = "appl_gJJiPgSNyJfIOeeFqdkTGFfTdid";
  static const String ENTITLEMENT_ID = 'monthly_unlimited';

  bool _isLoadingDialogShowing = false;

  Future<void> handlePurchase(BuildContext context, UserProfile profile) async {
    if (kIsNativeMobile || kIsNativeMacOS) {
      await _handleNativePurchase(context, profile);
    } else {
      _handleWebPurchase(context, profile);
    }
  }

  Future<void> _handleNativePurchase(
      BuildContext context, UserProfile profile) async {
    _showLoadingDialog(context);

    try {
      await _configurePurchases(profile);
      final offerings = await Purchases.getOfferings();

      if (!context.mounted) return;

      if (offerings.current == null) {
        _hideLoadingDialog(context);
        _showMessage(context, 'No offerings available at the moment');
        return;
      }

      final package = offerings.current!.availablePackages.first;
      d("package = $package");

      try {
        CustomerInfo customerInfo = await Purchases.purchasePackage(package);
        d("customerInfo.entitlements.active = ${customerInfo.entitlements.active}");

        if (customerInfo.entitlements.active.containsKey(ENTITLEMENT_ID)) {
          if (context.mounted) {
            _hideLoadingDialog(context);
            _showMessage(context, 'Thank you for purchasing Premium!');
          }
        }
      } on PlatformException catch (e) {
        final errorCode = PurchasesErrorHelper.getErrorCode(e);
        if (errorCode != PurchasesErrorCode.purchaseCancelledError) {
          if (context.mounted) {
            _handleError(context, 'PlatformException: ${e.message}');
          }
        }
      }
    } catch (e) {
      if (context.mounted) _handleError(context, 'Error: ${e.toString()}');
    }

    if (context.mounted) _hideLoadingDialog(context);
  }

  void _handleWebPurchase(BuildContext context, UserProfile profile) {
    push(
      context,
      CheckoutWebViewPage(
        url:
            'https://meetingaitools.com/checkout?email=${profile.email}&userId=${profile.uid}',
        onNavigationComplete: (url) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            pushAndRemoveUntil(context, PlanDetailsScreen(), HomeScreen());
          });
        },
      ),
    );
  }

  Future<void> _configurePurchases(UserProfile profile) async {
    PurchasesConfiguration configuration;
    if (Platform.isAndroid) {
      configuration = PurchasesConfiguration(_androidApiKey);
    } else if (Platform.isIOS || Platform.isMacOS) {
      configuration = PurchasesConfiguration(_iosApiKey);
    } else {
      throw UnsupportedError("Unsupported platform");
    }
    configuration.appUserID = profile.uid;
    await Purchases.configure(configuration);

    Purchases.setAttributes({"email": profile.email ?? "Email not set"});
  }

  void _showLoadingDialog(BuildContext context) {
    _isLoadingDialogShowing = true;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(24.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Processing purchase...'),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _hideLoadingDialog(BuildContext context) {
    if (_isLoadingDialogShowing && context.mounted) {
      Navigator.of(context).pop();
      _isLoadingDialogShowing = false;
    }
  }

  void _showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _handleError(BuildContext context, String message) {
    _hideLoadingDialog(context);
    _showErrorDialog(context, message);
    logError(message);
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Purchase Error'),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: Text('OK'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('Contact Support'),
              onPressed: () {
                final discordLink = remoteConfig.getString(KEY_DISCORD_LINK);
                launchUrl(Uri.parse(discordLink));
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:interview_hammer/utils/string_utils.dart';

class PaymentStatusChip extends StatelessWidget {
  final String status;

  const PaymentStatusChip({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    Color backgroundColor;
    Color textColor;

    switch (status.toLowerCase()) {
      case 'completed':
        backgroundColor = isDark ? Colors.green[900]! : Colors.green[100]!;
        textColor = isDark ? Colors.green[100]! : Colors.green[800]!;
        break;
      case 'pending':
        backgroundColor = isDark ? Colors.orange[900]! : Colors.orange[100]!;
        textColor = isDark ? Colors.orange[100]! : Colors.orange[800]!;
        break;
      case 'failed':
        backgroundColor = isDark ? Colors.red[900]! : Colors.red[100]!;
        textColor = isDark ? Colors.red[100]! : Colors.red[800]!;
        break;
      default:
        backgroundColor = isDark ? Colors.grey[900]! : Colors.grey[100]!;
        textColor = isDark ? Colors.grey[100]! : Colors.grey[800]!;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        'Payment Status: ${status.capitalize()}',
        style: theme.textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: textColor,
        ),
      ),
    );
  }
}

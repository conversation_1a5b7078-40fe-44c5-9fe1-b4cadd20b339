import 'package:flutter/material.dart';
import 'package:interview_hammer/screens/plan/webview/webview_widget.dart';

import '../../widgets/user_profile_stream_builder.dart';

class CheckoutWebViewPage extends StatelessWidget {
  final String url;
  final Function(String) onNavigationComplete;

  const CheckoutWebViewPage({
    super.key,
    required this.url,
    required this.onNavigationComplete,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Payment'),
      ),
      body: UserProfileStreamBuilder(
        builder: (context, profile) {
          final paymentStatus = profile.paymentStatus;

          if (paymentStatus == 'completed') {
            onNavigationComplete(url);
          }

          return WebViewWidget(
            url: url,
            onNavigationComplete: (p0) {
              //do nothing
            },
          );
        },
      ),
    );
  }
}

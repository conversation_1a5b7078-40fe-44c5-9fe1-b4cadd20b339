import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'webview_config.dart';

class WebViewPlatform extends StatefulWidget {
  final String url;
  final Function(String) onNavigationComplete;

  const WebViewPlatform({
    super.key,
    required this.url,
    required this.onNavigationComplete,
  });

  @override
  State<WebViewPlatform> createState() => _WebViewPlatformState();
}

class _WebViewPlatformState extends State<WebViewPlatform> {
  late WebViewController _webViewController;

  @override
  void initState() {
    super.initState();
    _initializeWebViewController();
  }

  void _initializeWebViewController() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // You can update a loading bar here if needed
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {},
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            if (WebViewConfig.isSuccessUrl(request.url)) {
              widget.onNavigationComplete(request.url);
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return WebViewWidget(controller: _webViewController);
  }
}

// ignore: avoid_web_libraries_in_flutter, deprecated_member_use
import 'dart:html' as html;
import 'dart:ui_web' as ui_web;

import 'package:flutter/material.dart';

import 'webview_config.dart';

class WebViewPlatform extends StatefulWidget {
  final String url;
  final Function(String) onNavigationComplete;

  const WebViewPlatform({
    super.key,
    required this.url,
    required this.onNavigationComplete,
  });

  @override
  State<WebViewPlatform> createState() => _WebViewPlatformState();
}

class _WebViewPlatformState extends State<WebViewPlatform> {
  late final String _viewType;
  late final html.IFrameElement _iframeElement;

  @override
  void initState() {
    super.initState();
    _viewType = 'webview-${DateTime.now().millisecondsSinceEpoch}';
    _iframeElement = html.IFrameElement()
      ..src = widget.url
      ..style.border = 'none';

    _iframeElement.onLoad.listen((event) {
      final String currentUrl = _iframeElement.src ?? '';
      if (WebViewConfig.isSuccessUrl(currentUrl)) {
        widget.onNavigationComplete(currentUrl);
      }
    });

    // Register the view factory
    // ignore: undefined_prefixed_name
    ui_web.platformViewRegistry.registerViewFactory(
      _viewType,
      (int viewId) => _iframeElement,
    );
  }

  @override
  Widget build(BuildContext context) {
    return HtmlElementView(
      viewType: _viewType,
    );
  }
}

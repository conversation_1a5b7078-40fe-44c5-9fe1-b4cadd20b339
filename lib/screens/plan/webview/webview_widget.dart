import 'package:flutter/material.dart';

import 'webview_stub.dart'
    if (dart.library.html) 'webview_web.dart'
    if (dart.library.io) 'webview_mobile.dart';

class WebViewWidget extends StatelessWidget {
  final String url;
  final Function(String) onNavigationComplete;

  const WebViewWidget({
    super.key,
    required this.url,
    required this.onNavigationComplete,
  });

  @override
  Widget build(BuildContext context) {
    return WebViewPlatform(
      url: url,
      onNavigationComplete: onNavigationComplete,
    );
  }
}

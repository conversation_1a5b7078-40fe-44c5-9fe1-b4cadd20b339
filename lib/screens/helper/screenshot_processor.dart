import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:image/image.dart' as img;
import 'package:interview_hammer/services/screenshot/web_screenshot_service.dart';
import 'package:interview_hammer/utils/platform_utils.dart';
import 'package:interview_hammer/utils/screen_sharing.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:screen_capturer/screen_capturer.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../display_settings_screen.dart';

class ScreenshotProcessor {
  static const int maxResolution = 2048;
  final _webScreenshotService = getWebScreenshotService();

  /// Initializes the processor, particularly for web platforms.
  /// Throws [ScreenshotInitializationException] if initialization fails.
  Future<void> init() async {
    if (kIsWebDesktop) {
      if (!_webScreenshotService.isSupported) {
        throw ScreenshotInitializationException(
            "Screenshots not supported on this platform.");
      }
      try {
        bool success = await _webScreenshotService.initialize();
        if (!success) {
          throw ScreenshotInitializationException(
              "Screenshot service initialization failed.");
        }
      } catch (e) {
        throw ScreenshotInitializationException(
            _webScreenshotService.getErrorMessage(e as Exception));
      }
    }
  }

  void dispose() {
    _webScreenshotService.dispose();
  }

  Future<ProcessedScreenshot?> takeAndProcessScreenshot() async {
    try {
      Uint8List? rawBytesForNative;
      String? imagePath;

      if (kIsWebDesktop) {
        // Web/Electron path: Screenshot is already resized in JS
        try {
          final webResult = await _webScreenshotService.takeScreenshot();

          // Directly return the processed data from the service
          return ProcessedScreenshot(
            bytes: webResult.bytes,
            width: webResult.width,
            height: webResult.height,
            imagePath: null, // No local path for web
          );
        } on ScreenshotException catch (e) {
          d('Web/Electron screenshot error: ${_webScreenshotService.getErrorMessage(e)}');
          return null;
        } catch (e) {
          d('Web/Electron screenshot error: $e');
          return null;
        }
      } else {
        // Native macOS path
        // If the screenshot is not hidden from screen sharing, hide it temporarily while taking the screenshot
        final prefs = await SharedPreferences.getInstance();
        final isHiddenFromScreenSharing =
            prefs.getBool(KEY_WINDOW_HIDE_FROM_SCREEN_SHARING);
        if (isHiddenFromScreenSharing != true) {
          await setHideFromScreenSharing(true);
        }

        final tempDir = await getTemporaryDirectory();
        imagePath = path.join(
          tempDir.path,
          'screenshot_${DateTime.now().millisecondsSinceEpoch}.png',
        );

        final capturedData = await ScreenCapturer.instance.capture(
          mode: CaptureMode.screen,
          imagePath: imagePath,
          copyToClipboard: false,
        );

        if (isHiddenFromScreenSharing != true) {
          await setHideFromScreenSharing(false);
        }
        rawBytesForNative = capturedData?.imageBytes;
      }

      if (rawBytesForNative == null) {
        d('Native screenshot returned null bytes.');
        return null;
      }

      final processedScreenshot = await processScreenshot(rawBytesForNative);
      return ProcessedScreenshot(
        bytes: processedScreenshot.bytes,
        width: processedScreenshot.width,
        height: processedScreenshot.height,
        imagePath: imagePath, // Only set for native
      );
    } catch (e) {
      d('Error capturing or processing screenshot: $e');
      return null;
    }
  }

  /// Processes screenshot bytes (used ONLY for native platforms now).
  /// Runs the image processing in a separate isolate via compute.
  Future<ProcessedScreenshot> processScreenshot(Uint8List imageBytes) async {
    // This compute call is fine for native, but for web it will run on the main thread, which will freeze the UI.
    return compute(_processScreenshotInIsolate, imageBytes);
  }
}

/// Exception thrown when screenshot initialization fails.
class ScreenshotInitializationException implements Exception {
  final String message;

  ScreenshotInitializationException(this.message);

  @override
  String toString() => 'ScreenshotInitializationException: $message';
}

class ProcessedScreenshot {
  final Uint8List bytes;
  final int width;
  final int height;
  final String? imagePath;

  ProcessedScreenshot({
    required this.bytes,
    required this.width,
    required this.height,
    this.imagePath,
  });

  /// Returns a URI suitable for displaying the image.
  /// For native platforms, this is the file path.
  /// For web/Electron, this is a base64 encoded data URI.
  String get displayUri {
    if (imagePath != null) {
      return imagePath!; // Native path
    } else {
      // Web/Electron: Create data URI
      final base64String = base64Encode(bytes);
      return 'data:image/png;base64,$base64String';
    }
  }
}

/// Top-level function for image processing in an isolate (used ONLY for native).
Future<ProcessedScreenshot> _processScreenshotInIsolate(
    Uint8List imageBytes) async {
  final original = img.decodeImage(imageBytes);
  if (original == null) {
    throw Exception('Failed to decode screenshot image');
  }

  double scaleFactor = 1.0;
  if (original.width > ScreenshotProcessor.maxResolution) {
    scaleFactor = ScreenshotProcessor.maxResolution / original.width;
  }
  if (original.height * scaleFactor > ScreenshotProcessor.maxResolution) {
    scaleFactor = ScreenshotProcessor.maxResolution / original.height;
  }

  final newWidth = (original.width * scaleFactor).round();
  final newHeight = (original.height * scaleFactor).round();

  Uint8List finalBytes;
  if (newWidth != original.width || newHeight != original.height) {
    final resized = img.copyResize(
      original,
      width: newWidth,
      height: newHeight,
    );
    finalBytes = Uint8List.fromList(img.encodePng(resized));
  } else {
    finalBytes = imageBytes;
  }

  return ProcessedScreenshot(
    bytes: finalBytes,
    width: newWidth,
    height: newHeight,
  );
}

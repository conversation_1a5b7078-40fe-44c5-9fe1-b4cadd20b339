import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chat_core/flutter_chat_core.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/api/ai/ai_api.dart';
import 'package:interview_hammer/api/management/user_profile_manager.dart';
import 'package:interview_hammer/api/transcription/transcription_manager.dart';
import 'package:interview_hammer/api/transcription/transcription_service/transcription_service.dart';
import 'package:interview_hammer/api/transcription/transcription_service/transcription_service_factory.dart';
import 'package:interview_hammer/api/utils.dart';
import 'package:interview_hammer/model/management/answer_settings.dart';
import 'package:interview_hammer/model/management/user_profile.dart';
import 'package:interview_hammer/model/management/user_profile_extensions.dart';
import 'package:interview_hammer/model/question.dart';
import 'package:interview_hammer/model/screenshot.dart';
import 'package:interview_hammer/screens/helper/helper_state_manager.dart';
import 'package:interview_hammer/screens/helper/remote_screenshot_processor.dart';
import 'package:interview_hammer/screens/helper/screenshot_processor.dart';
import 'package:interview_hammer/screens/helper/session_timer.dart';
import 'package:interview_hammer/screens/helper/stream_state_manager.dart';
import 'package:interview_hammer/screens/helper/widgets/chat_buttons.dart';
import 'package:interview_hammer/screens/helper/widgets/chat_view.dart';
import 'package:interview_hammer/screens/helper/widgets/custom_input_field.dart';
import 'package:interview_hammer/screens/helper/widgets/exit_confirmation_dialog.dart';
import 'package:interview_hammer/screens/helper/widgets/flyer_chat_text_stream_message/flyer_chat_text_stream_message.dart';
import 'package:interview_hammer/screens/helper/widgets/helper_app_bar.dart';
import 'package:interview_hammer/screens/helper/widgets/screenshot_feature_dialog.dart';
import 'package:interview_hammer/screens/helper/widgets/transcription_view.dart';
import 'package:interview_hammer/utils/completer_utils.dart';
import 'package:interview_hammer/utils/events/events.dart';
import 'package:interview_hammer/utils/platform_utils.dart';
import 'package:interview_hammer/utils/string_utils.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:string_similarity/string_similarity.dart';
import 'package:uuid/uuid.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class HelperScreen extends StatefulWidget {
  final UserProfile profile;
  final bool fromUndetectableMode;

  const HelperScreen({
    super.key,
    required this.profile,
    required this.fromUndetectableMode,
  });

  @override
  State<HelperScreen> createState() => _HelperScreenState();
}

class _HelperScreenState extends State<HelperScreen> {
  late final HelperStateManager _stateManager;
  final ScreenshotProcessor _screenshotProcessor = ScreenshotProcessor();
  var _helperState = HelperState.stopped;
  bool _webScreenshotFeatureEnabled = false;

  // transcription
  TranscriptionService? _transcriptionService;

  // logic
  var _transcription = '';
  var _previousQuestion = '';
  final _transcriptionManager = TranscriptionManager();

  // sessionStartTime
  DateTime? _sessionStartTime;
  Timer? _sessionTimer;

  //chat
  final _chatController = InMemoryChatController();
  final _streamStateManager = StreamStateManager();
  final List<Screenshot> _screenshots = [];

  // Prevent duplicate transcription processing
  final Set<String> _processingTranscriptions = {};

  @override
  void initState() {
    super.initState();
    WakelockPlus.enable();

    _stateManager = HelperStateManager(
      onStateChanged: (state) {
        if (mounted) setState(() => _helperState = state);
      },
    );

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _requestPermissionAndStartRecord();
      await _showEnableScreenshotsDialog();
    });
  }

  Future<void> _showEnableScreenshotsDialog() async {
    if (widget.fromUndetectableMode) {
      // Undetectable mode uses remote capture; no local permission needed.
      return;
    }

    if (kIsWebDesktop && !isElectron()) {
      final result = await ScreenshotFeatureDialog.show(context);

      if (result == true) {
        setState(() => _webScreenshotFeatureEnabled = true);
        await _initializeScreenshotProcessor();
      }
    } else {
      // desktop app: initialise screenshot pipeline, buttons already visible.
      await _initializeScreenshotProcessor();
    }
  }

  @override
  void dispose() {
    WakelockPlus.disable();

    _sessionTimer?.cancel();

    // need to call _transcriptionService?.dispose() instead of _stopRecord, to avoid state updates, which throws.
    _transcriptionService?.dispose();
    _transcriptionManager.dispose();

    _screenshotProcessor.dispose(); // Dispose screenshot processor
    _chatController.dispose();
    _streamStateManager.dispose();
    _processingTranscriptions.clear();
    super.dispose();
  }

  Future<void> _requestPermissionAndStartRecord() async {
    final status = await _checkPermissionStatus();
    if (status.isGranted) {
      _startRecord();
    } else {
      errorToast('You must grant microphone permission to use this feature.');
      d('Microphone permission denied');
    }
    if (mounted) setState(() {});
  }

  Future<PermissionStatus> _checkPermissionStatus() async {
    // todo: remove this when we have a proper permission handling for macos
    if (defaultTargetPlatform == TargetPlatform.macOS) {
      return PermissionStatus.granted;
    } else {
      return await Permission.microphone.request();
    }
  }

  // region transcription
  void _startRecord() async {
    await _stateManager.startListening(() async {
      await _initializeTranscriptionService();

      if (_sessionStartTime == null && mounted) {
        _sessionStartTime = DateTime.now();
        _sessionTimer ??= startSessionTimer(
          context,
          profile: widget.profile,
          sessionStartTime: _sessionStartTime!,
          stopRecord: _stopRecord,
        );
      }
    });
  }

  Future<void> _stopRecord() async {
    await _stateManager.stopListening(() async {
      await _transcriptionService?.stop();
    });
  }

  Future<void> _initializeTranscriptionService() async {
    final provider = widget.profile.getTranscriptionProvider();
    _transcriptionService = TranscriptionServiceFactory.create(provider);

    final languages =
        widget.profile.interviewLanguages?.map((l) => l.code).toList() ??
            [UserProfile.defaultLanguage.code];

    await _transcriptionService!.initialize(
      onInterim: (transcription) {
        _transcriptionManager.addInterimTranscription(transcription);
        _showTranscription(transcription);
      },
      onEndpoint: (transcription) {
        _transcriptionManager.addEndpointTranscription(transcription);
        _showTranscription(transcription);
      },
      onError: (err) => logError(err),
      languages: languages,
    );

    _transcriptionManager.listen((event) {
      if ((widget.profile.automaticQuestionAnswering ?? true) == true) {
        _endpointCallback(event.value);
      }
    });
  }

  void _showTranscription(String transcript) {
    setState(() {
      _transcription = transcript;
    });
    // transcriptionSteps.add(transcript);
    // log(transcript);
    // log('---');
    // log('');
    // d('Transcript: $transcript');
  }

  void _endpointCallback(String endpointTranscript) async {
    if (_stateManager.isGenerating) return;

    await _processTranscriptIfNotDuplicate(endpointTranscript, () async {
      final response = await extractQuestion(
        endpointTranscript,
        _previousQuestion,
        images: _screenshots,
      );

      final question = _getQuestionToAnswer(response, false);

      if (question != null) {
        await _answerNow(requiresPersonalInfo: question.requiresPersonalInfo);
      }
    });
  }

  Future<void> _processTranscriptIfNotDuplicate(
    String transcript,
    Future<void> Function() onProcess,
  ) async {
    final normalizedTranscript = transcript.trim().toLowerCase();

    // Check if we're already processing this or very similar transcription
    for (String processing in _processingTranscriptions) {
      if (normalizedTranscript.similarityTo(processing) > 0.95) {
        d('🚫 Blocked duplicate transcription processing: $transcript');
        return;
      }
    }

    _processingTranscriptions.add(normalizedTranscript);

    try {
      await onProcess();
    } finally {
      _processingTranscriptions.remove(normalizedTranscript);
    }
  }

  // Holds all normalized questions that have already been processed to avoid duplicates
  final Set<String> questions = {};

  Question? _getQuestionToAnswer(String response, bool manualActivation) {
    final json = extractLastJsonObjectFromString(response);
    final question_final = json['question_final'] as bool;
    final question = json['question'] as String;
    final requires_personal_info = json['requires_personal_info'] as bool;

    if (question_final && question.isNotEmpty) {
      if (_isNewQuestion(question) || manualActivation) {
        questions.add(question.trim().toLowerCase());
        return Question(question, question_final, requires_personal_info);
      } else {
        d('Old question: $question');
      }
    }
    return null;
  }

  bool _isNewQuestion(String question) {
    if (questions.contains(question.trim().toLowerCase())) {
      d('🚫 Dropped because: found in old questions: $question');
      return false;
    }

    // check for similarity, because of typos the ai get confused.
    for (var value in questions) {
      final similarity = question.similarityTo(value.trim().toLowerCase());
      final sensitivity = userProfileManager
          .userProfile?.answerSettings?.questionDetectionSensitivity;

      final threshold = {
            QuestionDetectionSensitivity.LOW: 0.85,
            QuestionDetectionSensitivity.MEDIUM: 0.90,
            QuestionDetectionSensitivity.HIGH: 0.95,
          }[sensitivity] ??
          0.95;

      if (similarity > threshold) {
        d('🚫 Dropped because: found similar question: $question');
        return false;
      }
    }

    return true;
  }

  // endregion transcription

  Future<void> _takeScreenshot() async {
    if (widget.fromUndetectableMode) {
      await _takeRemoteScreenshot();
    } else {
      await _takeLocalScreenshot();
    }
  }

  Future<void> _takeRemoteScreenshot() async {
    await _stateManager.startTakingScreenshot(() async {
      final screenshotUrl = await takeRemoteScreenshot();

      if (screenshotUrl != null) {
        final message = ImageMessage(
          id: const Uuid().v4(),
          authorId: currentUser.id,
          createdAt: DateTime.now().toUtc(),
          source: screenshotUrl,
        );

        await _handleNewScreenshot(RemoteScreenshot(screenshotUrl), message);
      }
    });
  }

  Future<void> _takeLocalScreenshot() async {
    final processedScreenshot =
        await _screenshotProcessor.takeAndProcessScreenshot();
    if (processedScreenshot == null) {
      errorToast('Failed to take screenshot, please try again.');
      return;
    }

    final message = ImageMessage(
      id: const Uuid().v4(),
      authorId: currentUser.id,
      createdAt: DateTime.now().toUtc(),
      source: processedScreenshot.displayUri,
      width: processedScreenshot.width.toDouble(),
      height: processedScreenshot.height.toDouble(),
    );

    await _handleNewScreenshot(
        LocalScreenshot(processedScreenshot.bytes), message);
  }

  Future<void> _handleNewScreenshot(
    Screenshot screenshot,
    ImageMessage message,
  ) async {
    _screenshots.add(screenshot);

    await _chatController.insertMessage(message);

    _stateManager.startExtracting(() async {
      final response = await extractQuestion(
        _transcription,
        _previousQuestion,
        images: _screenshots,
      );

      // Return early if extraction was cancelled.
      if (_stateManager.currentState != HelperState.extracting) return;

      final question = _getQuestionToAnswer(response, true);
      if (question != null) {
        await _answerNow(requiresPersonalInfo: question.requiresPersonalInfo);
      } else {
        toast(
            "This screenshot doesn't show a complete question.\nClick 'Answer now' to have the AI provide a response anyway.");
      }
    });
  }

  void _startNewQuestion() {
    final message = SystemMessage(
      id: const Uuid().v4(),
      authorId: helperUser.id,
      createdAt: DateTime.now().toUtc(),
      text: 'Started a new question, old messages will be ignored',
    );
    _chatController.insertMessage(message);
    _screenshots.clear();
    _transcriptionService?.clear();
    setState(() => _transcription = '');
  }

  Future<void> _initializeScreenshotProcessor() async {
    try {
      await _screenshotProcessor.init();
    } on ScreenshotInitializationException catch (e) {
      if (mounted) {
        d('Screenshot initialization error: ${e.message}');
        errorToast('Failed to initialize screen capture: ${e.message}');
      }
    } catch (e) {
      if (mounted) {
        d('Unexpected screenshot initialization error: $e');
        errorToast('An unexpected error occurred during screen capture setup.');
      }
    }
  }

  Future<void> _answerNow({
    required bool requiresPersonalInfo,
    bool manualActivation = false,
    String? customUserMessage,
  }) async {
    d('_ChatPageState._answerNow');

    final questionId = const Uuid().v4();
    final answerId = const Uuid().v4();

    final completer = Completer<void>();

    await _stateManager.startGenerating(() async {
      await answerQuestionStream(
        _transcription,
        requiresPersonalInfo: requiresPersonalInfo,
        customUserMessage: customUserMessage,
        images: _screenshots,
        listener: (response) {
          _updateChatWithQuestionAndAnswer(
              response, questionId, answerId, manualActivation);
        },
        onDone: () => completer.tryComplete(),
        onError: () {
          errorToast('Failed to answer the question, please try again.');
          completer.tryComplete();
        },
      );

      await completer.future;
    });
  }

  void _updateChatWithQuestionAndAnswer(Map<String, dynamic> response,
      String questionId, String answerId, bool manualActivation) {
    if (!mounted) return;
    final String? question = response['question'];
    final String? answer = response['answer'];

    // Retrieve existing question / answer messages (if any)
    Message? currentQuestion = _chatController.messages
        .firstWhereOrNull((message) => message.id == questionId);
    final Message? currentAnswer = _chatController.messages
        .firstWhereOrNull((message) => message.id == answerId);

    // Ensure a placeholder question bubble exists if the answer starts streaming first
    if (answer?.isNotEmpty == true && currentQuestion == null) {
      // when answer arrives first…
      _addQuestionToChat('Detecting question…', questionId);
      currentQuestion = _chatController.messages
          .firstWhereOrNull((message) => message.id == questionId);
    }

    // 1. Handle the question (it might arrive after the answer has already started streaming)
    if (question != null && question.isNotEmpty) {
      // If we already have a placeholder, simply update it
      if (currentQuestion != null) {
        _streamStateManager.updateState(
            questionId, StreamStateStreaming(question));
      } else if (_isNewQuestion(question) || manualActivation) {
        // No placeholder (question arrived before answer) – add it now normally
        _addQuestionToChat(question, questionId);
      } else {
        d('Old question by GPT4: $question');
      }

      // Update tracking sets only when we have a concrete question text
      if (question.isNotEmpty) {
        questions.add(question.trim().toLowerCase());

        // only set the previous question here because it's coming from better ai.
        // we set it here because we got it in full, since we start seeing answer.
        _previousQuestion = question;
      }
    }

    // 2. Handle the answer streaming – we should keep updating/adding it regardless of whether the question arrived yet
    final String? answerText = answer
        ?.replaceDollarWithFullWidth()
        .normalizeBulletAndParagraphSpacing();

    if (answerText?.isNotEmpty == true) {
      if (currentAnswer == null) {
        _addAnswerToChat(answerText!, answerId);
      } else {
        _streamStateManager.updateState(
            answerId, StreamStateStreaming(answerText!));
      }
    }
  }

  void _addAnswerToChat(String answer, String id) {
    final message = TextStreamMessage(
      id: id,
      streamId: id,
      authorId: helperUser.id,
      createdAt: DateTime.now().toUtc(),
    );
    _chatController.insertMessage(message);
    _streamStateManager.updateState(id, StreamStateStreaming(answer));
  }

  void _addQuestionToChat(String question, String id) {
    final message = TextStreamMessage(
      id: id,
      streamId: id,
      authorId: currentUser.id,
      createdAt: DateTime.now().toUtc(),
    );
    _chatController.insertMessage(message);
    _streamStateManager.updateState(id, StreamStateStreaming(question));
  }

  void _addUserCustomMessage(String customMessage) {
    if (customMessage.isEmpty) {
      _answerNow(requiresPersonalInfo: true, manualActivation: true);
      return;
    }

    final questionId = const Uuid().v4();
    _addQuestionToChat('Custom Input: $customMessage', questionId);

    setState(() {
      _transcription += '\nCustom Input: $customMessage';
    });
    _answerNow(requiresPersonalInfo: true, manualActivation: true);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, dynamic result) async {
          if (didPop) return;
          final result = await showExitConfirmationDialog(context);
          if (result == true && context.mounted) {
            Navigator.of(context).pop();
          }
        },
        child: Scaffold(
          body: Stack(
            children: [
              Column(
                children: [
                  ChatView(
                    chatController: _chatController,
                    streamStateManager: _streamStateManager,
                    onMessageSend: _addUserCustomMessage,
                  ),
                  _chatBottomWidget(),
                ],
              ),
              _appBar(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _appBar() {
    return HelperAppBar(
      helperState: _helperState,
      onBackPressed: () async {
        final result = await showExitConfirmationDialog(context);
        if (result == true && mounted) {
          Navigator.pop(context);
        }
      },
    );
  }

  Widget _chatBottomWidget() {
    return Column(
      children: [
        ChatButtons(
          stateManager: _stateManager,
          profile: widget.profile,
          answerNow: () =>
              _answerNow(requiresPersonalInfo: true, manualActivation: true),
          stopRecord: _stopRecord,
          startRecord: _startRecord,
          takeScreenshot: _takeScreenshot,
          startNewQuestion: _startNewQuestion,
          fromUndetectableMode: widget.fromUndetectableMode,
          webScreenshotFeatureEnabled: _webScreenshotFeatureEnabled,
        ),

        // transcription text
        if (widget.profile.showTranscript ?? true)
          TranscriptionView(transcription: _transcription),

        // Custom message input
        if (widget.profile.showCustomMessageInput ?? false)
          CustomInputField(onSend: _addUserCustomMessage),
      ],
    );
  }
}

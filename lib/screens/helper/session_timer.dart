import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/screens/plan/plan_details_screen.dart';

import '../../api/management/management_api.dart';
import '../../model/management/user_profile.dart';
import '../home/<USER>';

const _isDebug = false;

Timer? startSessionTimer(
  BuildContext context, {
  required UserProfile profile,
  required DateTime sessionStartTime,
  required Function stopRecord,
}) {
  sessionStartTime = DateTime.now();

  final String subscription = profile.planType ?? 'None';
  final isFreeSubscription = subscription.toLowerCase() == PLAN_TYPE_FREE ||
      subscription.toLowerCase() == 'none';
  if (!isFreeSubscription) return _startUnlimitedSessionTimer();

  final int availableMinutes = profile.availableMinutes ?? 0;
  final int usedMinutes = profile.usedMinutes ?? 0;
  final int sessionMaxMinutes = profile.sessionMaxMinutes ?? 0;

  final remainingMinutes = availableMinutes - usedMinutes;

  if (remainingMinutes <= 0) {
    _closeSession(context, stopRecord, profile, true);
    return null;
  }

  // make sure the remaining minutes isn't less than the session max minutes.
  int sessionRemainingMinutes = sessionMaxMinutes;
  if (remainingMinutes < sessionMaxMinutes) {
    sessionRemainingMinutes = remainingMinutes;
  }

  return Timer.periodic(
      _isDebug ? const Duration(seconds: 1) : const Duration(minutes: 1),
      (timer) {
    updateUsedMinuets();

    final duration = DateTime.now().difference(sessionStartTime);
    final durationMinutes = _isDebug ? duration.inSeconds : duration.inMinutes;

    if (durationMinutes >= sessionRemainingMinutes) {
      timer.cancel();
      var trailEnded = remainingMinutes < sessionMaxMinutes;
      _closeSession(context, stopRecord, profile, trailEnded);
    }
  });
}

// only used to update the used minutes.
Timer? _startUnlimitedSessionTimer() {
  return Timer.periodic(
      _isDebug ? const Duration(seconds: 1) : const Duration(minutes: 1),
      (timer) {
    updateUsedMinuets();
  });
}

// trailEnded means the user used all of the minutes not only the current session minutes.
void _closeSession(BuildContext context, Function stopRecord,
    UserProfile profile, bool trailEnded) {
  stopRecord();
  _showSessionEndedDialog(context, profile, trailEnded);
}

void _showSessionEndedDialog(
    BuildContext context, UserProfile profile, bool trailEnded) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      final colorScheme = Theme.of(context).colorScheme;
      final textTheme = Theme.of(context).textTheme;

      return AlertDialog(
        title: Text('Session Ended'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(trailEnded
                ? 'Your free minutes have been used up.\n\nYou have exceeded the available ${profile.availableMinutes} minutes.'
                : 'Your current session has ended.\n\nPlease note that free accounts are limited to ${profile.sessionMaxMinutes} minutes per session.'),
            if (trailEnded) SizedBox(height: 16),
            if (trailEnded)
              Text(
                'Upgrade to Premium to get unlimited minutes and sessions!',
                style: textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
          ],
        ),
        actions: <Widget>[
          TextButton(
            child: Text('Close'),
            onPressed: () {
              pushAndRemoveUntil(context, HomeScreen(), null);
            },
          ),
          if (trailEnded)
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
              ),
              child: Text('Upgrade to Premium'),
              onPressed: () {
                // Close the dialog
                Navigator.pop(context);
                // First navigate to home screen, then to plan details
                pushAndRemoveUntil(context, PlanDetailsScreen(), HomeScreen());
              },
            ),
        ],
      );
    },
  );
}

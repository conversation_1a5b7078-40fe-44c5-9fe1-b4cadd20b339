import 'package:interview_hammer/screens/helper/widgets/flyer_chat_text_stream_message/src/stream_state.dart';

class StreamStateManager {
  final Map<String, StreamState> _states = {};

  StreamState getState(String streamId) {
    return _states[streamId] ?? const StreamStateLoading();
  }

  void updateState(String streamId, StreamState state) {
    _states[streamId] = state;
  }

  void removeState(String streamId) {
    _states.remove(streamId);
  }

  void dispose() {
    _states.clear();
  }
}

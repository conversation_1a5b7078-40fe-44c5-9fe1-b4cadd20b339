import 'package:flutter/material.dart';

class HelperStateManager {
  RecordingState _recordingState = RecordingState.stopped;
  HelperState _currentState = HelperState.stopped;

  // Prevents state from being updated to stopped/listening while async operations are running
  bool _isOperationInProgress = false;
  CancellationToken? _extractionCancellationToken;

  final void Function(HelperState) onStateChanged;

  HelperStateManager({required this.onStateChanged});

  HelperState get currentState => _currentState;

  RecordingState get recordingState => _recordingState;

  bool get isListening => _recordingState == RecordingState.listening;

  bool get isGenerating => _currentState == HelperState.generating;

  Future<void> startListening(Future<void> Function() action) async {
    await _handleRecordingTransition(
      action: action,
      startState: RecordingState.starting,
      successState: RecordingState.listening,
      failureState: RecordingState.stopped,
      helperState: HelperState.listening,
    );
  }

  Future<void> stopListening(Future<void> Function() action) async {
    await _handleRecordingTransition(
      action: action,
      startState: RecordingState.stopping,
      successState: RecordingState.stopped,
      failureState: RecordingState.listening,
      helperState: HelperState.stopped,
    );
  }

  Future<void> startGenerating(Future<void> Function() action) async {
    _cancelExtraction();
    await runWithState(
      operationState: HelperState.generating,
      action: action,
    );
  }

  Future<void> startExtracting(Future<void> Function() action) async {
    _cancelExtraction();
    _extractionCancellationToken = CancellationToken();

    await runWithState(
      operationState: HelperState.extracting,
      action: action,
    );

    _extractionCancellationToken = null;
  }

  Future<void> startTakingScreenshot(Future<void> Function() action) async {
    await runWithState(
      operationState: HelperState.takingScreenshot,
      action: action,
    );
  }

  /// Temporarily sets the state to [operationState] while running [action].
  /// When [action] finishes, if no other operation has overridden the state,
  /// the UI state is updated based on the current recording state (listening or stopped).
  /// If the state was changed by another operation, no update is performed.
  Future<T> runWithState<T>({
    required HelperState operationState,
    required Future<T> Function() action,
  }) async {
    _isOperationInProgress = true;
    _setState(operationState);

    try {
      final result = await action();
      _isOperationInProgress = false;
      if (_currentState == operationState) {
        _syncStateWithRecordingState();
      }
      return result;
    } catch (e) {
      _isOperationInProgress = false;
      if (_currentState == operationState) {
        _syncStateWithRecordingState();
      }
      rethrow;
    }
  }

  Future<void> _handleRecordingTransition({
    required Future<void> Function() action,
    required RecordingState startState,
    required RecordingState successState,
    required RecordingState failureState,
    required HelperState helperState,
  }) async {
    try {
      _recordingState = startState;
      if (!_isOperationInProgress) _setState(helperState);
      onStateChanged(_currentState);

      await action();

      _recordingState = successState;
      if (!_isOperationInProgress) _setState(helperState);
      onStateChanged(_currentState);
    } catch (e) {
      _recordingState = failureState;
      if (!_isOperationInProgress) _setState(helperState);
      onStateChanged(_currentState);
      rethrow;
    }
  }

  void _cancelExtraction() {
    if (_extractionCancellationToken != null) {
      _extractionCancellationToken?.cancel();
      _extractionCancellationToken = null;

      // Only reset operation state if we're cancelling an active extraction.
      // If we're in a different state (like generating), leave that state's operation flag alone.
      if (_currentState == HelperState.extracting) {
        _isOperationInProgress = false;
        _syncStateWithRecordingState();
      }
    }
  }

  void _setState(HelperState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      onStateChanged(newState);
    }
  }

  void _syncStateWithRecordingState() {
    _setState(_recordingState == RecordingState.listening
        ? HelperState.listening
        : HelperState.stopped);
  }
}

enum RecordingState {
  stopped(label: 'Start', icon: Icons.mic),
  starting(label: 'Starting...', icon: Icons.hourglass_empty),
  listening(label: 'Stop', icon: Icons.stop_circle_outlined),
  stopping(label: 'Stopping...', icon: Icons.hourglass_empty);

  final String label;
  final IconData icon;

  const RecordingState({required this.label, required this.icon});

  bool get isTransitioning => this == starting || this == stopping;
}

enum HelperState {
  stopped('Stopped'),
  listening('Listening'),
  extracting('Extracting Question'),
  generating('Generating Answer'),
  takingScreenshot('Taking Screenshot');

  final String description;

  const HelperState(this.description);
}

class CancellationToken {
  bool _isCancelled = false;

  bool get isCancelled => _isCancelled;

  void cancel() => _isCancelled = true;
}

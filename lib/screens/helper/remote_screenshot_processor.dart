import 'dart:async';

import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/api/server/session_api.dart' as session_api;
import 'package:interview_hammer/model/server/session.dart';
import 'package:interview_hammer/providers/session_manager.dart';
import 'package:interview_hammer/utils/completer_utils.dart';

/// Takes a screenshot from a remote session and returns the screenshot URL.
Future<String?> takeRemoteScreenshot() async {
  final activeSession = SessionManager.instance.activeClientSession;

  if (activeSession == null) {
    errorToast('No active server session found, please start a new session.');
    return null;
  }

  final sessionId = activeSession.sessionId;
  try {
    // Send screenshot command to server
    await session_api.setCommand(
      sessionId,
      CommandType.TAKE_SCREENSHOT,
    );

    // Wait for screenshot completion
    final completer = Completer<String?>();
    late StreamSubscription<Session> subscription;

    subscription =
        session_api.listenToSessionCommands(sessionId).listen((session) {
      final isScreenshotCommand =
          session.currentCommand == CommandType.TAKE_SCREENSHOT;
      final isComplete = session.commandStatus == CommandStatus.COMPLETED;
      final hasFailed = session.commandStatus == CommandStatus.FAILED;

      if (isScreenshotCommand) {
        if (isComplete) {
          final latestScreenshot = session.screenshots.lastOrNull;
          if (latestScreenshot != null) {
            completer.tryComplete(latestScreenshot.screenshotUrl);
          } else {
            completer.tryComplete(null);
          }
          subscription.cancel();
        } else if (hasFailed) {
          errorToast('Failed to take screenshot from server');
          completer.tryComplete(null);
          subscription.cancel();
        }
      }
    });

    // Wait for completion with timeout
    return await completer.future.timeout(
      const Duration(seconds: 60),
      onTimeout: () {
        subscription.cancel();
        errorToast('Screenshot request timed out');
        return null;
      },
    );
  } catch (e) {
    d('Error taking remote screenshot: $e');
    errorToast('Error taking remote screenshot: $e');
    return null;
  }
}

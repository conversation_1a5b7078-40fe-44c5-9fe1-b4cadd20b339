import 'package:flutter/material.dart';

class CustomInputField extends StatefulWidget {
  final Function(String customMessage) onSend;

  const CustomInputField({
    super.key,
    required this.onSend,
  });

  @override
  State<CustomInputField> createState() => _CustomInputFieldState();
}

class _CustomInputFieldState extends State<CustomInputField> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleSend() {
    widget.onSend(_controller.text);
    _controller.clear();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    final textFieldBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(24),
      borderSide: isDark
          ? BorderSide(color: colorScheme.outline.withValues(alpha: 0.3))
          : BorderSide.none,
    );

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _controller,
              onSubmitted: (_) => _handleSend(),
              decoration: InputDecoration(
                filled: true,
                fillColor: isDark
                    ? colorScheme.surfaceContainerLowest
                    : colorScheme.surfaceContainer,
                hintText: 'Custom message or transcription fix',
                hintStyle: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                border: textFieldBorder,
                focusedBorder: textFieldBorder,
                enabledBorder: textFieldBorder,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                constraints: const BoxConstraints(maxHeight: 48),
              ),
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface,
              ),
              mouseCursor: SystemMouseCursors.basic,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorScheme.primary,
            ),
            child: IconButton(
              icon: Icon(Icons.send, color: colorScheme.onPrimary, size: 20),
              onPressed: _handleSend,
            ),
          ),
        ],
      ),
    );
  }
}

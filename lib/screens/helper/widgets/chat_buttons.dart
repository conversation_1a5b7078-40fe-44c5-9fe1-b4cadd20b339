import 'dart:async';

import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';

import '../../../model/management/user_profile.dart';
import '../../../utils/platform_utils.dart';
import '../helper_state_manager.dart';

class ChatButtons extends StatelessWidget {
  final HelperStateManager stateManager;
  final UserProfile profile;
  final Function answerNow;
  final Function stopRecord;
  final Function startRecord;
  final Function takeScreenshot;
  final Function startNewQuestion;
  final bool fromUndetectableMode;
  final bool webScreenshotFeatureEnabled;

  const ChatButtons({
    super.key,
    required this.stateManager,
    required this.profile,
    required this.answerNow,
    required this.stopRecord,
    required this.startRecord,
    required this.takeScreenshot,
    required this.startNewQuestion,
    required this.fromUndetectableMode,
    required this.webScreenshotFeatureEnabled,
  });

  @override
  Widget build(BuildContext context) {
    final isListening = stateManager.isListening;
    final recordingState = stateManager.recordingState;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ResponsiveButtonGrid(
        buttonConfigs: [
          ButtonConfig(
            onPressed: () {
              isListening ? stopRecord() : startRecord();
            },
            iconData: recordingState.icon,
            label: recordingState.label,
            disabled: recordingState.isTransitioning,
          ),
          ButtonConfig(
            onPressed: () => answerNow(),
            iconData: Icons.send,
            label: 'Answer now',
          ),
          if (_shouldShowScreenshotButtons()) ...[
            ButtonConfig(
              onPressed: () => takeScreenshot(),
              iconData: Icons.screenshot,
              label: 'Screenshot',
            ),
            ButtonConfig(
              onPressed: () => startNewQuestion(),
              iconData: Icons.restart_alt,
              label: 'New Question',
            ),
          ]
        ],
      ),
    );
  }

  bool _shouldShowScreenshotButtons() {
    // Logic:
    // • Undetectable mode – always show (remote screenshots).
    // • Native desktop   – platform supports screenshots out-of-the-box, show buttons unconditionally.
    // • Web desktop      – show only after the user accepted the permission dialog.
    if (fromUndetectableMode) return true;

    if (kIsNativeDesktop) return true;

    // Web desktop path
    return kIsWebDesktop && webScreenshotFeatureEnabled;
  }
}

/// A grid of buttons that automatically adjusts the number of buttons per row
/// based on available width while ensuring:
/// - All buttons in a row have equal width
/// - Button text fits without wrapping
/// - Maximum screen space utilization
class ResponsiveButtonGrid extends StatelessWidget {
  static const double _buttonSpacing = 8;
  static const double _iconWidth = 48;
  static const double _horizontalPadding = 16;

  final List<ButtonConfig> buttonConfigs;

  const ResponsiveButtonGrid({
    super.key,
    required this.buttonConfigs,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final buttonsData = _calculateButtonsLayout(context, constraints);

        return Wrap(
          spacing: _buttonSpacing,
          runSpacing: _buttonSpacing,
          alignment: WrapAlignment.center,
          children: _buildButtons(context, buttonsData),
        );
      },
    );
  }

  ButtonsLayoutData _calculateButtonsLayout(
    BuildContext context,
    BoxConstraints constraints,
  ) {
    final textStyle = Theme.of(context).textTheme.titleMedium!;

    // Calculate minimum width needed for each button's content
    final buttonWidths = buttonConfigs.map((config) {
      final textSpan = TextSpan(
        text: config.label,
        style: textStyle,
      );
      final textPainter = TextPainter(
        text: textSpan,
        maxLines: 1,
        textDirection: TextDirection.ltr,
      )..layout();
      // Add space for icon and horizontal padding
      return textPainter.width + _iconWidth + _horizontalPadding;
    }).toList();

    // Find the widest button to ensure all content fits
    final maxButtonWidth = buttonWidths
        .reduce((maxWidth, width) => maxWidth > width ? maxWidth : width);

    // Calculate how many buttons can fit in a row
    int buttonsPerRow =
        (constraints.maxWidth / (maxButtonWidth + _buttonSpacing)).floor();
    // Ensure at least 1 button per row and no more than available buttons
    buttonsPerRow = buttonsPerRow.clamp(1, buttonConfigs.length);

    // Distribute remaining space equally between buttons
    final buttonWidth =
        (constraints.maxWidth - (_buttonSpacing * (buttonsPerRow - 1))) /
            buttonsPerRow;

    return ButtonsLayoutData(
      buttonWidth: buttonWidth,
      spacing: _buttonSpacing,
      configs: buttonConfigs,
    );
  }

  List<Widget> _buildButtons(
      BuildContext context, ButtonsLayoutData layoutData) {
    return layoutData.configs.map((config) {
      return SizedBox(
        width: layoutData.buttonWidth,
        child: ThrottledButton(config: config),
      );
    }).toList();
  }
}

class ThrottledButton extends StatefulWidget {
  final ButtonConfig config;

  const ThrottledButton({
    super.key,
    required this.config,
  });

  @override
  State<ThrottledButton> createState() => _ThrottledButtonState();
}

class _ThrottledButtonState extends State<ThrottledButton> {
  final PublishSubject<VoidCallback> _actionSubject =
      PublishSubject<VoidCallback>();
  StreamSubscription? _subscription;

  @override
  void initState() {
    super.initState();

    if (widget.config.useDebounce) {
      _subscription = _actionSubject
          .throttleTime(widget.config.debounceDelay)
          .listen((action) => action());
    }
  }

  @override
  void dispose() {
    _actionSubject.close();
    _subscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final config = widget.config;

    VoidCallback? onPressed;
    if (!config.disabled) {
      onPressed = () {
        if (config.useDebounce) {
          _actionSubject.add(config.onPressed);
        } else {
          config.onPressed();
        }
      };
    }

    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(config.iconData, size: 20),
      label: Text(
        config.label,
        style: theme.textTheme.titleMedium,
      ),
      style: theme.elevatedButtonTheme.style?.copyWith(
        minimumSize: WidgetStateProperty.all(const Size(0, 48)),
      ),
    );
  }
}

/// Configuration for a button in the ResponsiveButtonGrid
@immutable
class ButtonConfig {
  final VoidCallback onPressed;
  final IconData iconData;
  final String label;
  final bool disabled;
  final bool useDebounce;
  final Duration debounceDelay;

  const ButtonConfig({
    required this.onPressed,
    required this.iconData,
    required this.label,
    this.disabled = false,
    this.useDebounce = true,
    this.debounceDelay = const Duration(seconds: 1),
  });
}

/// Layout data for ResponsiveButtonGrid containing calculated dimensions
@immutable
class ButtonsLayoutData {
  final double buttonWidth;
  final double spacing;
  final List<ButtonConfig> configs;

  const ButtonsLayoutData({
    required this.buttonWidth,
    required this.spacing,
    required this.configs,
  });
}

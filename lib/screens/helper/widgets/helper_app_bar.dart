import 'package:flutter/material.dart';
import 'package:interview_hammer/screens/helper/widgets/loading_widget.dart';

import '../helper_state_manager.dart';

class HelperAppBar extends StatelessWidget {
  final HelperState helperState;
  final VoidCallback onBackPressed;

  const HelperAppBar({
    super.key,
    required this.helperState,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            CircleAvatar(
              backgroundColor:
                  colorScheme.surfaceContainerHighest.withValues(alpha: 0.7),
              child: IconButton(
                icon: Icon(Icons.arrow_back, color: colorScheme.onSurface),
                onPressed: onBackPressed,
              ),
            ),
            Si<PERSON><PERSON><PERSON>(width: 16),
            LoadingWidget(helperState)
          ],
        ),
      ),
    );
  }
}

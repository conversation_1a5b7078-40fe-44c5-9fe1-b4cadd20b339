import 'package:cross_cache/cross_cache.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chat_core/flutter_chat_core.dart';
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:flyer_chat_image_message/flyer_chat_image_message.dart';
import 'package:flyer_chat_system_message/flyer_chat_system_message.dart';

import '../../display_settings_screen.dart';
import '../stream_state_manager.dart';
import 'flyer_chat_text_stream_message/flyer_chat_text_stream_message.dart';
import 'messages_empty_state.dart';

const currentUser = User(id: 'current');
const helperUser = User(id: 'helper');

class ChatView extends StatefulWidget {
  final InMemoryChatController chatController;
  final StreamStateManager streamStateManager;
  final Function(String) onMessageSend;

  const ChatView({
    super.key,
    required this.chatController,
    required this.streamStateManager,
    required this.onMessageSend,
  });

  @override
  State<ChatView> createState() => _ChatViewState();
}

class _ChatViewState extends State<ChatView> {
  final _crossCache = CrossCache();

  @override
  void dispose() {
    _crossCache.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: FutureLoadingBuilder<double>(
        future: getFontScale(),
        builder: (context, fontScale) {
          return StreamBuilder<ChatOperation>(
            stream: widget.chatController.operationsStream,
            builder: (context, snapshot) {
              final hasMessages = widget.chatController.messages.isNotEmpty;

              if (!hasMessages) {
                return const MessagesEmptyState();
              }

              return Chat(
                currentUserId: currentUser.id,
                chatController: widget.chatController,
                crossCache: _crossCache,
                theme: getChatTheme(context, fontScale),
                builders: Builders(
                  chatAnimatedListBuilder: (context, itemBuilder) {
                    return ChatAnimatedListReversed(
                      itemBuilder: itemBuilder,
                      shouldScrollToEndWhenSendingMessage: false,
                    );
                  },
                  textStreamMessageBuilder: (context, message, index,
                          {required isSentByMe, groupStatus}) =>
                      FlyerChatTextStreamMessage(
                    message: message,
                    index: index,
                    streamState:
                        widget.streamStateManager.getState(message.streamId),
                    fontScaleFactor: fontScale,
                    mode: TextStreamMessageMode.instantMarkdown,
                    showTime: false,
                    showStatus: false,
                  ),
                  imageMessageBuilder: (context, message, index,
                          {required isSentByMe, groupStatus}) =>
                      FlyerChatImageMessage(
                    message: message,
                    index: index,
                    showTime: false,
                    showStatus: false,
                  ),
                  systemMessageBuilder: (context, message, index,
                          {required isSentByMe, groupStatus}) =>
                      FlyerChatSystemMessage(message: message, index: index),
                  composerBuilder: (context) => SizedBox(),
                ),
                resolveUser: (id) => Future.value(
                  switch (id) {
                    'helper' => helperUser,
                    'current' => currentUser,
                    _ => null,
                  },
                ),
                onMessageSend: widget.onMessageSend,
              );
            },
          );
        },
      ),
    );
  }
}

ChatTheme getChatTheme(BuildContext context, double fontScale) {
  final chatTheme = ChatTheme.fromThemeData(Theme.of(context));

  final scaledTypography = chatTheme.typography.copyWith(
    bodyLarge: chatTheme.typography.bodyLarge.copyWith(
        fontSize: chatTheme.typography.bodyLarge.fontSize! * fontScale),
    bodyMedium: chatTheme.typography.bodyMedium.copyWith(
        fontSize: chatTheme.typography.bodyMedium.fontSize! * fontScale),
    bodySmall: chatTheme.typography.bodySmall.copyWith(
        fontSize: chatTheme.typography.bodySmall.fontSize! * fontScale),
    labelLarge: chatTheme.typography.labelLarge.copyWith(
        fontSize: chatTheme.typography.labelLarge.fontSize! * fontScale),
    labelMedium: chatTheme.typography.labelMedium.copyWith(
        fontSize: chatTheme.typography.labelMedium.fontSize! * fontScale),
    labelSmall: chatTheme.typography.labelSmall.copyWith(
        fontSize: chatTheme.typography.labelSmall.fontSize! * fontScale),
  );

  return chatTheme.copyWith(
    typography: scaledTypography,
  );
}

Future<double> getFontScale() async =>
    (await prefs()).getDouble(KEY_ANSWERS_FONT_SCALE) ?? DEFAULT_FONT_SCALE;

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart' show Bidi;

class TranscriptionView extends StatefulWidget {
  final String transcription;

  const TranscriptionView({
    super.key,
    required this.transcription,
  });

  @override
  State<TranscriptionView> createState() => _TranscriptionViewState();
}

class _TranscriptionViewState extends State<TranscriptionView> {
  bool _isExpanded = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(TranscriptionView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.transcription != oldWidget.transcription) {
      _queueScrollToBottom();
    }
  }

  String get _displayText {
    final trimmed = widget.transcription.trim();
    if (trimmed.isEmpty) return '';

    if (!_isExpanded) {
      final lines = trimmed.split('\n');
      return lines.lastWhere((line) => line.trim().isNotEmpty,
          orElse: () => '');
    }

    // Limit to last 10,000 characters to prevent UI issues
    return trimmed.length <= 10000
        ? trimmed
        : trimmed.substring(trimmed.length - 10000);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: _isExpanded ? 128 : 24,
              width: double.infinity,
              child: SingleChildScrollView(
                controller: _scrollController,
                child: _displayText.isEmpty
                    ? _buildEmptyView()
                    : _buildTranscriptionText(_displayText),
              ),
            ),
          ),
          PositionedDirectional(
            top: 0,
            end: 8,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() => _isExpanded = !_isExpanded);
                  if (_isExpanded) {
                    _queueScrollToBottom();
                  }
                },
                borderRadius: BorderRadius.circular(20),
                mouseCursor: SystemMouseCursors.basic,
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    _isExpanded ? Icons.expand_more : Icons.expand_less,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _queueScrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeOut,
      );
    }
  }

  Widget _buildTranscriptionText(String text) {
    final direction = _determineTextDirection(text);
    return Directionality(
      textDirection: direction,
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium,
        textAlign: TextAlign.start,
        maxLines: _isExpanded ? null : 1,
        overflow: _isExpanded ? null : TextOverflow.ellipsis,
      ),
    );
  }

  TextDirection _determineTextDirection(String text) {
    return Bidi.detectRtlDirectionality(text)
        ? TextDirection.rtl
        : TextDirection.ltr;
  }

  Widget _buildEmptyView() {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        "Transcription will appear here...",
        style: GoogleFonts.poppins(
          textStyle: TextStyle(
            color: Color(0xff9e9cab).withValues(alpha: 0.7),
            fontSize: 14,
            fontStyle: FontStyle.italic,
          ),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}

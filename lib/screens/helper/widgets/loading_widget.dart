import 'package:flutter/material.dart';

import '../helper_state_manager.dart';

class LoadingWidget extends StatelessWidget {
  final HelperState helperState;

  const LoadingWidget(this.helperState, {super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 8.0,
      ),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          const SizedBox(width: 8.0),
          if (helperState == HelperState.stopped)
            Icon(
              Icons.stop,
              color: colorScheme.onSurface,
              size: 16.0,
            )
          else
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
              ),
            ),
          const SizedBox(width: 12.0),
          Text(
            helperState.description,
            style: textTheme.labelLarge?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8.0),
        ],
      ),
    );
  }
}

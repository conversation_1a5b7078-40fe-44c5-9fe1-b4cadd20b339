import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/screens/home/<USER>';

import '../../api/management/management_api.dart';
import '../../model/management/language.dart';

class SettingUpLoadingScreen extends StatefulWidget {
  final List<Language> interviewLanguages;
  final Language answerLanguage;
  final String marketingSource;

  const SettingUpLoadingScreen(
    this.interviewLanguages,
    this.answerLanguage,
    this.marketingSource, {
    super.key,
  });

  @override
  State<SettingUpLoadingScreen> createState() => _SettingUpLoadingScreenState();
}

class _SettingUpLoadingScreenState extends State<SettingUpLoadingScreen> {
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      init();
    });
  }

  Future<void> init() async {
    await createProfile(
      widget.interviewLanguages,
      widget.answerLanguage,
      widget.marketingSource,
    );
    if (mounted) pushAndRemoveUntil(context, HomeScreen(), null);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6A67CE)),
              strokeWidth: 6,
            ),
            const SizedBox(height: 32),
            Text(
              'Setting up your profile...',
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 24,
                fontWeight: FontWeight.bold,
                letterSpacing: 1.2,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

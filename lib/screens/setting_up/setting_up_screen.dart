import 'package:flutter/material.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/main.dart';
import 'package:interview_hammer/model/management/language.dart';
import 'package:interview_hammer/model/management/marketing_source.dart';
import 'package:interview_hammer/model/management/user_profile.dart';
import 'package:interview_hammer/screens/setting_up/setting_up_loading_screen.dart';
import 'package:interview_hammer/widgets/custom_card.dart';

import '../../widgets/bottom_sheet_dropdowns/app_bottom_sheet_dropdown_multi_search.dart';
import '../../widgets/bottom_sheet_dropdowns/app_bottom_sheet_dropdown_search.dart';

class SettingUpScreen extends StatefulWidget {
  const SettingUpScreen({super.key});

  @override
  State<SettingUpScreen> createState() => _SettingUpScreenState();
}

class _SettingUpScreenState extends State<SettingUpScreen> {
  List<Language> _selectedInterviewLanguages = [UserProfile.defaultLanguage];
  Language _selectedAnswerLanguage = UserProfile.defaultLanguage;
  MarketingSource? _selectedSource;
  final TextEditingController _otherSourceController = TextEditingController();

  @override
  void dispose() {
    _otherSourceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeText(),
              SizedBox(height: 32),
              _buildSectionHeader('Language Preferences', Icons.language),
              SizedBox(height: 16),
              _buildLanguageSection(),
              SizedBox(height: 32),
              _buildSectionHeader('Quick Survey', Icons.poll),
              SizedBox(height: 16),
              _buildSurveySection(),
              SizedBox(height: 32),
              _buildFinishButton(),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'Complete Your Profile',
        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
      ),
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      elevation: 0,
    );
  }

  Widget _buildWelcomeText() {
    return Text(
      'Welcome to $appName! Let\'s set up your preferences to get you started.',
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w500,
            color: Theme.of(context).colorScheme.onSurface,
          ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary, size: 32),
        SizedBox(width: 16),
        Text(
          title,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
        ),
      ],
    );
  }

  Widget _buildLanguageSection() {
    return CustomCard(
      child: Column(
        children: [
          AppBottomSheetDropdownMultiSearch(
            label: "What language will the interview be conducted in?",
            hintText: 'Select languages',
            items: Language.values,
            initialItems: _selectedInterviewLanguages,
            onListChanged: (List<Language> languages) {
              setState(() {
                _selectedInterviewLanguages = languages;
              });
            },
          ),
          Divider(height: 0),
          AppBottomSheetDropdownSearch(
            label: "In what language do you want the generated answers?",
            hintText: 'Select language',
            items: Language.values,
            initialItem: UserProfile.defaultLanguage,
            onChanged: (Language? selectedLanguage) {
              if (selectedLanguage == null) return;
              setState(() {
                _selectedAnswerLanguage = selectedLanguage;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSurveySection() {
    final colorScheme = Theme.of(context).colorScheme;

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'How did you discover $appName?',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
            ),
          ),
          AppBottomSheetDropdownSearch<MarketingSource>(
            hintText: 'Select an option',
            items: MarketingSource.values,
            onChanged: (MarketingSource? selectedSource) {
              setState(() {
                _selectedSource = selectedSource;
              });
            },
          ),
          if (_selectedSource == MarketingSource.other)
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _otherSourceController,
                decoration: InputDecoration(
                  hintText: 'Please specify',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFinishButton() {
    var textTheme = Theme.of(context).textTheme;

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          if (_validateInputs()) {
            String marketingSource = _selectedSource == MarketingSource.other
                ? _otherSourceController.text.trim()
                : _selectedSource?.name ?? 'not specified';

            pushReplacement(
              context,
              SettingUpLoadingScreen(
                _selectedInterviewLanguages,
                _selectedAnswerLanguage,
                marketingSource,
              ),
            );
          }
        },
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 20),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        child: Text('Finish Setup'),
      ),
    );
  }

  bool _validateInputs() {
    // fixme: the dropdown don't work well on web, so making this optional for now
    return true;
    // ignore: dead_code
    if (_selectedSource == null) {
      _showErrorSnackBar('Please select how you discovered $appName.');
      return false;
    }
    if (_selectedSource == MarketingSource.other &&
        _otherSourceController.text.trim().isEmpty) {
      _showErrorSnackBar('Please specify how you discovered $appName.');
      return false;
    }
    return true;
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }
}

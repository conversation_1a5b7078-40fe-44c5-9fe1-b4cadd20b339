import 'package:flutter/material.dart';

import '../api/management/management_api.dart';
import '../model/management/personal_info.dart';
import '../widgets/app_text_field.dart';

class PersonalInfoScreen extends StatefulWidget {
  final PersonalInfo personalInfo;

  const PersonalInfoScreen({super.key, required this.personalInfo});

  @override
  State<PersonalInfoScreen> createState() => _PersonalInfoScreenState();
}

class _PersonalInfoScreenState extends State<PersonalInfoScreen> {
  late TextEditingController nameController;
  late TextEditingController currentRoleController;
  late TextEditingController companyController;
  late TextEditingController yearsOfExperienceController;
  late TextEditingController workHistoryController;
  late TextEditingController educationController;
  late TextEditingController additionalInfoController;

  List<String> skills = [];

  @override
  void initState() {
    super.initState();
    nameController = TextEditingController(text: widget.personalInfo.name);
    currentRoleController =
        TextEditingController(text: widget.personalInfo.currentRole);
    companyController =
        TextEditingController(text: widget.personalInfo.company);
    yearsOfExperienceController = TextEditingController(
        text: widget.personalInfo.yearsOfExperience?.toString());
    workHistoryController =
        TextEditingController(text: widget.personalInfo.workHistory);
    educationController =
        TextEditingController(text: widget.personalInfo.education);
    additionalInfoController =
        TextEditingController(text: widget.personalInfo.additionalInfo);

    skills = widget.personalInfo.skills ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'Edit Personal Info',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader('Basic Info', Icons.person),
              AppTextField(
                labelText: 'Full Name',
                hintText: 'Enter your full name',
                icon: Icons.person,
                controller: nameController,
                onChanged: (value) =>
                    updatePersonalInfo((p) => p.copyWith(name: value)),
              ),
              SizedBox(height: 32),
              _buildSectionHeader('Professional Info', Icons.work),
              AppTextField(
                labelText: 'Current Role',
                hintText: 'Enter your current role',
                icon: Icons.work,
                controller: currentRoleController,
                onChanged: (value) =>
                    updatePersonalInfo((p) => p.copyWith(currentRole: value)),
              ),
              SizedBox(height: 20),
              AppTextField(
                labelText: 'Company',
                hintText: 'Enter your company name',
                icon: Icons.business,
                controller: companyController,
                onChanged: (value) =>
                    updatePersonalInfo((p) => p.copyWith(company: value)),
              ),
              SizedBox(height: 20),
              AppTextField(
                labelText: 'Years of Experience',
                hintText: 'Enter your years of experience',
                icon: Icons.timeline,
                controller: yearsOfExperienceController,
                onChanged: (value) => updatePersonalInfo(
                    (p) => p.copyWith(yearsOfExperience: value)),
              ),
              SizedBox(height: 20),
              AppTextField(
                labelText: 'Work History',
                hintText: 'Enter your work history',
                icon: Icons.history,
                controller: workHistoryController,
                onChanged: (value) =>
                    updatePersonalInfo((p) => p.copyWith(workHistory: value)),
                maxLines: 5,
              ),
              SizedBox(height: 32),
              _buildSectionHeader('Skills', Icons.star),
              _buildSkillsSection(),
              SizedBox(height: 32),
              _buildSectionHeader('Education', Icons.school),
              AppTextField(
                labelText: 'Education',
                hintText: 'Enter your education details',
                icon: Icons.school,
                controller: educationController,
                onChanged: (value) =>
                    updatePersonalInfo((p) => p.copyWith(education: value)),
              ),
              SizedBox(height: 32),
              _buildSectionHeader('Additional Info', Icons.info),
              AppTextField(
                labelText: 'Additional Info',
                hintText: 'Enter any additional information',
                icon: Icons.info,
                controller: additionalInfoController,
                onChanged: (value) => updatePersonalInfo(
                    (p) => p.copyWith(additionalInfo: value)),
                maxLines: 5,
              ),
              SizedBox(height: 72),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Column(
      children: [
        Row(
          children: [
            Icon(icon, color: Theme.of(context).colorScheme.primary, size: 32),
            SizedBox(width: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
            ),
          ],
        ),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildSkillsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 8.0,
          runSpacing: 8.0,
          children: skills.map((skill) => _buildSkillChip(skill)).toList(),
        ),
        SizedBox(height: 16),
        _buildAddSkillButton(),
      ],
    );
  }

  Widget _buildSkillChip(String skill) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Chip(
      label: Text(skill),
      deleteIcon: const Icon(Icons.close, size: 18),
      onDeleted: () {
        setState(() => skills.remove(skill));
        updatePersonalInfo((p) => p.copyWith(skills: skills));
      },
      backgroundColor: colorScheme.surfaceContainerHighest,
      labelStyle: textTheme.bodyMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
    );
  }

  Widget _buildAddSkillButton() {
    return ElevatedButton.icon(
      icon: Icon(Icons.add),
      label: Text('Add Skill'),
      onPressed: () => _showAddSkillDialog(),
      style: ElevatedButton.styleFrom(
        textStyle: Theme.of(context).textTheme.titleMedium,
      ),
    );
  }

  void _showAddSkillDialog() {
    String newSkill = '';
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Add a Skill',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          content: TextField(
            onChanged: (value) => newSkill = value,
            decoration: InputDecoration(hintText: "Enter a skill"),
          ),
          actions: [
            TextButton(
              child: Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: Text(
                'Add',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              onPressed: () {
                if (newSkill.isNotEmpty) {
                  setState(() => skills.add(newSkill));
                  updatePersonalInfo((p) => p.copyWith(skills: skills));
                }
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    nameController.dispose();
    currentRoleController.dispose();
    companyController.dispose();
    yearsOfExperienceController.dispose();
    workHistoryController.dispose();
    educationController.dispose();
    additionalInfoController.dispose();
    super.dispose();
  }
}

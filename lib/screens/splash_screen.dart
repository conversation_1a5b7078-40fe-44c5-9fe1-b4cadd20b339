import 'package:firebase_auth/firebase_auth.dart' hide EmailAuthProvider;
import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/api/ai/openai.dart';
import 'package:interview_hammer/screens/home/<USER>';
import 'package:interview_hammer/screens/setting_up/setting_up_screen.dart';
import 'package:interview_hammer/utils/external_services.dart';

import '../api/management/remote_config.dart';
import '../utils/app_update_helper.dart';

const _autoLogin = kDebugMode && true;

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();

    ConnectionStatusBar.init(context);

    SchedulerBinding.instance.addPostFrameCallback((_) => init());
  }

  Future<void> init() async {
    initExternalServices();

    await initRemoteConfig();
    initOpenAi();

    if (mounted) await checkAppUpdate(context);

    setupLogin();
  }

  void setupLogin() {
    final isLoggedIn = FirebaseAuth.instance.currentUser != null;

    if (isLoggedIn) {
      pushReplacement(context, HomeScreen());
    } else {
      if (_autoLogin) {
        _devLogin();
        return;
      }
      pushReplacement(
        context,
        RegisterScreen(
          showPasswordVisibilityToggle: true,
          resizeToAvoidBottomInset: true,
          providers: [EmailAuthProvider()],
          actions: [
            AuthStateChangeAction<SignedIn>((context, state) {
              _openApp(context, false);
            }),
            AuthStateChangeAction<UserCreated>((context, state) {
              _openApp(context, true);
            }),
            AuthStateChangeAction<AuthFailed>((context, state) {
              d('AuthFailed: ${state.exception}');
              // errorToast(state.exception.toString());
            }),
          ],
        ),
      );
    }
  }

  Future<void> _openApp(BuildContext context, bool isNewUser) async {
    if (!context.mounted) return;

    if (isNewUser) {
      pushAndRemoveUntil(context, SettingUpScreen(), null);
    } else {
      pushAndRemoveUntil(context, HomeScreen(), null);
    }
  }

  void _devLogin() {
    FirebaseAuth.instance
        // .signInWithEmailAndPassword(
        // email: "<EMAIL>", password: "rc1234paid")
        // .signInWithEmailAndPassword(email: "<EMAIL>", password: "pass123")
        // .signInWithEmailAndPassword(email:  "<EMAIL>", password: "<EMAIL>")
        // .signInWithEmailAndPassword(email: "<EMAIL>", password: "pass123")
        .signInWithEmailAndPassword(email: "<EMAIL>", password: "pass123")
        .then((_) {
      if (mounted) _openApp(context, false);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [],
    );
  }
}

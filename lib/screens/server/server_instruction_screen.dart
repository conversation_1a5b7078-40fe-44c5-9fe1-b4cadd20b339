import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:interview_hammer/api/management/remote_config.dart';
import 'package:interview_hammer/model/management/user_profile.dart';
import 'package:interview_hammer/model/server/session.dart';
import 'package:interview_hammer/providers/session_manager.dart';
import 'package:interview_hammer/screens/server/server_session_screen.dart';
import 'package:interview_hammer/screens/server/widgets/hide_application_button.dart';
import 'package:interview_hammer/utils/platform_utils.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

class ServerInstructionScreen extends StatefulWidget {
  final UserProfile profile;

  const ServerInstructionScreen({super.key, required this.profile});

  @override
  State<ServerInstructionScreen> createState() =>
      _ServerInstructionScreenState();
}

class _ServerInstructionScreenState extends State<ServerInstructionScreen> {
  bool _hasClientConnected = false;
  bool _isCreatingSession = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    // Initialize the server session in Firestore
    _initializeSession();

    // Listen for client connections through Firestore
    if (isDesktopPlatform()) {
      _listenForClientConnection();
    }
  }

  @override
  void dispose() {
    SessionManager.instance.removeListener(_onSessionUpdate);
    super.dispose();
  }

  Future<void> _initializeSession() async {
    setState(() {
      _isCreatingSession = true;
    });

    try {
      await SessionManager.instance.createServerSession();

      if (mounted) {
        setState(() {
          _isCreatingSession = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isCreatingSession = false;
          _errorMessage = 'Failed to create session: $e';
        });
      }
    }
  }

  void _listenForClientConnection() {
    SessionManager.instance.addListener(_onSessionUpdate);
  }

  void _onSessionUpdate() {
    if (!mounted) return;

    final session = SessionManager.instance.activeServerSession;
    if (!_hasClientConnected && session?.status == SessionStatus.ACTIVE) {
      setState(() {
        _hasClientConnected = true;
      });

      pushReplacement(
        context,
        ServerSessionScreen(profile: widget.profile),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, dynamic result) async {
        if (!didPop) {
          _showCancelSessionDialog();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text('Undetectable Mode Server',
              style: GoogleFonts.poppins(fontWeight: FontWeight.bold)),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => _showCancelSessionDialog(),
          ),
        ),
        body: Center(
          child: SingleChildScrollView(
            padding:
                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .colorScheme
                        .surfaceContainerHighest
                        .withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.visibility_off,
                          size: 32,
                          color:
                              Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Server Ready',
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                            ),
                            Text(
                              'Your desktop is ready for two-device undetectable mode (100% undetectable against anti-cheating tools)',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    color:
                                        Theme.of(context).colorScheme.onSurface,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                // Session information
                _buildSessionInfo(),
                const SizedBox(height: 20),
                // Instructions card
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Text(
                          'Instructions',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 16),
                        _buildInstructionStep(
                          context,
                          icon: Icons.phone_android,
                          title: 'Connect from another device',
                          description:
                              'Open InterviewHammer on your mobile device or web browser.',
                        ),
                        const Divider(height: 24),
                        _buildInstructionStep(
                          context,
                          icon: Icons.list_alt,
                          title: 'Look for active sessions',
                          description:
                              'Open the app, tap Start button, and find this session in the Select Mode screen.',
                        ),
                        const Divider(height: 24),
                        _buildInstructionStep(
                          context,
                          icon: Icons.touch_app_outlined,
                          title: 'Connect to the session',
                          description:
                              'Tap on "Connect" to connect from your client device.',
                        ),
                        const Divider(),
                        const SizedBox(height: 8),
                        InkWellStacked(
                          onTap: () {
                            launchUrl(Uri.parse(remoteConfig
                                .getString(KEY_TUTORIAL_VIDEO_URL)));
                          },
                          borderRadius: BorderRadius.circular(10),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 16),
                            decoration: BoxDecoration(
                              color: Colors.red.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                  color: Colors.red.withValues(alpha: 0.3)),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(Icons.play_circle_outline,
                                    color: Colors.red, size: 16),
                                const SizedBox(width: 6),
                                Text(
                                  'Watch tutorial video',
                                  style: GoogleFonts.poppins(
                                    color: Colors.red,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 13,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Connection status
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .colorScheme
                        .surfaceContainerHighest
                        .withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .outline
                          .withValues(alpha: 0.2),
                    ),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (_isCreatingSession || !_hasClientConnected)
                        SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 3,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      if (_hasClientConnected)
                        const Icon(
                          Icons.check_circle,
                          color: Colors.green,
                          size: 24,
                        ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (_errorMessage.isNotEmpty)
                              Text(
                                _errorMessage,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      color: Colors.red,
                                    ),
                              ),
                            Text(
                              _isCreatingSession
                                  ? 'Creating session...'
                                  : (_hasClientConnected
                                      ? 'Client connected! Redirecting...'
                                      : 'Waiting for client connection...'),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    fontStyle: FontStyle.italic,
                                    color: _hasClientConnected
                                        ? Colors.green
                                        : Colors.grey,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                // Hide application button
                if (!_isCreatingSession && !_hasClientConnected)
                  const Center(child: HideApplicationButton()),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSessionInfo() {
    final sessionManager = Provider.of<SessionManager>(context);
    final session = sessionManager.activeServerSession;

    if (session == null) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Creating session...',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context)
                  .colorScheme
                  .primaryContainer
                  .withValues(alpha: 0.5),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.info_outline,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Session Information',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      'Device: ',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                    ),
                    Flexible(
                      child: Text(
                        session.deviceName,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      'Status: ',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                    ),
                    Text(
                      session.status == SessionStatus.WAITING
                          ? 'Waiting'
                          : 'Active',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: session.status == SessionStatus.WAITING
                                ? Colors.orange
                                : Colors.green,
                          ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionStep(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Theme.of(context)
                .colorScheme
                .primaryContainer
                .withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showCancelSessionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Session?'),
        content: const Text(
          'Are you sure you want to cancel this session? '
          'Any connected clients will be disconnected.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Continue Session'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () async {
              // Close dialog first to avoid state issues
              Navigator.pop(context);

              // Navigate back to previous screen immediately
              // instead of waiting for Firestore to update
              Navigator.pop(context);

              // End the session in Firestore after navigation
              // This prevents UI flicker while the session is being terminated
              await SessionManager.instance.endServerSession();
            },
            child: const Text('Cancel Session'),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:interview_hammer/model/management/user_profile.dart';
import 'package:interview_hammer/screens/helper/helper_screen.dart';
import 'package:interview_hammer/screens/server/server_instruction_screen.dart';
import 'package:interview_hammer/screens/server/sessions_widget.dart';
import 'package:interview_hammer/utils/platform_utils.dart';

class ModeSelectionScreen extends StatelessWidget {
  final UserProfile profile;

  const ModeSelectionScreen({super.key, required this.profile});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Select Mode',
            style: GoogleFonts.poppins(fontWeight: FontWeight.bold)),
        leading: I<PERSON><PERSON><PERSON>on(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Choose Your Interview Mode',
                style: GoogleFonts.poppins(
                  fontSize: 28,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Select the best option for your interview environment',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey.shade300
                      : Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 40),
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildNormalModeCard(context),
                  const SizedBox(height: 24),
                  isDesktopPlatform()
                      ? _buildUndetectableModeCard(context)
                      : _buildJoinSessionCard(context),
                ],
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNormalModeCard(BuildContext context) {
    return ModeOptionCard(
      icon: Icons.person_outline,
      illustration: Icons.computer_outlined,
      title: 'Normal Mode',
      subtitle: 'Standard Interview',
      description:
          'Ideal for standard interviews without monitoring software. Everything happens on this device.',
      color: Colors.blue,
      onTap: () => push(
        context,
        HelperScreen(profile: profile, fromUndetectableMode: false),
      ),
    );
  }

  Widget _buildUndetectableModeCard(BuildContext context) {
    return ModeOptionCard(
      icon: Icons.visibility_off_outlined,
      illustration: Icons.devices_outlined,
      title: 'Undetectable Mode',
      subtitle: 'Technical Interview with Monitoring',
      description:
          'Uses two devices for 100% undetectable AI assistance. While the desktop app is already invisible in normal mode, this provides complete protection against advanced monitoring systems.',
      color: Colors.deepPurple.shade400,
      onTap: () {
        push(
          context,
          ServerInstructionScreen(profile: profile),
        );
      },
    );
  }

  Widget _buildJoinSessionCard(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final cardBackground = isDark ? Colors.grey.shade900 : Colors.white;
    final color = Colors.deepPurple.shade400;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: cardBackground,
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.12),
            blurRadius: 12,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      clipBehavior: Clip.antiAlias,
      child: Stack(
        children: [
          // Background illustration
          Positioned(
            right: -20,
            bottom: -20,
            child: Icon(
              Icons.devices_other,
              size: 160,
              color: color.withValues(alpha: 0.07),
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Icon and title row
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      margin: const EdgeInsets.only(top: 2),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.visibility_off_outlined,
                        color: color,
                        size: 26,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Undetectable Mode',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Join a Desktop Session',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: color,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Connect to an active session running on your desktop computer',
                  style: GoogleFonts.poppins(
                    fontSize: 15,
                    height: 1.5,
                    color: isDark ? Colors.grey.shade300 : Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 20),
                _buildSessionsSection(context, color),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionsSection(BuildContext context, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        const Divider(),
        const SizedBox(height: 16),
        Row(
          children: [
            Icon(
              Icons.devices_other,
              color: color,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Available Sessions',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        const SessionsWidget(),
      ],
    );
  }
}

class ModeOptionCard extends StatefulWidget {
  final IconData icon;
  final IconData illustration;
  final String title;
  final String subtitle;
  final String description;
  final Color color;
  final VoidCallback onTap;

  const ModeOptionCard({
    super.key,
    required this.icon,
    required this.illustration,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.color,
    required this.onTap,
  });

  @override
  State<ModeOptionCard> createState() => _ModeOptionCardState();
}

class _ModeOptionCardState extends State<ModeOptionCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isHovering = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.03).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onHover(bool isHovering) {
    if (isHovering) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
    setState(() {
      _isHovering = isHovering;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final cardBackground = isDark ? Colors.grey.shade900 : Colors.white;

    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: cardBackground,
              boxShadow: [
                BoxShadow(
                  color: widget.color.withValues(alpha: 0.12),
                  blurRadius: 12,
                  offset: const Offset(0, 8),
                  spreadRadius: _isHovering ? 2 : 0,
                ),
              ],
              border: Border.all(
                color: _isHovering
                    ? widget.color
                    : widget.color.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            clipBehavior: Clip.antiAlias,
            child: Stack(
              children: [
                // Background illustration
                Positioned(
                  right: -20,
                  bottom: -20,
                  child: Icon(
                    widget.illustration,
                    size: 160,
                    color: widget.color.withValues(alpha: 0.07),
                  ),
                ),
                // Content
                Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Icon and title row
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(10),
                            margin: const EdgeInsets.only(top: 2),
                            decoration: BoxDecoration(
                              color: widget.color.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              widget.icon,
                              color: widget.color,
                              size: 26,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.title,
                                  style: GoogleFonts.poppins(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  widget.subtitle,
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    color: widget.color,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // Description
                      Text(
                        widget.description,
                        style: GoogleFonts.poppins(
                          fontSize: 15,
                          height: 1.5,
                          color: isDark
                              ? Colors.grey.shade300
                              : Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Action button
                      Align(
                        alignment: Alignment.centerRight,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 10),
                          decoration: BoxDecoration(
                            color: _isHovering
                                ? widget.color
                                : widget.color.withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Start',
                                style: GoogleFonts.poppins(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Icon(
                                Icons.arrow_forward_rounded,
                                color: Colors.white,
                                size: 18,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

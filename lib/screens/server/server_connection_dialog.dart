import 'package:flutter/material.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:interview_hammer/api/management/user_profile_manager.dart';
import 'package:interview_hammer/providers/session_manager.dart';
import 'package:interview_hammer/screens/helper/helper_screen.dart';

class ServerConnectionDialog extends StatefulWidget {
  final String deviceName;
  final String sessionDuration;
  final String sessionId;

  const ServerConnectionDialog({
    super.key,
    required this.deviceName,
    required this.sessionDuration,
    required this.sessionId,
  });

  @override
  State<ServerConnectionDialog> createState() => _ServerConnectionDialogState();
}

class _ServerConnectionDialogState extends State<ServerConnectionDialog> {
  Future<void> _connectToSession() async {
    final sessionManager = SessionManager.instance;

    toast('Connecting to session...');

    try {
      await sessionManager.joinSession(widget.sessionId);

      // Display a message if this was a waiting session that's now active
      if (sessionManager.activeClientSession != null) {
        successToast('Connected! Session is now active.');
      }

      if (mounted) {
        Navigator.pop(context);
        push(
          context,
          HelperScreen(
            profile: userProfileManager.userProfile!,
            fromUndetectableMode: true,
          ),
        );
      }
    } catch (e) {
      d('Failed to connect: $e');
      errorToast('Failed to connect: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Handle for bottom sheet
          Center(
            child: Container(
              width: 40,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2.5),
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Header
          Row(
            children: [
              Icon(
                Icons.link,
                size: 28,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Connect to Session',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Session details
          _buildSessionDetail(
            icon: Icons.computer,
            label: 'Device',
            value: widget.deviceName,
          ),
          const SizedBox(height: 12),
          _buildSessionDetail(
            icon: Icons.timer,
            label: 'Duration',
            value: widget.sessionDuration,
          ),
          const SizedBox(height: 32),

          // Connect button
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: _connectToSession,
            child: Text(
              'Connect',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(height: 12),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionDetail({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context)
                .colorScheme
                .primaryContainer
                .withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

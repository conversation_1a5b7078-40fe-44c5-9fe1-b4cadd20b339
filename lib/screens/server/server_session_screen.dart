import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:interview_hammer/model/management/user_profile.dart';
import 'package:interview_hammer/model/server/session.dart';
import 'package:interview_hammer/providers/session_manager.dart';
import 'package:interview_hammer/screens/server/widgets/hide_application_button.dart';
import 'package:provider/provider.dart';

class ServerSessionScreen extends StatefulWidget {
  final UserProfile profile;

  const ServerSessionScreen({super.key, required this.profile});

  @override
  State<ServerSessionScreen> createState() => _ServerSessionScreenState();
}

class _ServerSessionScreenState extends State<ServerSessionScreen> {
  StreamSubscription<CommandType>? _pendingCommandSubscription;

  @override
  void initState() {
    super.initState();

    // Listen for pending commands that need UI handling
    _setupCommandListener();
  }

  @override
  void dispose() {
    _pendingCommandSubscription?.cancel();
    super.dispose();
  }

  void _setupCommandListener() {
    _pendingCommandSubscription =
        SessionManager.instance.pendingCommands.listen((commandType) {
      // Handle different command types that need UI interaction
      if (commandType == CommandType.HIDE_APPLICATION) {
        _handleHideApplicationCommand();
      }
    });
  }

  Future<void> _handleHideApplicationCommand() async {
    if (!mounted) return;

    try {
      // Show the hide dialog
      bool cancelled =
          await HideApplicationButton.showHideDialog(context, autoHide: true);

      if (!cancelled) {
        await Future.delayed(const Duration(seconds: 1));
        await SessionManager.instance.hideApplication();
      }
    } catch (e) {
      errorToast('Failed to handle hide command: $e');
    }
  }

  Future<void> _endSession() async {
    Navigator.pop(context); // Close dialog

    await SessionManager.instance.endServerSession();

    // Check if still mounted before navigating
    if (mounted) {
      Navigator.pop(context); // Close session screen
    }
  }

  @override
  Widget build(BuildContext context) {
    final sessionManager = Provider.of<SessionManager>(context);
    final session = sessionManager.activeServerSession;

    if (session == null) {
      // If no session is available, show a message and a button to go back
      return Scaffold(
        appBar: AppBar(
          title: Text('No Active Session',
              style: GoogleFonts.poppins(fontWeight: FontWeight.bold)),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('No active session found.'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Session Active',
            style: GoogleFonts.poppins(fontWeight: FontWeight.bold)),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => _showEndSessionDialog(),
        ),
      ),
      body: Center(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Status indicator
                _buildStatusIndicator(session),
                const SizedBox(height: 40),

                // Session information card
                _buildSessionInfoCard(session),
                const SizedBox(height: 40),

                const HideApplicationButton(),
                const SizedBox(height: 20),

                // End session button
                TextButton(
                  onPressed: () => _showEndSessionDialog(),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 24),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'End Session',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(Session session) {
    Color statusColor;
    IconData statusIcon;

    switch (session.status) {
      case SessionStatus.WAITING:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        break;
      case SessionStatus.ACTIVE:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case SessionStatus.ENDED:
        statusColor = Colors.grey;
        statusIcon = Icons.cancel;
        break;
    }

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            statusIcon,
            size: 80,
            color: statusColor,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Session Status: ${session.status.name}',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: statusColor,
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }

  Widget _buildSessionInfoCard(Session session) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Session Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
            const Divider(height: 32),
            _buildInfoRow(
              icon: Icons.phone_android,
              label: 'Connected Device',
              value: session.deviceName,
            ),
            const SizedBox(height: 16),
            ValueListenableBuilder<String>(
              valueListenable: SessionManager.instance.sessionDurationNotifier,
              builder: (context, value, child) {
                return _buildInfoRow(
                  icon: Icons.timer,
                  label: 'Session Duration',
                  value: value,
                );
              },
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              icon: Icons.monitor,
              label: 'Current Status',
              value: session.status.name,
              valueColor: session.status == SessionStatus.ACTIVE
                  ? Colors.green
                  : (session.status == SessionStatus.WAITING
                      ? Colors.orange
                      : Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context)
                .colorScheme
                .primaryContainer
                .withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: valueColor,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showEndSessionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('End Session?'),
        content: const Text(
          'Are you sure you want to end this session? '
          'This will disconnect the client device and stop the server.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: _endSession,
            child: const Text('End Session'),
          ),
        ],
      ),
    );
  }
}

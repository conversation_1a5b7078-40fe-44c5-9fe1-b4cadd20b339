import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:interview_hammer/services/system_tray/system_tray_service.dart';

import '../../../utils/events/events.dart';
import '../../../utils/platform_utils.dart';

/// A button that allows hiding the application to the system tray
///
/// This component follows the desktop integration patterns described in the project
/// architecture, using platform detection utilities and SystemTrayService abstractions.
class HideApplicationButton extends StatelessWidget {
  const HideApplicationButton({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
        minimumSize: const Size(300, 60),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      onPressed: () => _showHideDialog(context),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.remove_from_queue, size: 28),
          const SizedBox(width: 12),
          Text(
            'Hide Application',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showHideDialog(BuildContext context) async {
    try {
      final cancelled = await showHideDialog(context);

      if (!cancelled) {
        await Future.delayed(Duration(seconds: 1));
        await SystemTrayService.instance.hideToTray();
      }
    } catch (e) {
      errorToast('Failed to hide application: $e');
      logError(e);
    }
  }

  /// Shows a dialog explaining how to restore the application from system tray
  ///
  /// @param context The BuildContext
  /// @param autoHide Whether this is an automatic hide (true) or manual (false)
  /// @return Future[bool] that resolves to true if user cancelled the operation
  static Future<bool> showHideDialog(
    BuildContext context, {
    bool autoHide = false,
  }) async {
    // Check if context is still mounted for autoHide case
    if (autoHide && !context.mounted) return false;

    final cancelled = await showDialog<bool>(
      context: context,
      barrierDismissible: !autoHide,
      builder: (context) =>
          autoHide ? _AutoHideDialog() : _buildRegularHideDialog(context),
    );
    return cancelled ?? false;
  }

  // Regular hide dialog for manual hiding
  static Widget _buildRegularHideDialog(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return AlertDialog(
      title: const Text('Hide Application'),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
      titleTextStyle: GoogleFonts.poppins(
        fontWeight: FontWeight.w600,
        fontSize: 18,
        color: colorScheme.onSurface,
      ),
      content: _buildDialogContent(context),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, true),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context, false),
          child: const Text('Hide'),
        ),
      ],
      actionsPadding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
      buttonPadding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }

  /// Builds the common dialog content
  static Widget _buildDialogContent(BuildContext context) {
    final isTrayHidden = SystemTrayService.instance.isTrayHidden;

    final message = isTrayHidden
        ? 'The application will be hidden. Since the system tray icon is hidden, to show it again, ${HideApplicationButton._getRestoreInstructionsForHiddenTray()}'
        : 'The application will be hidden. To show it again, ${HideApplicationButton._getRestoreInstructions()}';

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          message,
          style: GoogleFonts.poppins(fontSize: 14, height: 1.5),
        ),
        const SizedBox(height: 24),
        if (!isTrayHidden) _buildTrayIconInstructions(context),
      ],
    );
  }

  /// Builds the tray icon instructions row
  static Widget _buildTrayIconInstructions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          'assets/icons/tray_icon.png',
          width: 24,
          height: 24,
        ),
        const SizedBox(width: 8),
        Text(
          '← Look for this icon',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Gets platform-specific instructions for restoring the application
  ///
  /// Uses kIsNativeMacOS from platform_utils.dart as recommended in the docs
  static String _getRestoreInstructions() {
    return kIsNativeMacOS
        ? 'click on the speaker icon in your menu bar.'
        : 'click on the speaker icon in your system tray (bottom-right corner).';
  }

  /// Gets platform-specific instructions for restoring when tray is hidden
  static String _getRestoreInstructionsForHiddenTray() {
    return kIsNativeMacOS
        ? 'search for it in Spotlight (Cmd+Space) or Launchpad.'
        : 'search for it in the Start Menu or use the desktop shortcut.';
  }
}

class _AutoHideDialog extends StatefulWidget {
  @override
  State<_AutoHideDialog> createState() => _AutoHideDialogState();
}

class _AutoHideDialogState extends State<_AutoHideDialog>
    with SingleTickerProviderStateMixin {
  static const int _autoHideDurationSeconds = 20;
  late Timer _timer;
  late AnimationController _animationController;
  int _secondsRemaining = _autoHideDurationSeconds;
  bool _cancelled = false;

  @override
  void initState() {
    super.initState();
    _startTimer();
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(seconds: _autoHideDurationSeconds),
    )..forward();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_secondsRemaining > 0) {
          _secondsRemaining--;
        } else {
          _timer.cancel();
          if (!_cancelled) {
            _dismissDialog(true);
          }
        }
      });
    });
  }

  Future<void> _cancelAutoHide() async {
    setState(() {
      _cancelled = true;
    });
    _timer.cancel();
    _animationController.stop();
    _dismissDialog(true);
  }

  void _dismissDialog(bool cancelled) => Navigator.pop(context, cancelled);

  @override
  void dispose() {
    _timer.cancel();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isTrayHidden = SystemTrayService.instance.isTrayHidden;

    return AlertDialog(
      title: const Text('Application Will Be Hidden'),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
      titleTextStyle: GoogleFonts.poppins(
        fontWeight: FontWeight.w600,
        fontSize: 18,
        color: colorScheme.onSurface,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'The application will automatically be hidden in $_secondsRemaining seconds. '
            'To show it again, ${isTrayHidden ? HideApplicationButton._getRestoreInstructionsForHiddenTray() : HideApplicationButton._getRestoreInstructions()}',
            style: GoogleFonts.poppins(
              fontSize: 14,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 24),
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Hiding in $_secondsRemaining seconds...',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: 1.0 - _animationController.value,
                    backgroundColor: colorScheme.surfaceContainerHighest,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(colorScheme.primary),
                    borderRadius: BorderRadius.circular(4),
                    minHeight: 6,
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 24),
          if (!isTrayHidden)
            HideApplicationButton._buildTrayIconInstructions(context),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _cancelAutoHide,
          child: Text(
            'Cancel',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () => _dismissDialog(false),
          style: ElevatedButton.styleFrom(
            backgroundColor: colorScheme.primary,
            foregroundColor: colorScheme.onPrimary,
          ),
          child: Text(
            'Got it',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
      actionsPadding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
      buttonPadding: const EdgeInsets.symmetric(horizontal: 8),
    );
  }
}

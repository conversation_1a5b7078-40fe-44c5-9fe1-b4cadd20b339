import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chat_core/flutter_chat_core.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../api/management/remote_config.dart';
import '../utils/platform_utils.dart';
import '../utils/window_settings_utils.dart';
import '../widgets/app_switch_tile.dart';
import '../widgets/custom_card.dart';
import '../widgets/tray_hide_info_dialog.dart';

const KEY_ANSWERS_FONT_SCALE = "answers_font_scale";
const KEY_WINDOW_OPACITY = "window_opacity";
const KEY_WINDOW_ALWAYS_ON_TOP = "window_always_on_top";
const KEY_WINDOW_HIDE_APP_ICON = "window_hide_app_icon";
const KEY_WINDOW_VISIBLE_ALL_WORKSPACES = "window_visible_all_workspaces";
const KEY_WINDOW_HIDE_FROM_SCREEN_SHARING = "window_hide_from_screen_sharing";
const KEY_APP_NAME = "app_name";
const KEY_HIDE_SYSTEM_TRAY = "hide_system_tray";

const DEFAULT_FONT_SCALE = 1.0;
const DEFAULT_WINDOW_OPACITY = 1.0;
const DEFAULT_ALWAYS_ON_TOP = true;
const DEFAULT_HIDE_APP_ICON = false;
const DEFAULT_VISIBLE_ALL_WORKSPACES = true;
const DEFAULT_HIDE_FROM_SCREEN_SHARING = true;
const DEFAULT_APP_NAME = "Interview Hammer";
const DEFAULT_HIDE_SYSTEM_TRAY = false;

class DisplaySettingsScreen extends StatefulWidget {
  const DisplaySettingsScreen({super.key});

  @override
  State<DisplaySettingsScreen> createState() => _DisplaySettingsScreenState();
}

class _DisplaySettingsScreenState extends State<DisplaySettingsScreen> {
  double _answersFontScale = DEFAULT_FONT_SCALE;
  double _windowOpacity = DEFAULT_WINDOW_OPACITY;
  bool _alwaysOnTop = DEFAULT_ALWAYS_ON_TOP;
  bool _hideAppIcon = DEFAULT_HIDE_APP_ICON;
  bool _visibleOnAllWorkspaces = DEFAULT_VISIBLE_ALL_WORKSPACES;
  bool _hideFromScreenSharing = DEFAULT_HIDE_FROM_SCREEN_SHARING;
  bool _hideSystemTray = DEFAULT_HIDE_SYSTEM_TRAY;

  final _appNameController = TextEditingController();

  @override
  void initState() {
    super.initState();

    _appNameController.text = DEFAULT_APP_NAME;
    _loadSettings();
  }

  @override
  void dispose() {
    _appNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Display Settings',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: EdgeInsets.only(top: 20, left: 20, right: 20, bottom: 96),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSection(
                  'Text Size',
                  Icons.format_size,
                  _buildFontSettings(),
                ),
                SizedBox(height: 32),
                _buildSection(
                  'Window Settings',
                  Icons.window,
                  _buildWindowSettings(),
                ),
              ],
            ),
          ),
          _buildRestoreDefaultsButton(context),
        ],
      ),
    );
  }

  Widget _buildRestoreDefaultsButton(BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          border: Border(
            top: BorderSide(
              color: Theme.of(context).dividerColor.withValues(alpha: 0.1),
            ),
          ),
        ),
        child: Center(
          child: FilledButton.tonal(
            onPressed: _restoreDefaults,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.restore, size: 20),
                SizedBox(width: 8),
                Text('Restore Defaults'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(title, icon),
        SizedBox(height: 16),
        content,
      ],
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary, size: 32),
        SizedBox(width: 16),
        Text(
          title,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
        ),
      ],
    );
  }

  Widget _buildSliderSection({
    required String title,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required String Function(double) labelFormatter,
    required Function(double) onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 8),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            label: labelFormatter(value),
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildFontSettings() {
    final chatTheme = ChatTheme.fromThemeData(Theme.of(context));

    return CustomCard(
      child: Column(
        children: [
          _buildSliderSection(
            title: 'Font Size Scale for Answers',
            value: _answersFontScale,
            min: 0.8,
            max: 1.4,
            divisions: 12,
            labelFormatter: (value) => '${(value * 100).round()}%',
            onChanged: _updateFontScale,
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Preview:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'This is how the answer text will appear',
                    style: chatTheme.typography.bodyMedium.copyWith(
                      fontSize: chatTheme.typography.bodyMedium.fontSize! *
                          _answersFontScale,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWindowSettings() {
    if (!isDesktopPlatform()) {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Window settings are only available in the desktop version.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.6),
                      ),
                ),
              ),
              TextButton(
                onPressed: () => launchUrl(Uri.parse(
                    remoteConfig.getString(KEY_DESKTOP_DOWNLOAD_LINK))),
                child: Text('Download Desktop Version'),
              ),
            ],
          ),
          SizedBox(height: 16),
          _buildWindowSettingsContent(enabled: false),
        ],
      );
    } else {
      // Enable content if it's native desktop OR Electron
      return _buildWindowSettingsContent(enabled: true);
    }
  }

  Widget _buildWindowSettingsContent({required bool enabled}) {
    return Opacity(
      opacity: enabled ? 1.0 : 0.5,
      child: IgnorePointer(
        ignoring: !enabled,
        child: CustomCard(
          child: Column(
            children: [
              // Only show app settings for non-store macOS native builds
              if (!kIsStoreBuild &&
                  defaultTargetPlatform == TargetPlatform.macOS) ...[
                _buildAppSettings(),
                Divider(height: 0),
              ],
              _buildSliderSection(
                title: 'Window Transparency',
                value: _windowOpacity,
                min: 0.3,
                max: 1.0,
                divisions: 70,
                labelFormatter: (value) => '${(value * 100).round()}%',
                onChanged: _updateWindowOpacity,
              ),
              Divider(height: 0),
              AppSwitchListTile(
                title: 'Keep on Top of Other Windows',
                subtitle: 'Window will stay visible above other applications',
                value: _alwaysOnTop,
                onChanged: _updateAlwaysOnTop,
              ),
              Divider(height: 0),
              AppSwitchListTile(
                title: 'Hide App Icon',
                subtitle: 'Hide app icon from system taskbar/dock',
                value: _hideAppIcon,
                onChanged: _updateHideAppIcon,
              ),
              Divider(height: 0),
              AppSwitchListTile(
                title: 'Hide System Tray',
                subtitle:
                    'Completely hide app presence from system tray for maximum stealth',
                value: _hideSystemTray,
                onChanged: (value) async {
                  if (value) {
                    await showTrayHideInfoDialog(context);
                  }
                  await _updateHideSystemTray(value);
                },
              ),
              Divider(height: 0),
              AppSwitchListTile(
                title: 'Hide from Screen Sharing',
                subtitle:
                    'Prevent window from being captured in screen recordings',
                value: _hideFromScreenSharing,
                onChanged: _updateHideFromScreenSharing,
              ),
              // macOS Specific Settings
              if (defaultTargetPlatform == TargetPlatform.macOS) ...[
                Divider(height: 0),
                AppSwitchListTile(
                  title: 'Show on All Desktops',
                  subtitle: 'Make window visible across all macOS desktops',
                  value: _visibleOnAllWorkspaces,
                  onChanged: _updateVisibleOnAllWorkspaces,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppSettings() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'App Name',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 8),
          TextField(
            controller: _appNameController,
            decoration: InputDecoration(
              hintText: 'Enter application name',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) async {
              await _updateAppName(value);
            },
          ),
          SizedBox(height: 8),
          Text(
            'Changes how the app appears in ${defaultTargetPlatform == TargetPlatform.macOS ? 'Activity Monitor' : 'Task Manager'}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.6),
                ),
          ),
        ],
      ),
    );
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    final fontScale =
        prefs.getDouble(KEY_ANSWERS_FONT_SCALE) ?? DEFAULT_FONT_SCALE;
    final opacity =
        prefs.getDouble(KEY_WINDOW_OPACITY) ?? DEFAULT_WINDOW_OPACITY;
    final isAlwaysOnTop =
        prefs.getBool(KEY_WINDOW_ALWAYS_ON_TOP) ?? DEFAULT_ALWAYS_ON_TOP;
    final isSkipTaskbar =
        prefs.getBool(KEY_WINDOW_HIDE_APP_ICON) ?? DEFAULT_HIDE_APP_ICON;
    final isVisibleOnAllWorkspaces =
        prefs.getBool(KEY_WINDOW_VISIBLE_ALL_WORKSPACES) ??
            DEFAULT_VISIBLE_ALL_WORKSPACES;
    final isHideFromScreenSharing =
        prefs.getBool(KEY_WINDOW_HIDE_FROM_SCREEN_SHARING) ??
            DEFAULT_HIDE_FROM_SCREEN_SHARING;
    final isHideSystemTray =
        prefs.getBool(KEY_HIDE_SYSTEM_TRAY) ?? DEFAULT_HIDE_SYSTEM_TRAY;
    final appName = prefs.getString(KEY_APP_NAME) ?? DEFAULT_APP_NAME;

    if (mounted) {
      setState(() {
        _answersFontScale = fontScale;
        _windowOpacity = opacity;
        _alwaysOnTop = isAlwaysOnTop;
        _hideAppIcon = isSkipTaskbar;
        _visibleOnAllWorkspaces = isVisibleOnAllWorkspaces;
        _hideFromScreenSharing = isHideFromScreenSharing;
        _hideSystemTray = isHideSystemTray;
      });
      _appNameController.text = appName;
    }
  }

  Future<void> _restoreDefaults() async {
    await _updateFontScale(DEFAULT_FONT_SCALE);
    await _updateWindowOpacity(DEFAULT_WINDOW_OPACITY);
    await _updateAlwaysOnTop(DEFAULT_ALWAYS_ON_TOP);
    await _updateHideAppIcon(DEFAULT_HIDE_APP_ICON);
    await _updateVisibleOnAllWorkspaces(DEFAULT_VISIBLE_ALL_WORKSPACES);
    await _updateHideFromScreenSharing(DEFAULT_HIDE_FROM_SCREEN_SHARING);
    await _updateHideSystemTray(DEFAULT_HIDE_SYSTEM_TRAY);
    // Only restore app name for non-store builds
    if (!kIsStoreBuild) {
      await _restoreAppNameDefault();
    }

    toast('Settings restored to defaults');
  }

  Future<void> _updateFontScale(double value) async {
    setState(() => _answersFontScale = value);
    await _updateSetting(KEY_ANSWERS_FONT_SCALE, value);
  }

  Future<void> _updateWindowOpacity(double value) async {
    setState(() => _windowOpacity = value);
    await _updateSetting(KEY_WINDOW_OPACITY, value);
    await applyOpacitySetting(value);
  }

  Future<void> _updateAlwaysOnTop(bool value) async {
    setState(() => _alwaysOnTop = value);
    await _updateSetting(KEY_WINDOW_ALWAYS_ON_TOP, value);
    await applyAlwaysOnTopSetting(value);
  }

  Future<void> _updateHideAppIcon(bool value) async {
    setState(() => _hideAppIcon = value);
    await _updateSetting(KEY_WINDOW_HIDE_APP_ICON, value);
    await applySkipTaskbarSetting(value);
  }

  Future<void> _updateVisibleOnAllWorkspaces(bool value) async {
    setState(() => _visibleOnAllWorkspaces = value);
    await _updateSetting(KEY_WINDOW_VISIBLE_ALL_WORKSPACES, value);
    await applyVisibleOnAllWorkspacesSetting(value);
  }

  Future<void> _updateHideFromScreenSharing(bool value) async {
    setState(() => _hideFromScreenSharing = value);
    await _updateSetting(KEY_WINDOW_HIDE_FROM_SCREEN_SHARING, value);
    await applyHideFromScreenSharingSetting(value);
  }

  Future<void> _updateHideSystemTray(bool value) async {
    setState(() => _hideSystemTray = value);
    await _updateSetting(KEY_HIDE_SYSTEM_TRAY, value);
    await applyHideSystemTraySetting(value);
  }

  Future<void> _updateAppName(String value) async {
    await _updateSetting(KEY_APP_NAME, value);
    await applyAppNameSetting(value);
  }

  Future<void> _restoreAppNameDefault() async {
    _appNameController.text = DEFAULT_APP_NAME;
    await _updateAppName(DEFAULT_APP_NAME);
  }

  Future<void> _updateSetting(String key, dynamic value) async {
    final prefs = await SharedPreferences.getInstance();
    if (value is double) {
      await prefs.setDouble(key, value);
    } else if (value is bool) {
      await prefs.setBool(key, value);
    } else if (value is String) {
      await prefs.setString(key, value);
    }
  }
}

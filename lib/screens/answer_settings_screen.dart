import 'package:flutter/material.dart';
import 'package:interview_hammer/model/management/user_profile.dart';

import '../api/management/management_api.dart';
import '../model/management/answer_settings.dart';
import '../widgets/slider_with_warning.dart';

class AnswerSettingsScreen extends StatefulWidget {
  final UserProfile userProfile;

  const AnswerSettingsScreen({super.key, required this.userProfile});

  @override
  State<AnswerSettingsScreen> createState() => _AnswerSettingsScreenState();
}

class _AnswerSettingsScreenState extends State<AnswerSettingsScreen> {
  late BehavioralAnswerStructure _selectedStructure;
  late ResponseStyle _selectedStyle;
  late AnswerLength _selectedLength;
  late QuestionDetectionSensitivity _selectedSensitivity;
  late EnglishForTechnicalTerms _selectedEnglishForTechnicalTerms;
  late int _maxNumberOfImages;
  late int _transcriptDurationSeconds;

  @override
  void initState() {
    super.initState();
    final settings = widget.userProfile.answerSettings ?? AnswerSettings();
    _selectedStructure = settings.behavioralStructure;
    _selectedStyle = settings.responseStyle;
    _selectedLength = settings.length;
    _selectedSensitivity = settings.questionDetectionSensitivity;
    _selectedEnglishForTechnicalTerms = settings.englishForTechnicalTerms;
    _maxNumberOfImages = settings.maxNumberOfImages;
    _transcriptDurationSeconds = settings.transcriptDurationSeconds;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Answer Settings',
          style: textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.fromLTRB(20, 20, 20, 100),
        // Extra bottom padding for warnings
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader(
              'Behavioral Answer Structure',
              Icons.architecture,
              _selectedStructure.headerInfo,
            ),
            _buildCustomDropdown(
              value: _selectedStructure,
              items: BehavioralAnswerStructure.values,
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedStructure = value);
                  _updateAnswerSettings();
                }
              },
              getDisplayName: (value) => value.description,
              getInfoContent: (value) => value.info,
            ),
            SizedBox(height: 32),
            _buildSectionHeader(
              'Response Style',
              Icons.style,
              _selectedStyle.headerInfo,
            ),
            _buildCustomDropdown(
              value: _selectedStyle,
              items: ResponseStyle.values,
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedStyle = value);
                  _updateAnswerSettings();
                }
              },
              getDisplayName: (value) => value.description,
              getInfoContent: (value) => value.info,
            ),
            SizedBox(height: 32),
            _buildSectionHeader(
              'Answer Length',
              Icons.short_text,
              _selectedLength.headerInfo,
            ),
            _buildCustomDropdown(
              value: _selectedLength,
              items: AnswerLength.values,
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedLength = value);
                  _updateAnswerSettings();
                }
              },
              getDisplayName: (value) => value.description,
              getInfoContent: (value) => value.info,
            ),
            SizedBox(height: 32),
            _buildSectionHeader(
              'Question Detection Sensitivity',
              Icons.tune,
              _selectedSensitivity.headerInfo,
            ),
            _buildCustomDropdown(
              value: _selectedSensitivity,
              items: QuestionDetectionSensitivity.values,
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedSensitivity = value);
                  _updateAnswerSettings();
                }
              },
              getDisplayName: (value) => value.description,
              getInfoContent: (value) => value.info,
            ),
            if (widget.userProfile.answerLanguage?.isEnglish == false) ...[
              SizedBox(height: 32),
              _buildSectionHeader(
                'Use english for technical terms',
                Icons.tune,
                _selectedEnglishForTechnicalTerms.headerInfo,
              ),
              _buildCustomDropdown(
                value: _selectedEnglishForTechnicalTerms,
                items: EnglishForTechnicalTerms.values,
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _selectedEnglishForTechnicalTerms = value);
                    _updateAnswerSettings();
                  }
                },
                getDisplayName: (value) => value
                    .description(widget.userProfile.answerLanguage!.aiName),
                getInfoContent: (value) =>
                    value.info(widget.userProfile.answerLanguage!.aiName),
              ),
            ],
            SizedBox(height: 32),
            _buildSectionHeader(
              'Screenshot Limit',
              Icons.image,
              'Controls how many recent screenshots the AI analyzes.\nOnly increase the number if you expect to use many screenshots.\nIncreasing it may slow down the response time.',
            ),
            SliderWithWarning(
              value: _maxNumberOfImages.toDouble(),
              min: 1,
              max: 10,
              divisions: 9,
              label:
                  '$_maxNumberOfImages screenshot${_maxNumberOfImages > 1 ? 's' : ''}',
              onChanged: (value) {
                setState(() => _maxNumberOfImages = value.round());
                _updateAnswerSettings();
              },
              showWarning: _maxNumberOfImages > 3,
              warningMessage:
                  'More than 3 screenshots may slow response time and affect answer quality.',
              minLabel: '1 image',
              maxLabel: '10 images',
            ),
            SizedBox(height: 32),
            _buildSectionHeader(
              'Conversation Memory',
              Icons.text_snippet,
              'How much recent conversation the AI remembers.\nLonger memory provides better context but may slow responses.',
            ),
            SliderWithWarning(
              value: _transcriptDurationSeconds.toDouble(),
              min: 30,
              max: 180,
              divisions: 5,
              label: _formatDuration(_transcriptDurationSeconds),
              onChanged: (value) {
                setState(() => _transcriptDurationSeconds = value.round());
                _updateAnswerSettings();
              },
              showWarning: _transcriptDurationSeconds > 60,
              warningMessage:
                  'Memory longer than 1 minute may affect answer quality and response speed.',
              minLabel: '30s',
              maxLabel: '3m',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, String infoContent) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return InkWell(
      onTap: () => _showInfoDialog(context, title, infoContent),
      child: Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: Row(
          children: [
            Icon(icon, color: colorScheme.primary, size: 28),
            SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                  decoration: TextDecoration.underline,
                  decorationColor: colorScheme.primary.withValues(alpha: 0.3),
                  decorationThickness: 2,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomDropdown<T>({
    required T value,
    required List<T> items,
    required void Function(T?) onChanged,
    required String Function(T) getDisplayName,
    required String Function(T) getInfoContent,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(12),
        color: colorScheme.surfaceContainer,
      ),
      child: ListTile(
        title: Text(getDisplayName(value), style: textTheme.bodyLarge),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(Icons.info_outline,
                  size: 20, color: colorScheme.primary),
              onPressed: () => _showInfoDialog(
                  context, getDisplayName(value), getInfoContent(value)),
            ),
            Icon(Icons.arrow_drop_down, color: colorScheme.primary),
          ],
        ),
        onTap: () => _showSelectionDialog(
          context,
          value,
          items,
          onChanged,
          getDisplayName,
          getInfoContent,
        ),
      ),
    );
  }

  void _showSelectionDialog<T>(
    BuildContext context,
    T currentValue,
    List<T> items,
    void Function(T?) onChanged,
    String Function(T) getDisplayName,
    String Function(T) getInfoContent,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Select Option',
            style: textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: ListBody(
              children: items.map((T item) {
                final isSelected = currentValue == item;
                return Container(
                  decoration: BoxDecoration(
                    color: isSelected ? colorScheme.primaryContainer : null,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListTile(
                    title: Text(
                      getDisplayName(item),
                      style: textTheme.bodyLarge?.copyWith(
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected ? colorScheme.primary : null,
                      ),
                    ),
                    trailing: isSelected
                        ? Icon(Icons.check, color: colorScheme.primary)
                        : null,
                    onTap: () {
                      onChanged(item);
                      Navigator.of(context).pop();
                    },
                  ),
                );
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  void _showInfoDialog(BuildContext context, String title, String content) {
    final textTheme = Theme.of(context).textTheme;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Text(content, style: textTheme.bodyLarge),
          ),
          actions: [
            TextButton(
              child: Text('Close'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }

  void _updateAnswerSettings() {
    final updatedSettings = AnswerSettings(
      behavioralStructure: _selectedStructure,
      responseStyle: _selectedStyle,
      length: _selectedLength,
      questionDetectionSensitivity: _selectedSensitivity,
      englishForTechnicalTerms: _selectedEnglishForTechnicalTerms,
      maxNumberOfImages: _maxNumberOfImages,
      transcriptDurationSeconds: _transcriptDurationSeconds,
    );
    updateAnswerSettings((_) => updatedSettings);
  }

  String _formatDuration(int seconds) {
    if (seconds < 60) {
      return '${seconds}s';
    } else {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      if (remainingSeconds == 0) {
        return '${minutes}m';
      } else {
        return '${minutes}m ${remainingSeconds}s';
      }
    }
  }
}

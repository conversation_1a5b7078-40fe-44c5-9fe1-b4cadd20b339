import 'package:auto_size_text/auto_size_text.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/debug_data.dart';
import 'package:interview_hammer/model/management/language.dart';
import 'package:interview_hammer/model/management/user_profile_extensions.dart';
import 'package:interview_hammer/providers/theme_provider.dart';
import 'package:interview_hammer/screens/answer_settings_screen.dart';
import 'package:interview_hammer/screens/personal_info_screen.dart';
import 'package:interview_hammer/screens/plan/plan_details_screen.dart';
import 'package:interview_hammer/screens/server/mode_selection_screen.dart';
import 'package:interview_hammer/screens/splash_screen.dart';
import 'package:interview_hammer/utils/string_utils.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../api/management/management_api.dart';
import '../../api/management/remote_config.dart';
import '../../api/management/user_profile_manager.dart';
import '../../model/management/personal_info.dart';
import '../../model/management/user_profile.dart';
import '../../utils/external_services.dart';
import '../../utils/platform_utils.dart';
import '../../widgets/app_switch_tile.dart';
import '../../widgets/app_text_field.dart';
import '../../widgets/bottom_sheet_dropdowns/app_bottom_sheet_dropdown_multi_search.dart';
import '../../widgets/bottom_sheet_dropdowns/app_bottom_sheet_dropdown_search.dart';
import '../../widgets/custom_card.dart';
import '../../widgets/custom_card_with_arrow.dart';
import '../../widgets/user_profile_stream_builder.dart';
import '../display_settings_screen.dart';
import 'info_tooltip.dart';
import 'zoom_warning.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _automaticQuestionAnswering = true;
  bool _showTranscript = true;
  bool _showCustomMessageInput = true;

  late TextEditingController _interviewTopicController;
  late TextEditingController _extraInstructionsController;

  String _versionInfo = '';

  @override
  void initState() {
    super.initState();

    userProfileManager.initialize();

    setupUserForExternalServices();

    _interviewTopicController = TextEditingController();
    _extraInstructionsController = TextEditingController();

    _getVersionInfo();
  }

  Future<void> _getVersionInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _versionInfo = '${packageInfo.version} (${packageInfo.buildNumber})';
    });
  }

  @override
  void dispose() {
    _interviewTopicController.dispose();
    _extraInstructionsController.dispose();
    super.dispose();
  }

  bool _didInitSettings = false;

  void _initSettings(UserProfile profile) {
    if (_didInitSettings) return;
    _didInitSettings = true;

    _automaticQuestionAnswering = profile.automaticQuestionAnswering ?? true;
    _showTranscript = profile.showTranscript ?? true;
    _showCustomMessageInput = profile.showCustomMessageInput ?? false;

    _interviewTopicController.text = profile.interviewTopic ?? '';
    _extraInstructionsController.text = profile.extraInstructions ?? '';

    if (addDebugData) {
      updateUser(profile.copyWith(
        interviewTopic: d_interviewTopic,
        extraInstructions: d_extraInstructions,
        personalInfoData: d_personalInfoData,
      ));
    }
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Column(
      children: [
        Row(
          children: [
            Icon(icon, color: Theme.of(context).colorScheme.primary, size: 32),
            SizedBox(width: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
            ),
          ],
        ),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildSubscriptionSection(UserProfile profile) {
    String planType = profile.planType ?? PLAN_TYPE_FREE;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Current Plan', Icons.card_membership),
        CustomCardWithArrow(
          title: 'Plan Details',
          subtitle: 'Your current plan: ${planType.capitalize()}',
          onTap: () => push(context, PlanDetailsScreen()),
          extraContent: Text(
            '(One time payment, no subscription)',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontStyle: FontStyle.italic,
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildSessionSection(UserProfile profile) {
    return CustomCard(
      child: Column(
        children: [
          AppSwitchListTile(
            title: "Automatic question answering",
            value: _automaticQuestionAnswering,
            onChanged: (value) {
              setState(() => _automaticQuestionAnswering = value);
              updateUser(UserProfile(
                  automaticQuestionAnswering: _automaticQuestionAnswering));
            },
          ),
          Divider(height: 0),
          AppSwitchListTile(
            title: "Show live transcript",
            value: _showTranscript,
            onChanged: (value) {
              setState(() => _showTranscript = value);
              updateUser(UserProfile(showTranscript: _showTranscript));
            },
          ),
          Divider(height: 0),
          AppSwitchListTile(
            title: "Show custom message input",
            subtitle: "Used for custom message or transcription fix",
            value: _showCustomMessageInput,
            onChanged: (value) {
              setState(() => _showCustomMessageInput = value);
              updateUser(
                  UserProfile(showCustomMessageInput: _showCustomMessageInput));
            },
          ),
          Divider(height: 0),
          AppBottomSheetDropdownMultiSearch(
            label: "What language(s) will the interview be conducted in?",
            hintText: 'Select interview language(s)',
            items: Language.values,
            initialItems: profile.interviewLanguages,
            onListChanged: (List<Language> selectedLanguages) {
              updateUser(UserProfile(interviewLanguages: selectedLanguages));
            },
          ),
          Divider(height: 0),
          AppBottomSheetDropdownSearch(
            label: "In what language do you want the generated answers?",
            hintText: 'Select answer language',
            items: Language.values,
            initialItem: profile.answerLanguage,
            onChanged: (Language? selectedLanguage) {
              if (selectedLanguage != null) {
                updateUser(UserProfile(answerLanguage: selectedLanguage));
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInterviewSettingsSection(UserProfile profile) {
    return Column(
      children: [
        AppTextField(
          labelText: 'Interview Topic',
          hintText: 'Enter interview topic',
          icon: Icons.title,
          controller: _interviewTopicController,
          onChanged: (value) => updateUser(UserProfile(interviewTopic: value)),
        ),
        SizedBox(height: 20),
        CustomCardWithArrow(
          title: 'Personal Info',
          subtitle:
              'Edit your personal information for more personalized answers.',
          onTap: () => push(
              context,
              PersonalInfoScreen(
                  personalInfo: profile.personalInfoData ?? PersonalInfo())),
        ),
        SizedBox(height: 20),
        CustomCardWithArrow(
          title: 'Answer Settings',
          subtitle:
              'Customize your answer settings for more personalized answers.',
          onTap: () =>
              push(context, AnswerSettingsScreen(userProfile: profile)),
        ),
        SizedBox(height: 20),
        AppTextField(
          labelText: 'Extra Instructions',
          hintText:
              'Write extra instructions here (to disable it leave it empty).\nFor example: "Give detailed answers" or "Craft your responses in an easy-to-understand way, suitable for reading out loud."',
          icon: Icons.list,
          controller: _extraInstructionsController,
          onChanged: (value) =>
              updateUser(UserProfile(extraInstructions: value)),
          maxLines: 6,
        ),
      ],
    );
  }

  PreferredSizeWidget _buildAppBar(UserProfile profile) {
    final colorScheme = Theme.of(context).colorScheme;
    final screenWidth = MediaQuery.of(context).size.width;

    return PreferredSize(
      preferredSize: Size.fromHeight(kToolbarHeight + 20),
      child: AppBar(
        title: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AutoSizeText(
                    "InterviewHammer",
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                          letterSpacing: 0.5,
                        ),
                    maxLines: 1,
                    minFontSize: 14,
                    maxFontSize: 24,
                  ),
                  SizedBox(height: 4),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'by MeetingAi',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: colorScheme.onPrimaryContainer,
                            letterSpacing: 0.3,
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(
              context.watch<ThemeProvider>().isDarkMode
                  ? Icons.dark_mode
                  : Icons.light_mode,
              color: colorScheme.primary,
            ),
            onPressed: () => context.read<ThemeProvider>().toggleTheme(),
          ),
          PopupMenuButton<String>(
            offset: Offset(0, 36),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            itemBuilder: (context) => [
              PopupMenuItem(
                child: Text('Contact Support'),
                onTap: () async {
                  await _launchDiscord();
                },
              ),
              PopupMenuItem(
                child: Text('Delete account'),
                onTap: () async {
                  final deleteAccountUrl =
                      remoteConfig.getString(KEY_DELETE_ACCOUNT_URL);
                  await launchUrl(Uri.parse(deleteAccountUrl));
                },
              ),
              PopupMenuItem(
                child: Text('Log Out'),
                onTap: () async {
                  await FirebaseAuth.instance.signOut();
                  logoutUserFromExternalServices();
                  if (context.mounted) {
                    pushReplacement(context, SplashScreen());
                  }
                },
              ),
              PopupMenuDivider(),
              PopupMenuItem(
                enabled: false,
                child: Text(
                  'Version $_versionInfo',
                  style: TextStyle(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                    fontSize: 12,
                  ),
                ),
              ),
            ],
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: screenWidth * 0.3),
                    child: AutoSizeText(
                      profile.email ?? 'Loading...',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                          ),
                      maxLines: 2,
                      minFontSize: 12,
                      maxFontSize: 16,
                    ),
                  ),
                  Icon(Icons.arrow_drop_down, color: colorScheme.primary),
                ],
              ),
            ),
          ),
        ],
        elevation: 0,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      ),
    );
  }

  Future<void> _launchDiscord() async {
    final discordLink = remoteConfig.getString(KEY_DISCORD_LINK);
    try {
      await launchUrl(Uri.parse(discordLink));
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not launch Discord support link')),
        );
      }
    }
  }

  void _showUnsupportedLanguageDialog(UserProfile? currentProfile) {
    final unsupportedLanguages = currentProfile?.interviewLanguages
        ?.where(
          (lang) => lang.transcriptionProvider == TranscriptionProvider.AZURE,
        )
        .map((lang) => lang.label)
        .toList();

    if (unsupportedLanguages == null || unsupportedLanguages.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text("Language Not Supported on Mobile App"),
        content: Text(
          'The selected language(s) ${unsupportedLanguages.join(', ')} are not supported on the mobile app.\n\n'
          'Please switch to the web app to use this feature:\n'
          '${remoteConfig.getString(KEY_WEB_APP_URL)}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK'),
          ),
          TextButton(
            onPressed: () {
              launchUrl(Uri.parse(remoteConfig.getString(KEY_WEB_APP_URL)));
            },
            child: Text('Open Web App'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: UserProfileStreamBuilder(builder: (context, profile) {
        _initSettings(profile);

        return Scaffold(
          appBar: _buildAppBar(profile),
          body: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InfoTooltip(),
                ZoomWarning(),
                Padding(
                  padding: EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSubscriptionSection(profile),
                      SizedBox(height: 32),
                      _buildSectionHeader('Interview Settings', Icons.mic),
                      _buildInterviewSettingsSection(profile),
                      SizedBox(height: 32),
                      _buildSectionHeader('Session Settings', Icons.settings),
                      _buildSessionSection(profile),
                      SizedBox(height: 20),
                      CustomCardWithArrow(
                        title: 'Display Settings',
                        subtitle: isDesktopPlatform()
                            ? 'Customize window size, transparency, and text size.'
                            : 'Customize text size and other display options.',
                        onTap: () => push(context, DisplaySettingsScreen()),
                      ),
                      SizedBox(height: 72),
                    ],
                  ),
                ),
              ],
            ),
          ),
          floatingActionButton: FloatingActionButton.extended(
            onPressed: () {
              final currentProfile = userProfileManager.userProfile;
              // Azure transcription is only available on web.
              if (currentProfile?.getTranscriptionProvider() ==
                      TranscriptionProvider.AZURE &&
                  !kIsWeb) {
                _showUnsupportedLanguageDialog(currentProfile);
                return;
              }
              if (currentProfile != null && context.mounted) {
                push(context, ModeSelectionScreen(profile: currentProfile));
              }
            },
            label: Text(
              'Start',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            icon: Icon(Icons.play_arrow),
            mouseCursor: SystemMouseCursors.basic,
          ),
        );
      }),
    );
  }
}

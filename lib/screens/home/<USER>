import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:interview_hammer/utils/platform_utils.dart';

import '../../utils/zoom.dart';

/// A widget that shows a warning if the user has an incompatible version of Zoom installed
/// This is only relevant for macOS users where the hide from screen sharing feature
/// doesn't work with Zoom versions newer than 6.1.5
class ZoomWarning extends StatefulWidget {
  const ZoomWarning({super.key});

  @override
  State<ZoomWarning> createState() => _ZoomWarningState();
}

class _ZoomWarningState extends State<ZoomWarning> {
  bool _isLoading = true;
  bool _showWarning = false;
  ZoomInfo? _zoomInfo;

  // Version above which screen sharing hiding doesn't work
  static const String _incompatibleVersionThreshold = '6.1.5';

  @override
  void initState() {
    super.initState();
    _checkZoomVersion();
  }

  /// Checks if Zoom is installed and compares its version against the threshold
  Future<void> _checkZoomVersion() async {
    // Only relevant for macOS desktop
    if (!kIsNativeDesktop || defaultTargetPlatform != TargetPlatform.macOS) {
      setState(() {
        _isLoading = false;
        _showWarning = false;
      });
      return;
    }

    final zoomInfo = await detectZoom();

    // Check if Zoom is installed and has a version number
    bool isIncompatible = false;
    if (zoomInfo.installed && zoomInfo.version != null) {
      isIncompatible =
          _isNewerVersion(zoomInfo.version!, _incompatibleVersionThreshold);
    }

    setState(() {
      _zoomInfo = zoomInfo;
      _isLoading = false;
      _showWarning = isIncompatible;
    });
  }

  /// Compare two version strings to determine if version1 is newer than version2
  bool _isNewerVersion(String version1, String version2) {
    final List<int> v1Parts =
        version1.split('.').map((part) => int.tryParse(part) ?? 0).toList();

    final List<int> v2Parts =
        version2.split('.').map((part) => int.tryParse(part) ?? 0).toList();

    // Ensure both lists have the same length for comparison
    while (v1Parts.length < v2Parts.length) {
      v1Parts.add(0);
    }
    while (v2Parts.length < v1Parts.length) {
      v2Parts.add(0);
    }

    // Compare each part of the version
    for (int i = 0; i < v1Parts.length; i++) {
      if (v1Parts[i] > v2Parts[i]) {
        return true; // version1 is newer
      } else if (v1Parts[i] < v2Parts[i]) {
        return false; // version1 is older
      }
      // If equal, continue to next part
    }

    return false; // Versions are equal
  }

  Color _getBackgroundColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.orange.shade50
        : Colors.orange.withValues(alpha: 0.15);
  }

  Color _getBorderColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.orange.shade200
        : Colors.orange.withValues(alpha: 0.3);
  }

  Color _getTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.orange.shade900
        : Colors.orange.shade100;
  }

  Color _getIconColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.orange.shade800
        : Colors.orange.shade600;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || !_showWarning) {
      return const SizedBox.shrink();
    }

    final currentVersion = _zoomInfo?.version ?? 'unknown';

    return Container(
      margin: EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getBackgroundColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getBorderColor(context),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: _getIconColor(context),
            size: 24,
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Zoom Compatibility Warning',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: _getTextColor(context),
                        fontWeight: FontWeight.bold,
                      ),
                ),
                SizedBox(height: 8),
                Text(
                  'The "Hide from screen sharing" feature does not work with your current version of Zoom ($currentVersion).',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: _getTextColor(context),
                      ),
                ),
                SizedBox(height: 8),
                Text(
                  'To use this feature, please downgrade Zoom to version $_incompatibleVersionThreshold or older.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: _getTextColor(context),
                      ),
                ),
                SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Text(
                      'Recommended Zoom version: ',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: _getTextColor(context),
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getIconColor(context),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        'v$_incompatibleVersionThreshold or older',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.2,
                            ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

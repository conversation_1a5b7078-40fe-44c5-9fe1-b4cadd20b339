import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../api/management/remote_config.dart';
import '../../utils/platform_utils.dart';

class InfoTooltip extends StatelessWidget {
  InfoTooltip({super.key});

  final mobileTooltipText =
      "Important: Use a different device for the mock interview, not this {{platform}} device.\n"
      "• {{platform}} only allows one app to use the microphone at a time.\n"
      "• Don't use headphones - the app uses the device's mic, not system audio.";
  final webTooltipText = remoteConfig.getString(KEY_WEB_TOOLTIP);

  Color _getBackgroundColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.blue.shade50
        : Theme.of(context).colorScheme.primary.withValues(alpha: 0.15);
  }

  Color _getBorderColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.blue.shade200
        : Theme.of(context).colorScheme.primary.withValues(alpha: 0.3);
  }

  Color _getTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.blue.shade800
        : Colors.white.withValues(alpha: 0.9);
  }

  Color _getIconColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.blue
        : Theme.of(context).colorScheme.primary;
  }

  @override
  Widget build(BuildContext context) {
    final String tooltipText;
    if (kIsMobile) {
      final platform =
          defaultTargetPlatform == TargetPlatform.android ? 'Android' : 'iOS';
      tooltipText = mobileTooltipText.replaceAll('{{platform}}', platform);
    } else {
      tooltipText = webTooltipText;
    }

    if (tooltipText.isEmpty) {
      return Container();
    }

    return Container(
      margin: EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getBackgroundColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getBorderColor(context),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.info_outline,
            color: _getIconColor(context),
            size: 24,
          ),
          SizedBox(width: 16),
          Expanded(
            child: Text(
              tooltipText,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: _getTextColor(context),
                  ),
            ),
          ),
        ],
      ),
    );
  }
}

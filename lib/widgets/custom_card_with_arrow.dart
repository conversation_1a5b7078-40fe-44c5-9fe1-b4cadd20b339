import 'package:flutter/material.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:interview_hammer/widgets/custom_card.dart';

class CustomCardWithArrow extends StatelessWidget {
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  final Widget? extraContent;

  const CustomCardWithArrow({
    super.key,
    required this.title,
    required this.subtitle,
    required this.onTap,
    this.extraContent,
  });

  @override
  Widget build(BuildContext context) {
    return InkWellStacked(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: CustomCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: GoogleFonts.poppins(fontSize: 14),
                    ),
                    if (extraContent != null) const SizedBox(height: 2),
                    if (extraContent != null) extraContent!,
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }
}

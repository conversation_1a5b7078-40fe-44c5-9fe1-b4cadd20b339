import 'package:flutter/material.dart';

class SharedDropdownComponents {
  /// Builds the label widget for dropdown components
  static Widget buildDropdownLabel(BuildContext context, String label) {
    return Padding(
      padding: const EdgeInsetsDirectional.only(start: 16, top: 16, bottom: 8),
      child: Text(
        label,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
            ),
      ),
    );
  }

  /// Builds the dropdown button with consistent styling
  static Widget buildDropdownButton({
    required BuildContext context,
    required String displayText,
    required bool hasSelection,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(0),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                displayText,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: hasSelection
                          ? Theme.of(context).colorScheme.onSurface
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  /// Shows the modal bottom sheet with consistent configuration
  static void showBottomSheetModal<T>({
    required BuildContext context,
    required Widget Function(ScrollController) contentBuilder,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.0,
        maxChildSize: 0.8,
        snap: true,
        shouldCloseOnMinExtent: true,
        expand: false,
        builder: (context, scrollController) =>
            contentBuilder(scrollController),
      ),
    );
  }

  /// Builds the complete dropdown widget structure
  static Widget buildDropdownWidget({
    required BuildContext context,
    required String displayText,
    required bool hasSelection,
    required VoidCallback onTap,
    String? label,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) buildDropdownLabel(context, label),
        buildDropdownButton(
          context: context,
          displayText: displayText,
          hasSelection: hasSelection,
          onTap: onTap,
        ),
      ],
    );
  }
}

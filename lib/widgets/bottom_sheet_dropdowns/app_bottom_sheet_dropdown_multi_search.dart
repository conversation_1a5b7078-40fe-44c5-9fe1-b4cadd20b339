import 'package:flutter/material.dart';

import 'bottom_sheet_dropdown_base.dart';
import 'dropdown_display_utils.dart';
import 'dropdown_utils.dart';
import 'shared_dropdown_components.dart';

class AppBottomSheetDropdownMultiSearch<T> extends StatefulWidget {
  final String hintText;
  final List<T> items;
  final List<T>? initialItems;
  final ValueChanged<List<T>>? onListChanged;
  final String? label;

  const AppBottomSheetDropdownMultiSearch({
    super.key,
    required this.hintText,
    required this.items,
    this.initialItems,
    this.onListChanged,
    this.label,
  });

  @override
  State<AppBottomSheetDropdownMultiSearch<T>> createState() =>
      _AppBottomSheetDropdownMultiSearchState<T>();
}

class _AppBottomSheetDropdownMultiSearchState<T>
    extends State<AppBottomSheetDropdownMultiSearch<T>> {
  List<T> _selectedItems = [];

  @override
  void initState() {
    super.initState();
    _selectedItems = List.from(widget.initialItems ?? []);
  }

  @override
  Widget build(BuildContext context) {
    return SharedDropdownComponents.buildDropdownWidget(
      context: context,
      displayText: DropdownDisplayUtils.getMultiDisplayText(
          _selectedItems, widget.hintText),
      hasSelection: DropdownDisplayUtils.hasMultiSelection(_selectedItems),
      onTap: _showBottomSheet,
      label: widget.label,
    );
  }

  void _showBottomSheet() {
    SharedDropdownComponents.showBottomSheetModal<T>(
      context: context,
      contentBuilder: (scrollController) => _BottomSheetMultiContent<T>(
        items: widget.items,
        selectedItems: _selectedItems,
        hintText: widget.hintText,
        scrollController: scrollController,
        onSelectionChanged: (items) {
          setState(() {
            _selectedItems = items;
          });
          widget.onListChanged?.call(items);
        },
      ),
    );
  }
}

class _BottomSheetMultiContent<T> extends BottomSheetContentBase<T> {
  final List<T> selectedItems;
  final ValueChanged<List<T>> onSelectionChanged;

  const _BottomSheetMultiContent({
    required super.items,
    required super.hintText,
    required this.selectedItems,
    required this.onSelectionChanged,
    super.scrollController,
  });

  @override
  State<_BottomSheetMultiContent<T>> createState() =>
      _BottomSheetMultiContentState<T>();
}

class _BottomSheetMultiContentState<T>
    extends BottomSheetContentBaseState<T, _BottomSheetMultiContent<T>> {
  late List<T> _tempSelectedItems;

  @override
  void initState() {
    super.initState();
    _tempSelectedItems = List.from(widget.selectedItems);
  }

  void _toggleItem(T item) {
    setState(() {
      if (_tempSelectedItems.contains(item)) {
        _tempSelectedItems.remove(item);
      } else {
        _tempSelectedItems.add(item);
      }
    });
  }

  @override
  Widget buildListItem(T item) {
    final isSelected = _tempSelectedItems.contains(item);

    return BottomSheetDropdownUtils.buildLanguageCheckboxTile(
      context: context,
      item: item,
      isSelected: isSelected,
      onChanged: (value) => _toggleItem(item),
    );
  }

  @override
  List<Widget> buildFooterWidgets() {
    return [
      Container(
        width: double.infinity,
        padding: EdgeInsets.all(16),
        decoration:
            BottomSheetDropdownUtils.getFooterContainerDecoration(context),
        child: ElevatedButton(
          onPressed: () {
            widget.onSelectionChanged(_tempSelectedItems);
            Navigator.of(context).pop();
          },
          style: BottomSheetDropdownUtils.getFooterButtonStyle(context),
          child: Text(
            'Done (${_tempSelectedItems.length} selected)',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
          ),
        ),
      ),
    ];
  }
}

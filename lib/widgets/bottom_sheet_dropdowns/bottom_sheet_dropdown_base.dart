import 'package:flutter/material.dart';

import '../../model/management/language.dart';

abstract class BottomSheetContentBase<T> extends StatefulWidget {
  final List<T> items;
  final String hintText;
  final ScrollController? scrollController;

  const BottomSheetContentBase({
    super.key,
    required this.items,
    required this.hintText,
    this.scrollController,
  });
}

abstract class BottomSheetContentBaseState<T,
    W extends BottomSheetContentBase<T>> extends State<W> {
  late TextEditingController searchController;
  late List<T> filteredItems;

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();
    filteredItems = widget.items;
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  void filterItems(String query) {
    setState(() {
      if (query.isEmpty) {
        filteredItems = widget.items;
      } else {
        filteredItems = widget.items.where((item) {
          if (item is Language) {
            return item.filter(query);
          }
          return item.toString().toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          buildDragHandle(),
          buildSearchField(),
          Expanded(
            child: buildContent(),
          ),
          ...buildFooterWidgets(),
        ],
      ),
    );
  }

  Widget buildDragHandle() {
    return Container(
      margin: EdgeInsets.only(top: 8),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: Theme.of(context)
            .colorScheme
            .onSurfaceVariant
            .withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget buildSearchField() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: TextField(
        controller: searchController,
        onChanged: (value) {
          filterItems(value);
          setState(() {}); // Trigger rebuild for clear button visibility
        },
        decoration: InputDecoration(
          hintText: 'Search...',
          prefixIcon: Icon(Icons.search),
          suffixIcon: searchController.text.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear),
                  onPressed: () {
                    searchController.clear();
                    filterItems('');
                    setState(() {}); // Trigger rebuild to hide clear button
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        ),
      ),
    );
  }

  Widget buildContent() {
    return filteredItems.isEmpty
        ? Center(
            child: Text(
              'No results found',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
          )
        : ListView.builder(
            controller: widget.scrollController,
            itemCount: filteredItems.length,
            itemBuilder: (context, index) {
              return buildListItem(filteredItems[index]);
            },
          );
  }

  Widget buildListItem(T item);

  List<Widget> buildFooterWidgets() => [];
}

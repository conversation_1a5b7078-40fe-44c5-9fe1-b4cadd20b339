class DropdownDisplayUtils {
  /// Gets display text for single selection
  static String getSingleDisplayText<T>(T? selectedItem, String hintText) {
    return selectedItem?.toString() ?? hintText;
  }

  /// Gets display text for multi-selection with smart truncation
  static String getMultiDisplayText<T>(List<T> selectedItems, String hintText) {
    if (selectedItems.isEmpty) {
      return hintText;
    } else if (selectedItems.length == 1) {
      return selectedItems.first.toString();
    } else if (selectedItems.length <= 2) {
      return selectedItems.map((e) => e.toString()).join(', ');
    } else {
      return '${selectedItems.length} selected';
    }
  }

  /// Checks if there is any selection
  static bool hasSingleSelection<T>(T? selectedItem) {
    return selectedItem != null;
  }

  /// Checks if there are any selections in multi-select
  static bool hasMultiSelection<T>(List<T> selectedItems) {
    return selectedItems.isNotEmpty;
  }
}

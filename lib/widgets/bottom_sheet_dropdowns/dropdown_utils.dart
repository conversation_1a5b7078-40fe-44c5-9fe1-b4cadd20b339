import 'package:flutter/material.dart';

import 'dropdown_item.dart';

class BottomSheetDropdownUtils {
  static Widget buildLanguageListTile({
    required BuildContext context,
    required dynamic item,
    required bool isSelected,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return ListTile(
      leading: _buildLeadingWidget(item),
      title: _buildTitleText(context, item, isSelected),
      trailing: trailing,
      onTap: onTap,
    );
  }

  static Widget buildLanguageCheckboxTile({
    required BuildContext context,
    required dynamic item,
    required bool isSelected,
    required ValueChanged<bool?> onChanged,
  }) {
    return CheckboxListTile(
      value: isSelected,
      onChanged: onChanged,
      title: _buildTitleText(context, item, false),
      secondary: _buildLeadingWidget(item),
      activeColor: Theme.of(context).colorScheme.primary,
      checkColor: Theme.of(context).colorScheme.onPrimary,
    );
  }

  static Widget buildCheckIcon(BuildContext context) {
    return Icon(
      Icons.check,
      color: Theme.of(context).colorScheme.primary,
    );
  }

  static Widget? _buildLeadingWidget(dynamic item) {
    if (item is DropdownItem) {
      return item.leadingWidget;
    }
    return null;
  }

  static Widget _buildTitleText(
      BuildContext context, dynamic item, bool isSelected) {
    final displayText =
        item is DropdownItem ? item.displayText : item.toString();
    return Text(
      displayText,
      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface,
          ),
    );
  }

  static ButtonStyle getFooterButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Theme.of(context).colorScheme.onPrimary,
      padding: EdgeInsets.symmetric(vertical: 12),
    );
  }

  static BoxDecoration getFooterContainerDecoration(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).colorScheme.surface,
      border: Border(
        top: BorderSide(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
    );
  }
}

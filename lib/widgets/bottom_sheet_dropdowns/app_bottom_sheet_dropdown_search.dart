import 'package:flutter/material.dart';

import 'bottom_sheet_dropdown_base.dart';
import 'dropdown_display_utils.dart';
import 'dropdown_utils.dart';
import 'shared_dropdown_components.dart';

class AppBottomSheetDropdownSearch<T> extends StatefulWidget {
  final String hintText;
  final List<T> items;
  final T? initialItem;
  final ValueChanged<T?>? onChanged;
  final String? label;

  const AppBottomSheetDropdownSearch({
    super.key,
    required this.hintText,
    required this.items,
    this.initialItem,
    this.onChanged,
    this.label,
  });

  @override
  State<AppBottomSheetDropdownSearch<T>> createState() =>
      _AppBottomSheetDropdownSearchState<T>();
}

class _AppBottomSheetDropdownSearchState<T>
    extends State<AppBottomSheetDropdownSearch<T>> {
  T? _selectedItem;

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.initialItem;
  }

  @override
  Widget build(BuildContext context) {
    return SharedDropdownComponents.buildDropdownWidget(
      context: context,
      displayText: DropdownDisplayUtils.getSingleDisplayText(
          _selectedItem, widget.hintText),
      hasSelection: DropdownDisplayUtils.hasSingleSelection(_selectedItem),
      onTap: _showBottomSheet,
      label: widget.label,
    );
  }

  void _showBottomSheet() {
    SharedDropdownComponents.showBottomSheetModal<T>(
      context: context,
      contentBuilder: (scrollController) => _BottomSheetContent<T>(
        items: widget.items,
        selectedItem: _selectedItem,
        hintText: widget.hintText,
        scrollController: scrollController,
        onItemSelected: (item) {
          setState(() {
            _selectedItem = item;
          });
          widget.onChanged?.call(item);
          Navigator.of(context).pop();
        },
      ),
    );
  }
}

class _BottomSheetContent<T> extends BottomSheetContentBase<T> {
  final T? selectedItem;
  final ValueChanged<T?> onItemSelected;

  const _BottomSheetContent({
    required super.items,
    required super.hintText,
    required this.selectedItem,
    required this.onItemSelected,
    super.scrollController,
  });

  @override
  State<_BottomSheetContent<T>> createState() => _BottomSheetContentState<T>();
}

class _BottomSheetContentState<T>
    extends BottomSheetContentBaseState<T, _BottomSheetContent<T>> {
  @override
  Widget buildListItem(T item) {
    final isSelected = item == widget.selectedItem;

    return BottomSheetDropdownUtils.buildLanguageListTile(
      context: context,
      item: item,
      isSelected: isSelected,
      onTap: () => widget.onItemSelected(item),
      trailing:
          isSelected ? BottomSheetDropdownUtils.buildCheckIcon(context) : null,
    );
  }
}

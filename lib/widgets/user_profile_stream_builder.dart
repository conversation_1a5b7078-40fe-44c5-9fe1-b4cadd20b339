import 'package:flutter/material.dart';
import 'package:interview_hammer/api/management/user_profile_manager.dart';
import 'package:interview_hammer/model/management/user_profile.dart';
import 'package:interview_hammer/utils/events/events.dart';

class UserProfileStreamBuilder extends StatelessWidget {
  final Widget Function(BuildContext, UserProfile) builder;

  const UserProfileStreamBuilder({super.key, required this.builder});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<UserProfile?>(
      stream: userProfileManager.userProfileStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData || snapshot.data == null) {
          logError("UserProfileStreamBuilder: No user profile data available");
          return Center(child: Text('No user profile data available'));
        }

        return builder(context, snapshot.data!);
      },
    );
  }
}

import 'package:flutter/material.dart';

class AppSwitchListTile extends StatelessWidget {
  final String title;
  final bool value;
  final Function(bool) onChanged;
  final String? subtitle;

  const AppSwitchListTile({
    required this.title,
    required this.value,
    required this.onChanged,
    this.subtitle,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return SwitchListTile(
      title: Text(
        title,
        style: textTheme.bodyMedium,
      ),
      value: value,
      onChanged: onChanged,
      dense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: textTheme.bodySmall,
            )
          : null,
    );
  }
}

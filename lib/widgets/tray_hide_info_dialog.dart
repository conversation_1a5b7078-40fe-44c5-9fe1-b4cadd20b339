import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/platform_utils.dart';

const KEY_DONT_SHOW_TRAY_HIDE_INFO = "dont_show_tray_hide_info";

/// Dialog that shows information about how to reopen the app when tray is hidden
class TrayHideInfoDialog extends StatefulWidget {
  const TrayHideInfoDialog({super.key});

  @override
  State<TrayHideInfoDialog> createState() => _TrayHideInfoDialogState();
}

class _TrayHideInfoDialogState extends State<TrayHideInfoDialog> {
  bool _dontShowAgain = false;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: colorScheme.primary,
            size: 28,
          ),
          const SizedBox(width: 12),
          Text('Tray Icon Hidden'),
        ],
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
      titleTextStyle: GoogleFonts.poppins(
        fontWeight: FontWeight.w600,
        fontSize: 18,
        color: colorScheme.onSurface,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'The system tray icon is now hidden. To open the app again, ${_getRestoreInstructions()}',
            style: GoogleFonts.poppins(
              fontSize: 14,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 20),
          _buildPlatformSpecificInstructions(context),
          const SizedBox(height: 20),
          CheckboxListTile(
            value: _dontShowAgain,
            onChanged: (bool? value) {
              setState(() {
                _dontShowAgain = value ?? false;
              });
            },
            title: Text(
              "Don't show this message again",
              style: GoogleFonts.poppins(fontSize: 14),
            ),
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Got it',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
      actionsPadding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
    );
  }

  Widget _buildPlatformSpecificInstructions(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (kIsNativeMacOS) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              Icons.search,
              color: colorScheme.primary,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Press Cmd + Space',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    'Then search for "Interview Hammer"',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              Icons.search,
              color: colorScheme.primary,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Use Start Menu Search',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    'Search for "Interview Hammer" or use desktop shortcut',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
  }

  String _getRestoreInstructions() {
    return kIsNativeMacOS
        ? 'search for it in Spotlight or Launchpad:'
        : 'search for it in the Start Menu:';
  }

  void _saveDontShowAgainPreference() async {
    if (_dontShowAgain) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(KEY_DONT_SHOW_TRAY_HIDE_INFO, true);
    }
  }

  @override
  void dispose() {
    _saveDontShowAgainPreference();
    super.dispose();
  }
}

/// Shows the tray hide info dialog if the user hasn't disabled it
Future<void> showTrayHideInfoDialog(BuildContext context) async {
  final prefs = await SharedPreferences.getInstance();
  final dontShow = prefs.getBool(KEY_DONT_SHOW_TRAY_HIDE_INFO) ?? false;

  if (!dontShow && context.mounted) {
    await showDialog<void>(
      context: context,
      builder: (context) => const TrayHideInfoDialog(),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

const Color _primarySeedColor = Color(0xFF5B5CFF);

ThemeData buildLightTheme() {
  return _buildTheme(Brightness.light);
}

ThemeData buildDarkTheme() {
  return _buildTheme(Brightness.dark);
}

// needed just to make the fond more bolder, to match when using GoogleFonts.poppins directly
ThemeData _buildTheme(Brightness brightness) {
  final baseTheme = ThemeData(
    useMaterial3: true,
    brightness: brightness,
    colorScheme: ColorScheme.fromSeed(
      seedColor: _primarySeedColor,
      brightness: brightness,
    ),
  );

  final colorScheme = baseTheme.colorScheme;
  final basicCursor = WidgetStateProperty.all(SystemMouseCursors.basic);

  return baseTheme.copyWith(
    textTheme: _buildTextTheme(baseTheme),
    inputDecorationTheme: _buildInputDecorationTheme(brightness, colorScheme),
    elevatedButtonTheme: _buildElevatedButtonTheme(colorScheme),
    textButtonTheme: TextButtonThemeData(
      style: ButtonStyle(mouseCursor: basicCursor),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: ButtonStyle(mouseCursor: basicCursor),
    ),
    iconButtonTheme: IconButtonThemeData(
      style: ButtonStyle(mouseCursor: basicCursor),
    ),
    switchTheme: SwitchThemeData(mouseCursor: basicCursor),
    floatingActionButtonTheme:
        FloatingActionButtonThemeData(mouseCursor: basicCursor),
    listTileTheme: ListTileThemeData(mouseCursor: basicCursor),
    dropdownMenuTheme: DropdownMenuThemeData(
      menuStyle: MenuStyle(mouseCursor: basicCursor),
    ),
    menuButtonTheme: MenuButtonThemeData(
      style: ButtonStyle(mouseCursor: basicCursor),
    ),
    menuTheme: MenuThemeData(
      style: MenuStyle(mouseCursor: basicCursor),
    ),
    popupMenuTheme: PopupMenuThemeData(mouseCursor: basicCursor),
    radioTheme: RadioThemeData(mouseCursor: basicCursor),
    checkboxTheme: CheckboxThemeData(mouseCursor: basicCursor),
    sliderTheme: SliderThemeData(mouseCursor: basicCursor),
  );
}

TextTheme _buildTextTheme(ThemeData baseTheme) {
  return GoogleFonts.poppinsTextTheme(baseTheme.textTheme).copyWith(
    headlineLarge: GoogleFonts.poppins(
      textStyle: baseTheme.textTheme.headlineLarge,
      fontWeight: FontWeight.w600,
    ),
    headlineMedium: GoogleFonts.poppins(
      textStyle: baseTheme.textTheme.headlineMedium,
      fontWeight: FontWeight.w600,
    ),
    headlineSmall: GoogleFonts.poppins(
      textStyle: baseTheme.textTheme.headlineSmall,
      fontWeight: FontWeight.w600,
    ),
    titleLarge: GoogleFonts.poppins(
      textStyle: baseTheme.textTheme.titleLarge,
      fontWeight: FontWeight.w600,
    ),
    titleMedium: GoogleFonts.poppins(
      textStyle: baseTheme.textTheme.titleMedium,
      fontWeight: FontWeight.w500,
    ),
    bodyLarge: GoogleFonts.poppins(
      textStyle: baseTheme.textTheme.bodyLarge,
      fontWeight: FontWeight.w400,
    ),
  );
}

InputDecorationTheme _buildInputDecorationTheme(
    Brightness brightness, ColorScheme colorScheme) {
  return InputDecorationTheme(
    filled: true,
    fillColor: brightness == Brightness.light
        ? colorScheme.surface.withValues(alpha: 0.95)
        : colorScheme.surfaceContainerHighest.withValues(alpha: 0.25),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(
        color: colorScheme.outline.withValues(alpha: 0.2),
        width: 1,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(
        color: colorScheme.primary,
        width: 1.5,
      ),
    ),
  );
}

ElevatedButtonThemeData _buildElevatedButtonTheme(ColorScheme colorScheme) {
  return ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(25),
      ),
      elevation: 2,
      shadowColor: Colors.black.withValues(alpha: 0.3),
    ).copyWith(
      mouseCursor: WidgetStateProperty.all(SystemMouseCursors.basic),
    ),
  );
}

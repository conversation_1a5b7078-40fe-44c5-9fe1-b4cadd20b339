import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/api/server/session_api.dart' as session_api;
import 'package:interview_hammer/api/storage/image_storage_service.dart';
import 'package:interview_hammer/model/server/session.dart';
import 'package:interview_hammer/screens/helper/screenshot_processor.dart';
import 'package:interview_hammer/services/system_tray/system_tray_service.dart';
import 'package:interview_hammer/utils/events/events.dart';

class SessionManager extends ChangeNotifier {
  static final SessionManager _instance = SessionManager._internal();

  static SessionManager get instance => _instance;

  SessionManager._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;

  Session? _activeServerSession;
  Session? _activeClientSession;

  StreamSubscription<Session>? _serverCommandSubscription;

  DateTime? _sessionStartTime;
  Timer? _sessionTimer;
  final ValueNotifier<String> sessionDurationNotifier =
      ValueNotifier<String>('00:00');

  ScreenshotProcessor? _screenshotProcessor;

  bool _isProcessingCommand = false;
  final StreamController<CommandType> _pendingCommandController =
      StreamController<CommandType>.broadcast();

  // Getters
  Session? get activeServerSession => _activeServerSession;

  Session? get activeClientSession => _activeClientSession;

  bool get isServerSessionActive => _activeServerSession != null;

  String get sessionDuration => sessionDurationNotifier.value;

  /// Stream of pending commands that require UI interaction
  Stream<CommandType> get pendingCommands => _pendingCommandController.stream;

  String? get _userId => _auth.currentUser?.uid;

  //region SESSION LIFECYCLE MANAGEMENT
  /// Creates a new server session and initializes necessary components
  Future<void> createServerSession() async {
    if (_userId == null) {
      throw Exception('User must be logged in to create a session');
    }

    _activeServerSession = await session_api.createSession();

    // Set session start time and begin tracking duration
    _sessionStartTime = _activeServerSession!.sessionStartTime.toDate();
    _startSessionTimer();

    // Initialize screenshot processor early to handle any permissions
    try {
      await initializeScreenshotProcessor();
    } catch (e) {
      d('Failed to initialize screenshot processor: $e');
      // Don't throw here - we can try again when actually needed
    }

    // Set up listener for commands on this session
    if (_activeServerSession != null) {
      _serverCommandSubscription?.cancel();
      _serverCommandSubscription = session_api
          .listenToSessionCommands(_activeServerSession!.sessionId)
          .listen(_handleSessionCommandUpdates);
    }

    notifyListeners();
  }

  /// Ends the current server session and performs cleanup
  Future<void> endServerSession() async {
    if (_activeServerSession == null) return;

    await session_api.endSession(_activeServerSession!.sessionId);
    _serverCommandSubscription?.cancel();
    _serverCommandSubscription = null;
    _activeServerSession = null;

    // Stop the timer when session ends
    _stopSessionTimer();

    // Clean up screenshot processor if exists
    if (_screenshotProcessor != null) {
      _screenshotProcessor?.dispose();
      _screenshotProcessor = null;
    }

    notifyListeners();
  }

  /// Join an existing session as client
  Future<void> joinSession(String sessionId) async {
    _activeClientSession =
        (await session_api.listenToSessionCommands(sessionId).first);

    // Update session status to ACTIVE if it was in WAITING
    if (_activeClientSession?.status == SessionStatus.WAITING) {
      await session_api.updateSessionStatus(sessionId, SessionStatus.ACTIVE);
    }

    // Always hide the desktop app
    await session_api.setCommand(sessionId, CommandType.HIDE_APPLICATION);

    notifyListeners();
  }

  /// End client session
  Future<void> endClientSession({String? sessionId}) async {
    if (sessionId != null) {
      await session_api.endSession(sessionId);
    } else if (_activeClientSession != null) {
      await session_api.endSession(_activeClientSession!.sessionId);
      _activeClientSession = null;
    }

    notifyListeners();
  }

  //endregion

  //region SESSION TIMING MANAGEMENT

  /// Start session timer for tracking session duration
  void _startSessionTimer() {
    _stopSessionTimer(); // Ensure any existing timer is stopped

    _sessionTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (_sessionStartTime != null) {
        final duration = DateTime.now().difference(_sessionStartTime!);
        final minutes = duration.inMinutes.toString().padLeft(2, '0');
        final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
        sessionDurationNotifier.value = '$minutes:$seconds';
      }
    });
  }

  /// Stop session timer
  void _stopSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = null;
    sessionDurationNotifier.value = '00:00';
  }

  //endregion

  //region COMMAND PROCESSING
  /// Handle command updates on server
  Future<void> _handleSessionCommandUpdates(Session updatedSession) async {
    _activeServerSession = updatedSession;

    // Process commands if they are pending
    if (updatedSession.commandStatus == CommandStatus.PENDING) {
      switch (updatedSession.currentCommand) {
        case CommandType.TAKE_SCREENSHOT:
          _handleTakeScreenshotCommand();
          break;
        case CommandType.HIDE_APPLICATION:
          await session_api.updateCommandStatus(
              _activeServerSession!.sessionId, CommandStatus.COMPLETED);

          if (!SystemTrayService.instance.isHidden) {
            _pendingCommandController.add(CommandType.HIDE_APPLICATION);
          }
          break;
        case CommandType.IDLE:
          // No action needed
          break;
      }
    }

    notifyListeners();
  }

  /// Hide application to system tray
  Future<void> hideApplication() async {
    if (_activeServerSession == null) return;

    try {
      await session_api.updateCommandStatus(
          _activeServerSession!.sessionId, CommandStatus.COMPLETED);

      // Hide to system tray
      await SystemTrayService.instance.hideToTray();
    } catch (e) {
      errorToast('Failed to hide application: $e');
      logError(e);

      if (_activeServerSession != null) {
        await session_api.updateCommandStatus(
            _activeServerSession!.sessionId, CommandStatus.FAILED);
      }
    }
  }

  //endregion

  //region SCREENSHOT OPERATIONS
  /// Initialize screenshot processor if not already initialized
  Future<void> initializeScreenshotProcessor() async {
    _screenshotProcessor ??= ScreenshotProcessor();
    try {
      await _screenshotProcessor!.init();
    } on ScreenshotInitializationException catch (e) {
      d('Failed to initialize screen capture: ${e.message}');
      throw ScreenshotInitializationException(e.message);
    } catch (e) {
      d('An unexpected error occurred initializing screenshot processor: $e');
      throw Exception('Failed to initialize screenshot processor: $e');
    }
  }

  /// Handle take screenshot command
  Future<void> _handleTakeScreenshotCommand() async {
    if (_isProcessingCommand || _activeServerSession == null) return;

    _isProcessingCommand = true;
    notifyListeners();

    try {
      if (_screenshotProcessor == null) {
        await initializeScreenshotProcessor();
      }

      final screenshot = await _screenshotProcessor!.takeAndProcessScreenshot();

      if (screenshot == null) {
        await session_api.updateCommandStatus(
            _activeServerSession!.sessionId, CommandStatus.FAILED);
        return;
      }

      await takeAndUploadScreenshot(screenshot.bytes);
    } catch (e) {
      errorToast('Error taking screenshot: $e');

      if (_activeServerSession != null) {
        await session_api.updateCommandStatus(
            _activeServerSession!.sessionId, CommandStatus.FAILED);
      }
    } finally {
      _isProcessingCommand = false;
      notifyListeners();
    }
  }

  /// Take a screenshot and upload it
  Future<void> takeAndUploadScreenshot(Uint8List screenshotBytes) async {
    if (_activeServerSession == null || _userId == null) return;
    final sessionId = _activeServerSession!.sessionId;

    try {
      final url =
          await ImageStorageService.instance.uploadScreenshot(screenshotBytes);

      if (url == null) {
        throw Exception('Failed to upload screenshot');
      }

      await session_api.addScreenshotToSession(sessionId, url);
      await session_api.updateCommandStatus(sessionId, CommandStatus.COMPLETED);
      successToast('Screenshot captured and uploaded successfully');
    } catch (e) {
      d('Failed to take and upload screenshot: $e');

      if (_activeServerSession != null) {
        await session_api.updateCommandStatus(sessionId, CommandStatus.FAILED);
      }
      rethrow; // Rethrow so the calling method can handle appropriately
    }
  }

  //endregion

  @override
  void dispose() {
    _pendingCommandController.close();
    sessionDurationNotifier.dispose();
    super.dispose();
  }
}

import 'dart:async';
import 'dart:ui';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_acrylic/flutter_acrylic.dart';
import 'package:interview_hammer/screens/display_settings_screen.dart';
import 'package:interview_hammer/services/system_tray/system_tray_service.dart';
import 'package:interview_hammer/utils/events/events.dart';
import 'package:interview_hammer/utils/platform_utils.dart';
import 'package:interview_hammer/utils/window_settings_utils.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:screen_retriever/screen_retriever.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:window_manager/window_manager.dart';

import 'app.dart';
import 'firebase_options.dart';

const appName = "InterviewHammer";

Future<void> main() async {
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    await initializeDateFormatting();

    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    if (!kIsWeb) {
      // Initialize Crashlytics only for non-web platforms using unified helper
      FlutterError.onError = (errorDetails) {
        logError(errorDetails.exception, errorDetails.stack);
      };

      // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework
      PlatformDispatcher.instance.onError = (error, stack) {
        logError(error, stack);
        return true;
      };
    }

    await SentryFlutter.init(
      (options) {
        const dsn =
            'https://<EMAIL>/4507676295168000';
        options.dsn = kDebugMode ? '' : dsn;
        options.debug = false;
      },
    );

    await _setupDesktopOptions();

    runApp(const MyApp());
  }, (error, stack) {
    // This is your error handler for any errors not caught by other error handlers
    logError(error, stack);

    print('Uncaught error: $error');
    Sentry.captureException(error, stackTrace: stack);
  });
}

Future<void> _setupDesktopOptions() async {
  if (!isDesktopPlatform()) return;

  await SystemTrayService.instance.initialize();

  final prefs = await SharedPreferences.getInstance();
  final opacity = prefs.getDouble(KEY_WINDOW_OPACITY) ?? DEFAULT_WINDOW_OPACITY;
  final alwaysOnTop =
      prefs.getBool(KEY_WINDOW_ALWAYS_ON_TOP) ?? DEFAULT_ALWAYS_ON_TOP;
  final hideAppIcon =
      prefs.getBool(KEY_WINDOW_HIDE_APP_ICON) ?? DEFAULT_HIDE_APP_ICON;
  final hideFromScreenSharing =
      prefs.getBool(KEY_WINDOW_HIDE_FROM_SCREEN_SHARING) ??
          DEFAULT_HIDE_FROM_SCREEN_SHARING;
  final isVisibleOnAllWorkspaces =
      prefs.getBool(KEY_WINDOW_VISIBLE_ALL_WORKSPACES) ??
          DEFAULT_VISIBLE_ALL_WORKSPACES;
  final appName = prefs.getString(KEY_APP_NAME) ?? DEFAULT_APP_NAME;

  if (isElectron()) {
    Future.delayed(const Duration(milliseconds: 100), () async {
      await applyOpacitySetting(opacity);
      await applyAlwaysOnTopSetting(alwaysOnTop);
      await applySkipTaskbarSetting(hideAppIcon);
      await applyHideFromScreenSharingSetting(hideFromScreenSharing);
      await applyVisibleOnAllWorkspacesSetting(isVisibleOnAllWorkspaces);
      await applyAppNameSetting(appName);
    });
  } else if (kIsNativeDesktop) {
    await Window.initialize();
    await Window.setEffect(effect: WindowEffect.transparent);
    await applyOpacitySetting(opacity);

    await windowManager.ensureInitialized();

    final screen = await screenRetriever.getPrimaryDisplay();
    final screenHeight = screen.size.height;

    WindowOptions windowOptions = WindowOptions(
      size: Size(500, screenHeight * 0.8),
      alwaysOnTop: alwaysOnTop,
      skipTaskbar: hideAppIcon,
    );

    windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();

      await applyHideFromScreenSharingSetting(hideFromScreenSharing);
      await applyVisibleOnAllWorkspacesSetting(isVisibleOnAllWorkspaces);
      await applyAppNameSetting(appName);
    });
  }
}

import 'dart:async';

import 'screenshot_types.dart';

/// Abstract base class defining the contract for web-based screenshot services.
///
/// This allows for platform-specific implementations (web/JS vs. native stubs).
abstract class BaseWebScreenshotService {
  /// Returns true if the service can be used on the current platform (e.g., web).
  bool get isSupported;

  /// Checks if the code is running within the Electron environment.
  bool get isElectron;

  /// Checks if screen capture is currently active.
  bool get isCapturing;

  /// Initializes the screenshot service.
  ///
  /// May require user interaction (permissions) on some platforms.
  /// Throws [ScreenshotException] on failure.
  Future<bool> initialize();

  /// Takes a screenshot.
  ///
  /// Returns a [WebScreenshotResult] containing the image data and dimensions.
  /// Throws [ScreenshotException] if not supported, permissions denied, or capture fails.
  Future<WebScreenshotResult> takeScreenshot();

  /// Formats error messages related to screenshot operations for user-friendly display.
  String getErrorMessage(Exception e);

  /// Releases resources used by the screenshot service (e.g., stops capture stream).
  void dispose();
}

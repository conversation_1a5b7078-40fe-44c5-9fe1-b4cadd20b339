import 'dart:typed_data';

/// Simple Dart class to hold the processed screenshot data from the web service.
class WebScreenshotResult {
  final Uint8List bytes;
  final int width;
  final int height;

  WebScreenshotResult(
      {required this.bytes, required this.width, required this.height});
}

/// Exception thrown when screenshot operations fail
class ScreenshotException implements Exception {
  final String message;

  ScreenshotException(this.message);

  @override
  String toString() => 'ScreenshotException: $message';
}

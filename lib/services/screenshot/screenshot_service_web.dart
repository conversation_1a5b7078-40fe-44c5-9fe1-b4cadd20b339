import 'dart:async';
import 'dart:js_interop';

import 'package:flutter/foundation.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';

import '../electron_interaction/electron_interaction_service.dart';
import 'base_web_screenshot_service.dart';
import 'screenshot_types.dart';

// Web flow functions
@JS('startScreenCapture')
external JSPromise<JSBoolean> _startScreenCaptureJS();

@JS('takeWebScreenshot')
external JSPromise<JSResizeResult> _takeWebScreenshotJS();

@JS('stopScreenCapture')
external bool _stopScreenCaptureJS();

@JS('isScreenCaptureActive')
external bool _isScreenCaptureActiveJS();

// Electron flow function
@JS('takeDirectElectronScreenshot')
external JSPromise<JSResizeResult> _takeDirectElectronScreenshotJS();

/// JS Interop type for the result returned by the modified screenshot functions.
@JS()
@anonymous
extension type JSResizeResult._(JSObject _) implements JSObject {
  external JSArrayBuffer get buffer;

  external JSNumber get width;

  external JSNumber get height;
}

/// Web/Electron implementation of the screenshot service.
///
/// Uses JS interop to interact with browser or Electron APIs.
class ScreenshotService implements BaseWebScreenshotService {
  bool _isInitialized = false;
  bool _isCapturing = false;

  @override
  bool get isElectron => getElectronInteractionService().isElectron;

  @override
  bool get isCapturing {
    // kIsWeb check is crucial
    if (!kIsWeb) return false;
    if (isElectron) return _isCapturing;

    try {
      // Use JS function to check the actual stream state
      return _isScreenCaptureActiveJS();
    } catch (e) {
      return false;
    }
  }

  @override
  bool get isSupported => kIsWeb;

  @override
  Future<bool> initialize() async {
    if (!kIsWeb) {
      throw ScreenshotException(
          'Screen capture is only supported on web platforms');
    }

    try {
      if (isElectron) {
        // In Electron, we don't need pre-initialization, just mark as ready
        _isInitialized = true;
        _isCapturing = true; // Conceptually ready to take screenshots
        return true;
      } else {
        // In web, we need to start the capture stream
        final JSPromise<JSBoolean> promise = _startScreenCaptureJS();
        final JSBoolean result = await promise.toDart;
        final bool success = result.toDart;
        _isInitialized = success;
        _isCapturing = success;
        return success;
      }
    } catch (e) {
      _isInitialized = false;
      _isCapturing = false;
      throw ScreenshotException(
          'Failed to initialize screen capture: ${e.toString()}');
    }
  }

  @override
  Future<WebScreenshotResult> takeScreenshot() async {
    if (!kIsWeb) {
      throw ScreenshotException(
          'Screen capture is only supported on web platforms');
    }

    // If not initialized, try to initialize first (which handles permissions)
    if (!_isInitialized || (!isElectron && !isCapturing)) {
      bool success = await initialize();
      if (!success) {
        throw ScreenshotException(
            'Failed to initialize screen capture. User permission may be required.');
      }
    }

    try {
      JSResizeResult jsResult;

      if (isElectron) {
        // Use Electron direct screenshot method which now returns JSResizeResult
        jsResult = await _takeDirectElectronScreenshotJS().toDart;
      } else {
        // Use web capture method which now returns JSResizeResult
        if (!isCapturing) {
          throw ScreenshotException('Web screen capture is not active');
        }
        jsResult = await _takeWebScreenshotJS().toDart;
      }

      // Extract data from the JSResizeResult
      final int width = jsResult.width.toDartInt;
      final int height = jsResult.height.toDartInt;
      final Uint8List bytes = jsResult.buffer.toDart
          .asUint8List(); // Convert ArrayBuffer to Uint8List

      if (bytes.isEmpty || width <= 0 || height <= 0) {
        throw ScreenshotException(
            'Invalid screenshot data received (empty bytes or zero dimensions)');
      }

      return WebScreenshotResult(bytes: bytes, width: width, height: height);
    } catch (e) {
      if (e is ScreenshotException) {
        rethrow; // Rethrow specific ScreenshotExceptions
      }
      d('takeScreenshot failed. Raw error: $e');
      d('Stack trace: ${StackTrace.current}');
      throw ScreenshotException('Failed to take screenshot: ${e.toString()}');
    }
  }

  @override
  String getErrorMessage(Exception e) {
    final message = e.toString().toLowerCase();

    if (message.contains('permission')) {
      return "Please allow screen capture permission when prompted.";
    } else if (!kIsWeb) {
      return "Screenshots not supported on this platform.";
    } else if (message.contains('not active') ||
        message.contains('not initialized')) {
      return "Screen capture needs to be initialized first.";
    }

    // Default general error message
    return "Failed to take screenshot: ${e.toString()}";
  }

  @override
  void dispose() {
    if (!kIsWeb) return;

    if (!isElectron) {
      try {
        _stopScreenCaptureJS();
      } catch (_) {
        // Ignore errors during disposal
      }
    }

    _isInitialized = false;
    _isCapturing = false;
  }
}

// Conditional export based on the presence of dart:html
// This file acts as a locator, providing the correct implementation
// for the screenshot service based on the current platform.

import 'base_web_screenshot_service.dart';
import 'screenshot_service_native.dart'
    if (dart.library.html) 'screenshot_service_web.dart';

// Re-export the base class and types so consumers only need to import this file
export 'base_web_screenshot_service.dart';
export 'screenshot_types.dart';

// Private instance variable to hold the singleton
BaseWebScreenshotService? _instance;

/// Factory function to get the singleton instance of [BaseScreenshotService].
BaseWebScreenshotService getWebScreenshotService() {
  // Create instance on first call, return existing instance otherwise
  _instance ??=
      ScreenshotService(); // Directly instantiate the conditionally imported class
  return _instance!;
}

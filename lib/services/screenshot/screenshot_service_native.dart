import 'dart:async';

import 'base_web_screenshot_service.dart';
import 'screenshot_types.dart';

/// Native stub implementation for the screenshot service.
///
/// This implementation is used on non-web platforms (macOS, Windows, Linux, iOS, Android).
/// Screenshots are not supported, so methods return default values or throw exceptions.
class ScreenshotService implements BaseWebScreenshotService {
  @override
  bool get isSupported =>
      false; // Screenshots not supported via this service on native

  @override
  bool get isElectron => false; // Not running in Electron

  @override
  bool get isCapturing => false; // No capture stream on native

  @override
  Future<bool> initialize() async {
    // No initialization needed or possible for this stub
    return false;
  }

  @override
  Future<WebScreenshotResult> takeScreenshot() async {
    // Indicate that the operation is not supported on this platform
    throw ScreenshotException(
        'Screenshots are not supported on this platform.');
  }

  @override
  String getErrorMessage(Exception e) {
    // Provide a consistent error message for native platforms
    if (e is ScreenshotException) {
      return e.message; // Use the specific message if available
    }
    return "Screenshots are not supported on this platform.";
  }

  @override
  void dispose() {
    // No resources to release for the native stub
  }
}

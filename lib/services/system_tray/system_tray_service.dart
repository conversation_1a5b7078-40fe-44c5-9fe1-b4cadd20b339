import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:interview_hammer/providers/session_manager.dart';
import 'package:interview_hammer/services/electron_interaction/electron_interaction_service.dart';
import 'package:interview_hammer/utils/platform_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tray_manager/tray_manager.dart';
import 'package:window_manager/window_manager.dart';

import '../../screens/display_settings_screen.dart';
import '../../utils/events/events.dart';

/// Service for managing system tray functionality
class SystemTrayService {
  static final SystemTrayService _instance = SystemTrayService._internal();

  static SystemTrayService get instance => _instance;

  // Menu item labels as constants for consistency
  static const String _showAppLabel = 'Show App';
  static const String _hideTrayLabel = 'Hide Tray Icon';
  static const String _endSessionLabel = 'End Session';
  static const String _exitLabel = 'Exit';

  SystemTrayService._internal();

  final _electronService = getElectronInteractionService();
  final _sessionManager = SessionManager.instance;
  bool _isInitialized = false;
  bool _isHidden = false;
  bool _isTrayHidden = false;

  bool get isHidden => _isHidden;

  bool get isTrayHidden => _isTrayHidden;

  // Store window position and size before hiding
  Rect? _savedWindowRect;

  // Store reference to the tray handler for cleanup
  _TrayHandler? _trayHandler;

  Future<void> initialize() async {
    if (_isInitialized) return;

    final prefs = await SharedPreferences.getInstance();
    _isTrayHidden = prefs.getBool(KEY_HIDE_SYSTEM_TRAY) ?? false;

    try {
      if (isElectron()) {
        // Initialize tray only if it should be visible
        if (!_isTrayHidden) {
          await _initElectronTray();
        }

        // Set up listeners regardless so we can respond when tray is eventually shown
        _setupElectronTrayListeners();
      } else if (kIsNativeDesktop) {
        // Initialize tray only if it should be visible
        if (!_isTrayHidden) {
          await _initNativeTray();
        }

        // Listen for window events in native desktop
        windowManager.addListener(_WindowEventListener(this));
      }

      // Listen for session changes to update the tray menu
      _sessionManager.addListener(_refreshTrayMenu);

      // Update session state initially
      _refreshTrayMenu();

      _isInitialized = true;
    } catch (e, stack) {
      d('Failed to initialize system tray: $e');
      logError(e, stack);
    }
  }

  /// Hide the application to system tray
  Future<void> hideToTray() async {
    await initialize();

    try {
      // Save current window position and size
      if (kIsNativeDesktop) {
        _savedWindowRect = await windowManager.getBounds();
      }

      if (isElectron()) {
        await _electronService.hideWindow();
      } else if (kIsNativeDesktop) {
        await windowManager.hide();
      }

      _isHidden = true;
    } catch (e, stack) {
      d('Failed to hide application to system tray: $e');
      logError(e, stack);
      throw Exception('Failed to hide application to system tray');
    }
  }

  /// Show the window from system tray
  Future<void> showFromTray() async {
    await initialize();

    try {
      if (isElectron()) {
        await _electronService.showWindow();
      } else if (kIsNativeDesktop) {
        await windowManager.show();
        await windowManager.focus();

        // Restore window position and size if saved
        if (_savedWindowRect != null) {
          await windowManager.setBounds(_savedWindowRect!);
        }
      }

      _isHidden = false;
    } catch (e, stack) {
      d('Failed to show application from system tray: $e');
      logError(e, stack);
      throw Exception('Failed to show application from system tray');
    }
  }

  /// Update system tray menu when session state changes
  Future<void> _refreshTrayMenu() async {
    if (isElectron()) {
      await _electronService
          .updateSessionState(_sessionManager.isServerSessionActive);
    } else if (kIsNativeDesktop) {
      await _updateNativeTrayMenu();
    }
  }

  /// Hide the system tray icon completely
  Future<void> hideTrayIcon() async {
    if (_isTrayHidden) return;

    try {
      _isTrayHidden = true;

      if (isElectron()) {
        await _electronService.hideTrayIcon();
      } else if (kIsNativeDesktop) {
        if (_trayHandler != null) {
          trayManager.removeListener(_trayHandler!);
        }
        await trayManager.destroy();
        _trayHandler = null;
      }

      // Save tray icon visibility state to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(KEY_HIDE_SYSTEM_TRAY, true);

      d('System tray icon hidden');
    } catch (e, stack) {
      d('Failed to hide system tray icon: $e');
      logError(e, stack);
      _isTrayHidden = false; // Reset state on failure
      throw Exception('Failed to hide system tray icon');
    }
  }

  /// Show the system tray icon
  Future<void> showTrayIcon() async {
    if (!_isTrayHidden) return;

    try {
      _isTrayHidden = false;

      if (isElectron()) {
        await _electronService.showTrayIcon();
      } else if (kIsNativeDesktop) {
        await _initNativeTray();
      }

      // Save tray icon visibility state to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(KEY_HIDE_SYSTEM_TRAY, false);

      d('System tray icon shown');
    } catch (e, stack) {
      d('Failed to show system tray icon: $e');
      logError(e, stack);
      _isTrayHidden = true; // Reset state on failure
      throw Exception('Failed to show system tray icon');
    }
  }

  /// Initialize the system tray for native desktop platforms
  Future<void> _initNativeTray() async {
    await trayManager.setIcon('assets/icons/tray_icon.png');

    // Create the context menu
    await _updateNativeTrayMenu();

    // Set up event listeners
    _trayHandler = _TrayHandler(this);
    trayManager.addListener(_trayHandler!);
  }

  /// Update the native tray menu
  Future<void> _updateNativeTrayMenu() async {
    await trayManager.setContextMenu(
      Menu(
        items: [
          MenuItem(label: _showAppLabel),
          MenuItem.separator(),
          MenuItem(label: _hideTrayLabel),
          if (_sessionManager.isServerSessionActive) ...[
            MenuItem.separator(),
            MenuItem(label: _endSessionLabel),
          ],
          MenuItem.separator(),
          MenuItem(label: _exitLabel),
        ],
      ),
    );
  }

  /// Handle ending the active session
  Future<void> _handleEndSession() async {
    if (_sessionManager.isServerSessionActive) {
      await _sessionManager.endServerSession();
      d('Session ended from tray menu');

      // Update the tray menu after ending the session
      await _refreshTrayMenu();
    }
  }

  /// Initialize the system tray for Electron
  Future<void> _initElectronTray() async {
    await _electronService.initSystemTray();
    d('Electron tray initialization requested');
  }

  /// Set up listeners for Electron IPC events
  void _setupElectronTrayListeners() {
    _electronService.setEndSessionFromTrayListener(() {
      _handleEndSession();
    });

    _electronService.setHideTrayIconListener(() {
      _handleHideTrayIconFromMenu();
    });
  }

  /// Handle hiding tray icon from the system tray menu
  Future<void> _handleHideTrayIconFromMenu() async {
    try {
      _isTrayHidden = true;

      // Save tray icon visibility state to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(KEY_HIDE_SYSTEM_TRAY, true);

      d('System tray icon hidden from tray menu');
    } catch (e, stack) {
      d('Failed to handle hide tray icon from menu: $e');
      logError(e, stack);
      _isTrayHidden = false; // Reset state on failure
    }
  }

  /// Dispose the system tray service
  Future<void> dispose() async {
    if (!_isInitialized) return;

    try {
      _sessionManager.removeListener(_refreshTrayMenu);

      if (!isElectron() && kIsNativeDesktop) {
        if (_trayHandler != null) {
          trayManager.removeListener(_trayHandler!);
        }
        await trayManager.destroy();
      }

      _isInitialized = false;
    } catch (e, stack) {
      d('Failed to dispose system tray service: $e');
      logError(e, stack);
    }
  }
}

/// Internal class to handle window events from window_manager
class _WindowEventListener extends WindowListener {
  final SystemTrayService _systemTrayService;

  _WindowEventListener(this._systemTrayService);

  @override
  void onWindowMinimize() async {
    try {
      // Save current window position and size if not already saved
      if (!_systemTrayService.isHidden) {
        _systemTrayService._savedWindowRect = await windowManager.getBounds();
        _systemTrayService._isHidden = true;
      }
    } catch (e, stack) {
      d('Failed to handle window minimize event: $e');
      logError(e, stack);
    }
  }
}

/// Internal class to handle tray events
class _TrayHandler implements TrayListener {
  final SystemTrayService _service;

  _TrayHandler(this._service);

  @override
  void onTrayIconMouseDown() {
    trayManager.popUpContextMenu();
  }

  @override
  void onTrayIconRightMouseDown() {
    trayManager.popUpContextMenu();
  }

  @override
  void onTrayMenuItemClick(MenuItem menuItem) async {
    d('Menu item clicked: ${menuItem.label}');

    try {
      if (menuItem.label == SystemTrayService._showAppLabel) {
        await _service.showFromTray();
      } else if (menuItem.label == SystemTrayService._hideTrayLabel) {
        await _service.hideTrayIcon();
      } else if (menuItem.label == SystemTrayService._endSessionLabel) {
        await _service._handleEndSession();
      } else if (menuItem.label == SystemTrayService._exitLabel) {
        exit(0);
      }
    } catch (e, stack) {
      d('Error handling tray menu item click: ${e.toString()}');
      logError(e, stack);
    }
  }

  @override
  void onTrayIconMouseUp() {}

  @override
  void onTrayIconRightMouseUp() {}
}

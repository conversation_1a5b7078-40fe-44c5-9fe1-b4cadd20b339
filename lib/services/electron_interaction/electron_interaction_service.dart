// Conditional export based on the presence of dart:html
// This file acts as a locator, providing the correct implementation
// for the current platform (web/Electron vs. native).

import 'base_electron_interaction_service.dart';
import 'electron_interaction_service_native.dart'
    if (dart.library.html) 'electron_interaction_service_web.dart';

// Re-export the base class so consumers only need to import this file
export 'base_electron_interaction_service.dart';

// Private instance variable to hold the singleton
BaseElectronInteractionService? _instance;

/// Factory function to get the singleton instance of [BaseElectronInteractionService].
BaseElectronInteractionService getElectronInteractionService() {
  // Create instance on first call, return existing instance otherwise
  _instance ??= ElectronInteractionService();
  return _instance!;
}

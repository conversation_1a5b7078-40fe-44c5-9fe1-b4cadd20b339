import 'dart:js_interop';

import 'package:flutter/foundation.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';

import 'base_electron_interaction_service.dart';

// Check for the existence of a specific function exposed by the preload script
// to determine if we are in the correct Electron environment.
@JS('window.electronAPI.getSources')
external JSFunction? get _electronGetSourcesFn;

@JS('window.electronAPI.setOpacity')
external void _setElectronOpacityJS(double value);

@JS('window.electronAPI.setAlwaysOnTop')
external void _setElectronAlwaysOnTopJS(bool value);

@JS('window.electronAPI.setSkipTaskbar')
external void _setElectronSkipTaskbarJS(bool value);

@JS('window.electronAPI.setVisibleOnAllWorkspaces')
external void _setElectronVisibleOnAllWorkspacesJS(bool value);

@JS('window.electronAPI.setContentProtection')
external void _setElectronContentProtectionJS(bool value);

@JS('window.electronAPI.hideWindow')
external void _hideElectronWindowJS();

@JS('window.electronAPI.showWindow')
external void _showElectronWindowJS();

@JS('window.electronAPI.initSystemTray')
external void _initElectronSystemTrayJS();

@JS('window.electronAPI.updateSessionState')
external void _updateSessionStateJS(bool isActive);

@JS('window.electronAPI.onEndSessionFromTray')
external void _onEndSessionFromTrayJS(JSFunction callback);

@JS('window.electronAPI.onHideTrayIcon')
external void _onHideTrayIconJS(JSFunction callback);

@JS('window.electronAPI.hideTrayIcon')
external void _hideTrayIconJS();

@JS('window.electronAPI.showTrayIcon')
external void _showTrayIconJS();

/// Web/Electron implementation of the Electron interaction service.
///
/// Uses JS interop to communicate with Electron's preload script.
class ElectronInteractionService implements BaseElectronInteractionService {
  @override
  bool get isElectron {
    // kIsWeb check is crucial for conditional import logic to work correctly
    // and prevent this code from being compiled on non-web platforms.
    if (!kIsWeb) return false;
    try {
      // If _electronGetSourcesFn exists (is not null), we are in Electron.
      // Accessing it will throw if window.electronAPI is undefined, which is caught below.
      return _electronGetSourcesFn != null;
    } catch (e) {
      // If the function doesn't exist or throws, assume not Electron
      return false;
    }
  }

  @override
  Future<void> setOpacity(double value) async {
    if (!isElectron) return;
    try {
      _setElectronOpacityJS(value);
    } catch (e) {
      d('Failed to set Electron opacity: $e');
    }
  }

  @override
  Future<void> setAlwaysOnTop(bool value) async {
    if (!isElectron) return;
    try {
      _setElectronAlwaysOnTopJS(value);
    } catch (e) {
      d('Failed to set Electron alwaysOnTop: $e');
    }
  }

  @override
  Future<void> setSkipTaskbar(bool value) async {
    if (!isElectron) return;
    try {
      _setElectronSkipTaskbarJS(value);
    } catch (e) {
      d('Failed to set Electron skipTaskbar: $e');
    }
  }

  @override
  Future<void> setVisibleOnAllWorkspaces(bool value) async {
    if (!isElectron) return;
    try {
      _setElectronVisibleOnAllWorkspacesJS(value);
    } catch (e) {
      d('Failed to set Electron visibleOnAllWorkspaces: $e');
    }
  }

  @override
  Future<void> setContentProtection(bool value) async {
    if (!isElectron) return;
    try {
      _setElectronContentProtectionJS(value);
    } catch (e) {
      d('Failed to set Electron contentProtection: $e');
    }
  }

  @override
  Future<void> hideWindow() async {
    if (!isElectron) return;
    try {
      _hideElectronWindowJS();
    } catch (e) {
      d('Failed to hide Electron window: $e');
    }
  }

  @override
  Future<void> showWindow() async {
    if (!isElectron) return;
    try {
      _showElectronWindowJS();
    } catch (e) {
      d('Failed to show Electron window: $e');
    }
  }

  @override
  Future<void> initSystemTray() async {
    if (!isElectron) return;
    try {
      _initElectronSystemTrayJS();
    } catch (e) {
      d('Failed to initialize Electron system tray: $e');
    }
  }

  @override
  Future<void> updateSessionState(bool hasActiveSession) async {
    if (!isElectron) return;
    try {
      _updateSessionStateJS(hasActiveSession);
    } catch (e) {
      d('Failed to update Electron session state: $e');
    }
  }

  @override
  Future<void> setEndSessionFromTrayListener(void Function() callback) async {
    if (!isElectron) return;

    void wrapperCallback() {
      callback();
    }

    try {
      final JSFunction jsCallback = wrapperCallback.toJS;
      _onEndSessionFromTrayJS(jsCallback);
    } catch (e) {
      d('Failed to set Electron end session from tray listener: $e');
    }
  }

  @override
  Future<void> setHideTrayIconListener(void Function() callback) async {
    if (!isElectron) return;

    void wrapperCallback() {
      callback();
    }

    try {
      final JSFunction jsCallback = wrapperCallback.toJS;
      _onHideTrayIconJS(jsCallback);
    } catch (e) {
      d('Failed to set Electron hide tray icon listener: $e');
    }
  }

  @override
  Future<void> hideTrayIcon() async {
    if (!isElectron) return;
    try {
      _hideTrayIconJS();
    } catch (e) {
      d('Failed to hide Electron tray icon: $e');
    }
  }

  @override
  Future<void> showTrayIcon() async {
    if (!isElectron) return;
    try {
      _showTrayIconJS();
    } catch (e) {
      d('Failed to show Electron tray icon: $e');
    }
  }
}

import 'base_electron_interaction_service.dart';

/// Native stub implementation for the Electron interaction service.
///
/// This implementation is used on non-web platforms where Electron APIs are not available.
/// It provides default behavior (e.g., `isElectron` is always false) and no-op methods.
class ElectronInteractionService implements BaseElectronInteractionService {
  @override
  bool get isElectron => false; // Not running in Electron on native platforms

  @override
  Future<void> setOpacity(double value) async {
    // No-op on native platforms
  }

  @override
  Future<void> setAlwaysOnTop(bool value) async {
    // No-op on native platforms
  }

  @override
  Future<void> setSkipTaskbar(bool value) async {
    // No-op on native platforms
  }

  @override
  Future<void> setVisibleOnAllWorkspaces(bool value) async {
    // No-op on native platforms
  }

  @override
  Future<void> setContentProtection(bool value) async {
    // No-op on native platforms
  }

  @override
  Future<void> hideWindow() async {
    // No-op on native platforms
  }

  @override
  Future<void> showWindow() async {
    // No-op on native platforms
  }

  @override
  Future<void> initSystemTray() async {
    // No-op on native platforms
  }

  @override
  Future<void> updateSessionState(bool hasActiveSession) async {
    // No-op on native platforms
  }

  @override
  Future<void> setEndSessionFromTrayListener(void Function() callback) async {
    // No-op on native platforms
  }

  @override
  Future<void> setHideTrayIconListener(void Function() callback) async {
    // No-op on native platforms
  }

  @override
  Future<void> hideTrayIcon() async {
    // No-op on native platforms
  }

  @override
  Future<void> showTrayIcon() async {
    // No-op on native platforms
  }

}

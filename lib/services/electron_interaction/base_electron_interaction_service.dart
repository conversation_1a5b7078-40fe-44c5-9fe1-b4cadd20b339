/// Abstract base class defining the contract for Electron interaction services.
///
/// This allows for platform-specific implementations (web/JS vs. native stubs).
abstract class BaseElectronInteractionService {
  /// Checks if the code is running within the Electron environment.
  ///
  /// Returns:
  /// - true if running in Electron
  /// - false otherwise (e.g., native platform, or standard web browser)
  bool get isElectron;

  /// Sets the Electron window opacity.
  ///
  /// Does nothing if not running in Electron.
  Future<void> setOpacity(double value);

  /// Sets whether the Electron window should always be on top.
  ///
  /// Does nothing if not running in Electron.
  Future<void> setAlwaysOnTop(bool value);

  /// Sets whether the Electron window should skip the taskbar/dock.
  ///
  /// Does nothing if not running in Electron.
  Future<void> setSkipTaskbar(bool value);

  /// Sets whether the Electron window should be visible on all workspaces (macOS).
  ///
  /// Does nothing if not running in Electron.
  Future<void> setVisibleOnAllWorkspaces(bool value);

  /// Sets the content protection for the Electron window (prevents screen capture).
  ///
  /// Does nothing if not running in Electron.
  Future<void> setContentProtection(bool value);

  /// Hides the window to system tray.
  ///
  /// Does nothing if not running in Electron.
  Future<void> hideWindow();

  /// Shows the window from system tray.
  Future<void> showWindow();

  /// Initializes the system tray with icon and context menu.
  ///
  /// Does nothing if not running in Electron.
  Future<void> initSystemTray();

  /// Updates the session state for the system tray menu.
  ///
  /// This updates whether the "End Session" option appears in the system tray menu.
  Future<void> updateSessionState(bool hasActiveSession);

  /// Sets the end session listener from the system tray.
  ///
  /// Callback will be called when the "End Session" option is selected in the system tray menu.
  Future<void> setEndSessionFromTrayListener(void Function() callback);

  /// Sets the hide tray icon listener from the system tray.
  ///
  /// Callback will be called when the "Hide Tray Icon" option is selected in the system tray menu.
  Future<void> setHideTrayIconListener(void Function() callback);

  /// Hides the system tray icon completely.
  Future<void> hideTrayIcon();

  /// Shows the system tray icon.
  Future<void> showTrayIcon();
}

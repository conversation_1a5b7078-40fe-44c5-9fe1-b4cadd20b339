import 'package:flutter/material.dart';
import 'package:flutter_toolbox/flutter_toolbox.dart';
import 'package:flutter_toolbox/generated/l10n.dart' as toolbox;
import 'package:interview_hammer/providers/session_manager.dart';
import 'package:interview_hammer/providers/theme_provider.dart';
import 'package:interview_hammer/screens/splash_screen.dart';
import 'package:interview_hammer/styles/theme.dart';
import 'package:interview_hammer/utils/smartlook/smartlook.dart';
import 'package:provider/provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'main.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ToolboxApp(
      toolboxConfig: ToolboxConfig(),
      child: MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => ThemeProvider()),
          ChangeNotifierProvider.value(value: SessionManager.instance),
        ],
        child: Consumer<ThemeProvider>(
          builder: (context, themeProvider, _) {
            return AppSmartlookRecordingWidget(
              child: MaterialApp(
                navigatorObservers: [SentryNavigatorObserver()],
                title: appName,
                debugShowCheckedModeBanner: false,
                theme: buildLightTheme(),
                darkTheme: buildDarkTheme(),
                themeMode: themeProvider.themeMode,
                restorationScopeId: 'app',
                localizationsDelegates: const [toolbox.S.delegate],
                supportedLocales: const [Locale('en', '')],
                onGenerateTitle: (BuildContext context) => "InterviewHammer",
                builder: (context, child) {
                  return child!;
                },
                home: const SplashScreen(),
              ),
            );
          },
        ),
      ),
    );
  }
}

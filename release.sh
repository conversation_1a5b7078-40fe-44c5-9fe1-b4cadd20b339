fvm flutter clean && peanut -b test --web-renderer canvaskit && <NAME_EMAIL>-humazed:humazed/interview_hammer.git test &&
fvm flutter clean && peanut -b gh-pages --web-renderer canvaskit && <NAME_EMAIL>-humazed:humazed/interview_hammer.git gh-pages &&
rm -rf build/macos && # Clean macOS artifacts that crash sentry_dart_plugin
fvm flutter packages pub run sentry_dart_plugin &&
fvm flutter clean && fvm flutter build apk && open build/app/outputs/apk/release

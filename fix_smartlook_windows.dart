import 'dart:io';

void main() async {
  // Set up environment
  final pubCacheDir = Platform.environment['PUB_CACHE'] ??
      'C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache';

  final packageDir =
      Directory('$pubCacheDir\\hosted\\pub.dev\\flutter_smartlook-4.1.25');

  if (!await packageDir.exists()) {
    print('flutter_smartlook package not found at: ${packageDir.path}');
    print(
        'Please run "flutter pub get" first to ensure the package is downloaded.');
    return;
  }

  print('Found flutter_smartlook package at: ${packageDir.path}');
  print('Applying fixes for Windows compatibility...');

  // Apply Canvas fixes
  await _fixCanvasDescriptor(packageDir);

  // Apply Material delegate fixes
  await _fixMaterialDelegate(packageDir);

  print('✅ All fixes applied successfully!');
  print('You can now run: flutter build web --base-href /');
}

Future<void> _fixCanvasDescriptor(Directory packageDir) async {
  final canvasFile = File(
      '${packageDir.path}\\lib\\wireframe\\element_descriptors\\canvas_descriptor.dart');

  if (!await canvasFile.exists()) {
    print('Canvas descriptor file not found');
    return;
  }

  String content = await canvasFile.readAsString();

  // Check if already patched
  if (content.contains('clipRSuperellipse') &&
      content.contains('drawRSuperellipse')) {
    print('Canvas descriptor already patched');
    return;
  }

  // Add the missing methods before the closing brace of the class
  final insertionPoint = content.lastIndexOf('}');
  if (insertionPoint == -1) {
    print('Could not find insertion point in canvas descriptor');
    return;
  }

  final newMethods = '''

  @override
  void clipRSuperellipse(RSuperellipse rsuperellipse, {bool doAntiAlias = true}) {
    // No-op for wireframe mode
  }

  @override
  void drawRSuperellipse(RSuperellipse rsuperellipse, Paint paint) {
    addSkeleton(rsuperellipse.outerRect, paint.color);
  }
''';

  final newContent = '${content.substring(0, insertionPoint)}$newMethods\n}';
  await canvasFile.writeAsString(newContent);

  print('✅ Canvas descriptor fixed');
}

Future<void> _fixMaterialDelegate(Directory packageDir) async {
  final delegateFile = File(
      '${packageDir.path}\\lib\\wireframe\\obfuscated_names\\custom_material_delegate.dart');

  if (!await delegateFile.exists()) {
    print('Material delegate file not found');
    return;
  }

  String content = await delegateFile.readAsString();

  // Check if already patched
  if (content.contains('copyButtonLabel') &&
      content.contains('shareButtonLabel')) {
    print('Material delegate already patched');
    return;
  }

  // Add the missing methods before the closing brace of the class
  final insertionPoint = content.lastIndexOf('}');
  if (insertionPoint == -1) {
    print('Could not find insertion point in material delegate');
    return;
  }

  final newMethods = '''

  @override
  String get copyButtonLabel => 'Copy';

  @override
  String get cutButtonLabel => 'Cut';

  @override
  String get lookUpButtonLabel => 'Look up';

  @override
  String get pasteButtonLabel => 'Paste';

  @override
  String get searchWebButtonLabel => 'Search the web';

  @override
  String get selectAllButtonLabel => 'Select all';

  @override
  String get shareButtonLabel => 'Share';
''';

  final newContent = '${content.substring(0, insertionPoint)}$newMethods\n}';
  await delegateFile.writeAsString(newContent);

  print('✅ Material delegate fixed');
}

# interview_hammer

## Package Patching

This project uses `patch_package` to fix compatibility issues with third-party packages. After
running `flutter pub get`, you must apply patches to ensure the project builds correctly:

```bash
dart run patch_package apply
```

### What patches are applied?

- **flutter_smartlook**: Adds missing Canvas methods (`clipRSuperellipse`, `drawRSuperellipse`) and
  WidgetsLocalizations getters for Flutter 3.22+ compatibility.

### Development workflow

1. Run `fvm flutter pub get`
2. Run `dart run patch_package apply`
3. Proceed with normal development

For production builds, the commands below automatically include the patch application step.

---

## Development Commands

Run macOS app (non-store version with app name feature):

```bash
fvm flutter run -d macos --dart-define=STORE_BUILD=false
```

Run macOS app (store version without app name feature):

```bash
fvm flutter run -d macos --dart-define=STORE_BUILD=true
```

---

Release macOS to the store:
```bash
git status --porcelain | grep -q . && { echo "Error: You have uncommitted changes. Commit or stash them before building."; exit 1; } || \
fvm flutter clean && fvm flutter pub get && dart run patch_package apply  && \
fvm flutter build macos --dart-define=STORE_BUILD=true && \
cd macos && \
xcodebuild -workspace Runner.xcworkspace \
           -scheme "Runner" \
           -configuration Release-Store \
           archive \
           -archivePath ../build/macos/Runner.xcarchive && \
xcodebuild -exportArchive \
           -archivePath ../build/macos/Runner.xcarchive \
           -exportPath ../build/macos/pkg \
           -exportOptionsPlist ExportOptions.plist && \
cd .. && \
xcrun altool --upload-app \
             --type macos \
             -f build/macos/pkg/*.pkg \
             --apiKey 26RYSKN534 \
             --apiIssuer d1553ec9-5b60-4cc5-af3c-ca67208a14c0 && \
fvm flutter packages pub run sentry_dart_plugin
```

Build and sign macOS app quickly (non-store):
```bash
git status --porcelain | grep -q . && { echo "Error: You have uncommitted changes. Commit or stash them before building."; exit 1; } || \
fvm flutter build macos --dart-define=STORE_BUILD=false --release && \
cd build/macos/Build/Products/Release && \
open .
```

Build and sign macOS DMG (non-store): !! NOT WORKING USE XCode !!
```bash
git status --porcelain | grep -q . && { echo "Error: You have uncommitted changes. Commit or stash them before building."; exit 1; } || \
fvm flutter clean && fvm flutter pub get && dart run patch_package apply && \
fvm flutter build macos --dart-define=STORE_BUILD=false --release && \
cd build/macos/Build/Products/Release && \
codesign --deep --force --verify --verbose \
  --options runtime \
  --timestamp \
  --sign "Developer ID Application: Fatma Hassan (V2BWCUM7B8)" \
  InterviewHammer.app && \
rm -f InterviewHammer.dmg && \
create-dmg \
  --volname "InterviewHammer" \
  --window-pos 200 120 \
  --window-size 800 400 \
  --icon-size 100 \
  --icon "InterviewHammer.app" 200 190 \
  --hide-extension "InterviewHammer.app" \
  --app-drop-link 600 185 \
  "InterviewHammer.dmg" \
  "InterviewHammer.app" && \
xcrun notarytool submit "InterviewHammer.dmg" \
  --apple-id "<EMAIL>" \
  --team-id "V2BWCUM7B8" \
  --password "fnps-jcge-vtzz-hoez" \
  --wait && \
xcrun stapler staple "InterviewHammer.dmg" && \
fvm flutter packages pub run sentry_dart_plugin && \
open -R "InterviewHammer.dmg"
```

Build android
```bash
git status --porcelain | grep -q . && { echo "Error: You have uncommitted changes. Commit or stash them before building."; exit 1; } || \
fvm flutter clean && fvm flutter pub get && dart run patch_package apply && \
fvm flutter build appbundle && \
fvm flutter packages pub run sentry_dart_plugin && \
open build/app/outputs/bundle/release
```

Release iOS to the store:
```bash
git status --porcelain | grep -q . && { echo "Error: You have uncommitted changes. Commit or stash them before building."; exit 1; } || \
fvm flutter clean && fvm flutter pub get && dart run patch_package apply\
fvm flutter build ipa && \
IPA_FILE=$(ls build/ios/ipa/*.ipa) && \
(unzip -l "$IPA_FILE" | grep ._Symbols && zip -d "$IPA_FILE" "._Symbols/") || echo "No ._Symbols found" && \
xcrun altool --upload-app \
             --type ios \
             -f build/ios/ipa/*.ipa \
             --apiKey 26RYSKN534 \
             --apiIssuer d1553ec9-5b60-4cc5-af3c-ca67208a14c0 && \
fvm flutter packages pub run sentry_dart_plugin && \
open build/ios/ipa
```

Publish to: app.interviewhammer.com
```bash
git status --porcelain | grep -q . && { echo "Error: You have uncommitted changes. Commit or stash them before building."; exit 1; } || \
fvm flutter clean && fvm flutter pub get && dart run patch_package apply && \
peanut -b gh-pages && \
<NAME_EMAIL>-humazed:humazed/interview_hammer.git gh-pages && \
fvm flutter packages pub run sentry_dart_plugin
```

Publish to: web.interviewhammer.com (beta)
```bash
git status --porcelain | grep -q . && { echo "Error: You have uncommitted changes. Commit or stash them before building."; exit 1; } || \
fvm flutter clean && fvm flutter pub get && dart run patch_package apply  && \
peanut -b test && \
<NAME_EMAIL>-humazed:humazed/interview_hammer.git test
```

Publish to ALL in one command
```bash
git status --porcelain | grep -q . && { echo "Error: You have uncommitted changes. Commit or stash them before building."; exit 1; } || \
INITIAL_DIR=$(pwd) && \

echo "=== Preparing Dependencies ===" && \
fvm flutter clean && fvm flutter pub get && dart run patch_package apply && \

echo "=== MacOS Store Release ===" && \
fvm flutter build macos --dart-define=STORE_BUILD=true && \
cd macos && \
xcodebuild -workspace Runner.xcworkspace \
           -scheme "Runner" \
           -configuration Release-Store \
           archive \
           -archivePath ../build/macos/Runner.xcarchive && \
xcodebuild -exportArchive \
           -archivePath ../build/macos/Runner.xcarchive \
           -exportPath ../build/macos/pkg \
           -exportOptionsPlist ExportOptions.plist && \
cd .. && \
xcrun altool --upload-app \
             --type macos \
             -f build/macos/pkg/*.pkg \
             --apiKey 26RYSKN534 \
             --apiIssuer d1553ec9-5b60-4cc5-af3c-ca67208a14c0 && \

echo "\n=== iOS Store Release ===" && \
fvm flutter build ipa && \
IPA_FILE=$(ls build/ios/ipa/*.ipa) && \
(unzip -l "$IPA_FILE" | grep ._Symbols && zip -d "$IPA_FILE" "._Symbols/") || echo "No ._Symbols found" && \
xcrun altool --upload-app \
             --type ios \
             -f build/ios/ipa/*.ipa \
             --apiKey 26RYSKN534 \
             --apiIssuer d1553ec9-5b60-4cc5-af3c-ca67208a14c0 && \

echo "\n=== Android Build ===" && \
fvm flutter build appbundle && \

echo "\n=== Web Production Release ===" && \
peanut -b gh-pages && \
<NAME_EMAIL>-humazed:humazed/interview_hammer.git gh-pages && \

echo "\n=== Run Sentry ===" && \
fvm flutter packages pub run sentry_dart_plugin && \

echo "\n=== Opening Build Files ===" && \
cd "$INITIAL_DIR" && \
open -R "$INITIAL_DIR/build/app/outputs/bundle/release"/*.aab && \
open -R "$INITIAL_DIR/build/ios/ipa"/*.ipa
```

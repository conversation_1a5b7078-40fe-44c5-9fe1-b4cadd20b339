{"rules": ["ALWAYS use .withValues(alpha:) for color transparency: withOpacity/withAlpha are deprecated. Convert integers (0-255) to decimals (0.0-1.0) by dividing by 255.0, but use the clean decimal value in code (NOT the division calculation). Examples: Colors.black.withOpacity(0.5) → Colors.black.withValues(alpha: 0.5), Colors.black.withAlpha(128) → Colors.black.withValues(alpha: 0.5)", "Always use super parameters in constructors where applicable - e.g., 'MyWidget({super.key})' instead of 'MyWidget({Key? key}) : super(key: key)'", "DO NOT instruct, suggest or even attempt to run the application from the terminal after implementing changes - the user will run the application themselves", "Don't remove any of the existing code or comments that is not related to the changes", "Don't add comments like '// Added' for new code since it makes the code ugly", "Organize new code in the following order: 1) public functions and properties at the top, 2) private functions that support the public functions below them, 3) maintain the existing order within each section", "This organization helps with: readability (public interface is immediately visible), maintainability (related functions are grouped together), and code navigation (easier to find the entry points and their implementations)", "It's OK to remove comments ONLY if they are directly related to the code being changed/removed. All other existing comments must be preserved", "IMPORTANT: don't remove comments or commented code that is not related to the changes", "ALWAYS read the docs/PROJECT_BRAIN.md document BEFORE implementing any changes to understand the project architecture, component relationships, and code organization patterns"], "mcpServers": {}}
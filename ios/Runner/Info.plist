<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>InterviewHammer</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>interview_hammer</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSMicrophoneUsageDescription</key>
	<string>Your microphone will be used to record audio for the application.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>We need calendar access to schedule and remind you about upcoming interview practice sessions.</string>
	<key>NSContactsUsageDescription</key>
	<string>Access to contacts allows you to share interview tips and practice sessions with your network.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Photo access is needed to upload your professional profile picture and save interview preparation materials.</string>
	<key>NSAppleMusicUsageDescription</key>
	<string>Media access helps in recording and playing back your interview practice sessions.</string>
	<key>NSMotionUsageDescription</key>
	<string>Motion detection helps analyze your body language during mock interviews.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Speech recognition is used to analyze your responses during interview practice sessions.</string>
	<key>NSSiriUsageDescription</key>
	<string>Siri integration allows you to quickly start interview practice sessions using voice commands.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Location access helps find nearby interview opportunities and networking events.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Location access helps find nearby interview opportunities and networking events while using the app.</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Bluetooth is used to connect with wireless headphones for better audio quality during practice sessions.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
</dict>
</plist>

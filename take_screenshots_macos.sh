#!/bin/bash

# Define the dimensions for screenshots
declare -a dimensions=("1280x800" "1440x900" "2560x1600" "2880x1800")

# Set app name
APP_NAME="InterviewHammer"

# Create a folder to save the screenshots
mkdir -p screenshots/macos

# Function to resize app window and take screenshot
take_screenshot() {
    local width height
    IFS="x" read -r width height <<< "$1"

    echo "Attempting to capture screenshot for dimension: ${width}x${height}"

    # Just activate and resize
    osascript <<EOT
tell application "$APP_NAME"
    activate
end tell

delay 1

tell application "System Events"
    tell process "$APP_NAME"
        set frontmost to true
        set position of window 1 to {0, 0}
        set size of window 1 to {$width, $height}
    end tell
end tell
EOT

    echo "Window resized, waiting to stabilize..."
    sleep 2

    # Create directory for this dimension
    mkdir -p "screenshots/macos/${width}x${height}"

    # Generate filename with timestamp
    local timestamp=$(date +%Y%m%d%H%M%S)
    local filename="screenshots/macos/${width}x${height}/screenshot_${timestamp}.png"

    # Take the screenshot using the -w option
    echo "Please click on the InterviewHammer window when prompted..."
    screencapture -w "$filename"

    if [ -f "$filename" ]; then
        echo "Screenshot saved to: $filename"
        # Resize the screenshot to the correct dimensions
        sips -z $height $width "$filename" >/dev/null 2>&1
        echo "Screenshot resized to ${width}x${height}"
    else
        echo "Failed to save screenshot!"
    fi
    
    sleep 1
}

# Loop over each dimension and take screenshots
for dim in "${dimensions[@]}"; do
    echo "Processing dimension: $dim"
    take_screenshot "$dim"
done

# Restore window to a reasonable size (1024x768)
osascript <<EOT
tell application "$APP_NAME"
    activate
end tell

tell application "System Events"
    tell process "$APP_NAME"
        set frontmost to true
        set position of window 1 to {50, 50}
        set size of window 1 to {1024, 768}
    end tell
end tell
EOT

echo "Screenshot process completed. Window restored to normal size."
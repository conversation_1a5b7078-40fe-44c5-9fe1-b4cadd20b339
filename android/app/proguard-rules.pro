# ---- ONNX Runtime ---------------------------------------------------------
# keep everything under ai.onnxruntime.* because it is accessed from JNI
-keep class ai.onnxruntime.** { *; }
-keep class ai.onnxruntime.**.* { *; }

# If you also bundle models that are loaded via reflection add:
# -keep class ai.onnxruntime.extensions.** { *; }
# --------------------------------------------------------------------------
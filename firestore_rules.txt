rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read: if true;
      allow create, update: if true;
      allow delete: if false;
    }
    match /users/{userId} {
    	allow read: if false;
      allow list: if false;
      allow create, update: if true;
      allow delete: if false;
    }
    match /events/{eventId} {
      allow read: if false;
      allow create, update: if true;
      allow delete: if false;
    }
    match /users_profiles/{profileId} {
      allow list: if false;
      allow read, create, update: if request.auth.uid == profileId;
      allow delete: if false;
    }
  }
}
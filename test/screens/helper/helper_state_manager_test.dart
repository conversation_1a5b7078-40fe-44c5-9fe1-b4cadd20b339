import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:interview_hammer/screens/helper/helper_state_manager.dart';

void main() {
  late HelperStateManager stateManager;
  late List<HelperState> stateChanges;

  setUp(() {
    stateChanges = [];
    stateManager = HelperStateManager(
      onStateChanged: (state) => stateChanges.add(state),
    );
  });

  group('Basic state transitions', () {
    test('initial state should be stopped', () {
      expect(stateManager.currentState, equals(HelperState.stopped));
      expect(stateManager.isListening, isFalse);
      expect(stateManager.isGenerating, isFalse);
      expect(stateManager.recordingState, equals(RecordingState.stopped));
    });

    test('startListening should update state to starting then listening',
        () async {
      final startListeningFuture = stateManager.startListening(() async {
        expect(stateManager.recordingState, equals(RecordingState.starting));
        expect(stateManager.isListening, isFalse);
      });

      await startListeningFuture;

      expect(stateManager.currentState, equals(HelperState.listening));
      expect(stateManager.recordingState, equals(RecordingState.listening));
      expect(stateManager.isListening, isTrue);
    });

    test('stopListening should update state to stopping then stopped',
        () async {
      await stateManager.startListening(() async {});

      final stopListeningFuture = stateManager.stopListening(() async {
        expect(stateManager.recordingState, equals(RecordingState.stopping));
        expect(stateManager.isListening, isFalse);
      });

      await stopListeningFuture;

      expect(stateManager.currentState, equals(HelperState.stopped));
      expect(stateManager.recordingState, equals(RecordingState.stopped));
      expect(stateManager.isListening, isFalse);
    });

    test('multiple start/stop transitions work correctly', () async {
      await stateManager.startListening(() async {});
      expect(stateManager.recordingState, equals(RecordingState.listening));

      await stateManager.stopListening(() async {});
      expect(stateManager.recordingState, equals(RecordingState.stopped));

      await stateManager.startListening(() async {});
      expect(stateManager.recordingState, equals(RecordingState.listening));
    });
  });

  group('Basic operations', () {
    test('generating operation manages state correctly', () async {
      await stateManager.startListening(() async {});
      await stateManager.startGenerating(() async {
        expect(stateManager.currentState, equals(HelperState.generating));
        expect(stateManager.isGenerating, isTrue);
      });
      expect(stateManager.currentState, equals(HelperState.listening));
    });

    test('extracting operation manages state correctly', () async {
      await stateManager.startListening(() async {});
      await stateManager.startExtracting(() async {
        expect(stateManager.currentState, equals(HelperState.extracting));
      });
      expect(stateManager.currentState, equals(HelperState.listening));
    });

    test('operation handles errors and restores state', () async {
      await stateManager.startListening(() async {});
      try {
        await stateManager.startGenerating(() async {
          throw Exception('Test error');
        });
        fail('Should have thrown');
      } catch (e) {
        expect(e, isException);
      }
      expect(stateManager.currentState, equals(HelperState.listening));
    });
  });

  group('Operation state management', () {
    test('recording state changes during operation', () async {
      await stateManager.startListening(() async {});
      final completer = Completer<void>();
      final states = <bool>[];

      final operationFuture = stateManager.startGenerating(() async {
        expect(stateManager.currentState, equals(HelperState.generating));
        await completer.future;
      });

      await stateManager.stopListening(() async {});
      states.add(stateManager.isListening);

      await stateManager.startListening(() async {});
      states.add(stateManager.isListening);

      completer.complete();
      await operationFuture;

      expect(states, equals([false, true]));
      expect(stateManager.currentState, equals(HelperState.listening));
    });

    test('rapid recording state changes during operation', () async {
      final completer = Completer<void>();
      final states = <bool>[];

      final operationFuture = stateManager.startGenerating(() async {
        await completer.future;
      });

      // Rapid state changes
      for (var i = 0; i < 5; i++) {
        if (i.isEven) {
          await stateManager.startListening(() async {});
        } else {
          await stateManager.stopListening(() async {});
        }
        states.add(stateManager.isListening);
      }

      completer.complete();
      await operationFuture;

      expect(states, equals([true, false, true, false, true]));
      expect(stateManager.isListening, isTrue);
      expect(stateManager.currentState, equals(HelperState.listening));
    });

    test('operation cancellation', () async {
      await stateManager.startListening(() async {});

      final extractionFuture = stateManager.startExtracting(() async {
        expect(stateManager.currentState, equals(HelperState.extracting));
        await Future.delayed(const Duration(milliseconds: 50));
      });

      // Start generation while extraction is running
      await stateManager.startGenerating(() async {
        expect(stateManager.currentState, equals(HelperState.generating));
      });

      await extractionFuture;
      expect(stateManager.currentState, equals(HelperState.listening));
    });

    test('sequential operations maintain correct state', () async {
      await stateManager.startListening(() async {});

      await stateManager.startGenerating(() async {
        expect(stateManager.currentState, equals(HelperState.generating));
      });
      expect(stateManager.currentState, equals(HelperState.listening));

      await stateManager.startExtracting(() async {
        expect(stateManager.currentState, equals(HelperState.extracting));
      });
      expect(stateManager.currentState, equals(HelperState.listening));

      await stateManager.stopListening(() async {});
      expect(stateManager.currentState, equals(HelperState.stopped));
    });
  });
}

import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:interview_hammer/api/utils.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';

@GenerateMocks([SharedPreferences])
void main() {
  group('getModel Tests', () {

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
    });

    test('returns correct model string for first call', () async {
      final result = await getModel('test/model');
      expect(result, 'test0/model');
    });

    test('cycles through model numbers correctly', () async {
      expect(await getModel('test/model', 3), 'test0/model');
      expect(await getModel('test/model', 3), 'test1/model');
      expect(await getModel('test/model', 3), 'test2/model');
      expect(await getModel('test/model', 3), 'test0/model');
    });

    test('handles different models independently', () async {
      expect(await getModel('test1/model'), 'test10/model');
      expect(await getModel('test2/model'), 'test20/model');
      expect(await getModel('test1/model'), 'test11/model');
    });
  });

  group('getLastLines Tests', () {
    test('returns last n lines from multiline string', () {
      const text = 'line1\nline2\nline3\nline4\nline5';
      expect(getLastLines(text, 2), 'line4\nline5');
    });

    test('returns entire string if lines requested exceeds total lines', () {
      const text = 'line1\nline2';
      expect(getLastLines(text, 5), 'line1\nline2');
    });

    test('handles empty string', () {
      expect(getLastLines('', 3), '');
    });

    test('handles single line', () {
      expect(getLastLines('single line', 1), 'single line');
    });
  });

  group('getLastMinute Tests', () {
    test('returns last n characters with complete words', () {
      const text = 'This is a long text that should be truncated';
      expect(getLastMinute(text, 20), 'should be truncated');
    });

    test('returns entire text if shorter than limit', () {
      const text = 'Short text';
      expect(getLastMinute(text, 20), 'Short text');
    });

    test('handles empty string', () {
      expect(getLastMinute('', 20), '');
    });
  });

  group('extractJson Tests', () {
    test('extracts JSON from markdown code block', () {
      const input = '```json\n{"key": "value"}\n```';
      expect(extractJson(input), {'key': 'value'});
    });

    test('handles plain JSON input', () {
      const input = '{"key": "value"}';
      expect(extractJson(input), {'key': 'value'});
    });

    test('throws FormatException for invalid JSON', () {
      const input = '```json\n{"key": value}\n```';
      expect(() => extractJson(input), throwsFormatException);
    });
  });

  group('extractJsonFromLLMOutput Tests', () {
    test('extracts JSON from format tags', () {
      const input = '<format>{"key": "value"}</format>';
      expect(extractJsonFromLLMOutput(input), {'key': 'value'});
    });

    test('handles plain JSON input', () {
      const input = '{"key": "value"}';
      expect(extractJsonFromLLMOutput(input), {'key': 'value'});
    });

    test('throws FormatException for invalid JSON', () {
      const input = '<format>{"key": value}</format>';
      expect(() => extractJsonFromLLMOutput(input), throwsFormatException);
    });
  });

  group('extractLastJsonObjectFromString Tests', () {
    test('extracts last JSON object from string', () {
      const input =
          'Some text {"first": "object"} more text {"second": "object"}';
      expect(extractLastJsonObjectFromString(input), {'second': 'object'});
    });

    test('throws FormatException when no JSON object found', () {
      const input = 'No JSON objects here';
      expect(
          () => extractLastJsonObjectFromString(input), throwsFormatException);
    });
  });

  group('PrettyJson Extension Tests', () {
    test('converts object to pretty JSON string', () {
      final obj = {
        'key': 'value',
        'nested': {'inner': 'value'}
      };
      final pretty = obj.toPrettyString();
      expect(pretty.contains('\n'), isTrue);
      expect(pretty.contains('     '), isTrue);
      expect(jsonDecode(pretty), equals(obj));
    });
  });

  group('parseIncompleteJson Tests', () {
    test('parses complete JSON', () {
      const input = '{"key": "value"}';
      expect(parseIncompleteJson(input), {'key': 'value'});
    });

    test('fixes and parses incomplete JSON without space after colon', () {
      const input = '{"key":"value';
      expect(parseIncompleteJson(input), {'key': 'value'});
    });

    test('fixes and parses incomplete JSON with space after colon', () {
      const input = '{"key": "value';
      expect(parseIncompleteJson(input), {'key': 'value'});
    });

    test('handles missing value after colon', () {
      const input = '{"key":';
      expect(parseIncompleteJson(input), {'key': ''});
    });

    test('handles missing value after colon with space', () {
      const input = '{"key": ';
      expect(parseIncompleteJson(input), {'key': ''});
    });

    test('handles multiple key-value pairs', () {
      const input = '{"key1":"value1","key2":"value2';
      expect(parseIncompleteJson(input), {'key1': 'value1', 'key2': 'value2'});
    });
    test('returns empty object for invalid JSON', () {
      const input = 'invalid json';
      expect(parseIncompleteJson(input), {});
    });
  });

  group('retryOperation Tests', () {
    test('succeeds after retry', () async {
      int attempts = 0;

      Future<void> testOperation() async {
        attempts++;
        if (attempts < 2) {
          throw Exception('Failing attempt $attempts');
        }
      }

      await retryOperation(
        testOperation,
        retries: 3,
        delay: Duration(milliseconds: 100),
      );

      expect(attempts, 2);
    });

    test('fails after max retries', () async {
      int attempts = 0;

      Future<void> testOperation() async {
        attempts++;
        throw Exception('Always failing');
      }

      await retryOperation(
        testOperation,
        retries: 3,
        delay: Duration(milliseconds: 100),
      );

      expect(attempts, 3);
    });
  });
}

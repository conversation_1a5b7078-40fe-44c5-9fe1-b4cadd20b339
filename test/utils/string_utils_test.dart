import 'package:flutter_test/flutter_test.dart';
import 'package:interview_hammer/utils/string_utils.dart';

void main() {
  group('StringExtension', () {
    group('capitalize', () {
      test('should capitalize the first letter of a string', () {
        expect('hello'.capitalize(), 'Hello');
        expect('WORLD'.capitalize(), 'WORLD');
        expect(''.capitalize(), '');
      });
    });

    group('capitalizeEachWord', () {
      test('should capitalize the first letter of each word in a string', () {
        expect('hello world'.capitalizeEachWord(), 'Hello World');
        expect('HELLO WORLD'.capitalizeEachWord(), 'HELLO WORLD');
        expect(''.capitalizeEachWord(), '');
        expect(' one two three '.capitalizeEachWord(), ' One Two Three ');
      });
    });

    group('slice', () {
      test('should return a substring based on start and end indices', () {
        expect('hello'.slice(1, 3), 'el');
        expect('hello'.slice(0, 5), 'hello');
        expect('hello'.slice(2), 'llo');
        expect('hello'.slice(0, -1), 'hell');
        expect('hello'.slice(-3, -1), 'll');
      });

      test('handles negative indices correctly', () {
        expect('hello'.slice(-2), 'lo');
        expect('hello'.slice(0, -2), 'hel');
        expect('hello'.slice(-4, -1), 'ell');
      });

      test('handles start > end correctly', () {
        expect('hello'.slice(3, 1), '');
        expect('hello'.slice(-1, -3), '');
      });

      test('clamps indices to the bounds of the string', () {
        expect('hello'.slice(0, 10), 'hello');
        expect('hello'.slice(-10, 2), 'he');
      });

      test('returns an empty string if start index equals end index', () {
        expect('hello'.slice(2, 2), "");
        expect('hello'.slice(-2, -2), "");
      });

      test('handles empty string', () {
        expect(''.slice(0, 1), '');
        expect(''.slice(-1, 1), '');
        expect(''.slice(0, -1), '');
      });

      test('should handle null start and end index', () {
        expect('abc'.slice(), 'abc');
        expect('abc'.slice(1), 'bc');
      });
    });

    group('normalizeBulletAndParagraphSpacing', () {
      test(
          'should replace ". •" with ".\\n\\n•" and ensure two newlines between paragraphs',
          () {
        expect(
            'This is a sentence. •This is a bullet point.'
                .normalizeBulletAndParagraphSpacing(),
            'This is a sentence.\n\n•This is a bullet point.');
        expect(
            'Paragraph 1.\n\nParagraph 2.'.normalizeBulletAndParagraphSpacing(),
            'Paragraph 1.\n\nParagraph 2.');
        expect('  leading space. •Bullet'.normalizeBulletAndParagraphSpacing(),
            '  leading space.\n\n•Bullet');
      });

      test('should handle edge cases correctly', () {
        expect(''.normalizeBulletAndParagraphSpacing(), '');
        expect('. •One\nTwo\n\nThree'.normalizeBulletAndParagraphSpacing(),
            '.\n\n•One\n\nTwo\n\nThree');
      });

      test('should handle multiple consecutive spaces correctly', () {
        expect('Sentence.   •Bullet'.normalizeBulletAndParagraphSpacing(),
            'Sentence.\n\n•Bullet');
      });

      test('handles mixed cases with bullets and paragraphs', () {
        const input =
            'First sentence. •First bullet.\nSecond line.\n\nNew paragraph.';
        const expected =
            'First sentence.\n\n•First bullet.\n\nSecond line.\n\nNew paragraph.';
        expect(input.normalizeBulletAndParagraphSpacing(), expected);
      });

      test('inserts a blank line before a bullet if the previous line has text',
          () {
        const input = 'Some line of text\n•Next bullet';
        const expected = 'Some line of text\n\n•Next bullet';
        expect(input.normalizeBulletAndParagraphSpacing(), equals(expected));
      });

      test('should not modify text without bullet points', () {
        expect(
            'First sentence. Second sentence.'
                .normalizeBulletAndParagraphSpacing(),
            'First sentence. Second sentence.');
        expect('Line 1.\nLine 2.\nLine 3.'.normalizeBulletAndParagraphSpacing(),
            'Line 1.\nLine 2.\nLine 3.');
      });

      group('normalizeBulletAndParagraphSpacing - code block handling', () {
        test('code block alone remains unchanged', () {
          const input = '''
```
•A bullet inside code
Plain text inside code
```
''';

          // Since the entire input is a code block, we expect no changes:
          const expected = '''
```
•A bullet inside code
Plain text inside code
```
''';

          expect(input.normalizeBulletAndParagraphSpacing(), equals(expected));
        });

        test('multiple code blocks remain unchanged', () {
          const input = '''
```
Some code here
•Bullet in code
```

Regular text •Bullet outside

```dart
Another code block
•Another bullet in code
```
''';

          // Both code blocks remain unchanged; only bullet outside them is processed:
          //
          // "Regular text •Bullet outside" -> "Regular text\n\n•Bullet outside"
          const expected = '''
```
Some code here
•Bullet in code
```

Regular text\n\n•Bullet outside

```dart
Another code block
•Another bullet in code
```
''';

          expect(input.normalizeBulletAndParagraphSpacing(), equals(expected));
        });

        test('mixed text with bullet points and code blocks', () {
          const input = '''
Text before code block. •Bullet

```
In-code bullet:
•Should remain
```

Text after code block. •Another bullet
''';

          // Explanation:
          // 1) "Text before code block. •Bullet" -> "Text before code block.\n\n•Bullet"
          // 2) The code block is unchanged:
          //   In-code bullet:
          //   •Should remain
          // 3) "Text after code block. •Another bullet" -> "Text after code block.\n\n•Another bullet"
          const expected = '''
Text before code block.\n\n•Bullet

```
In-code bullet:
•Should remain
```

Text after code block.\n\n•Another bullet
''';

          expect(input.normalizeBulletAndParagraphSpacing(), equals(expected));
        });
      });
    });
  });
}

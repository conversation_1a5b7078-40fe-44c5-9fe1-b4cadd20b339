import 'package:flutter_test/flutter_test.dart';
import 'package:interview_hammer/api/transcription/transcription_manager.dart';

void main() {
  late TranscriptionManager manager;

  setUp(() {
    manager = TranscriptionManager();
  });

  tearDown(() {
    manager.dispose();
  });

  // Helper function to improve readability
  Future<void> waitForThrottle(Duration duration) => Future.delayed(duration * 2);

  group('Initialization and Disposal', () {
    test('should initialize TranscriptionManager without errors', () {
      expect(manager, isNotNull);
    });

    test('should dispose TranscriptionManager without throwing', () {
      expect(() => manager.dispose(), returnsNormally);
    });
  });

  group('Adding Transcriptions', () {
    test('should add interim transcription without throwing', () {
      expect(() => manager.addInterimTranscription('test'), returnsNormally);
    });

    test('should add endpoint transcription without throwing', () {
      expect(() => manager.addEndpointTranscription('test'), returnsNormally);
    });

    test('should handle empty transcriptions gracefully', () {
      expect(() => manager.addInterimTranscription(''), returnsNormally);
      expect(() => manager.addEndpointTranscription(''), returnsNormally);
    });

    test('should handle very long transcriptions', () {
      final longText = 'a' * 10000;
      expect(() => manager.addInterimTranscription(longText), returnsNormally);
      expect(() => manager.addEndpointTranscription(longText), returnsNormally);
    });
  });

  group('Listening to Transcriptions', () {
    test('should emit endpoint transcription through listen callback', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      manager.addEndpointTranscription('endpoint test');
      await waitForThrottle(endpointThrottleDuration);

      expect(events, isNotEmpty);
      expect(events.last.type, equals(TranscriptionType.endpoint));
      expect(events.last.value, equals('endpoint test'));
    });

    test('should emit interim transcription through listen callback', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      manager.addInterimTranscription('interim test');
      await waitForThrottle(interimThrottleDuration);

      expect(events, isNotEmpty);
      expect(events.last.type, equals(TranscriptionType.interim));
      expect(events.last.value, equals('interim test'));
    });
  });

  group('Transcription Prioritization', () {
    test('should prioritize endpoint transcription over interim', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      manager.addInterimTranscription('interim test');
      await Future.delayed(interimWaitDuration ~/ 2);
      manager.addEndpointTranscription('endpoint test');
      await waitForThrottle(endpointThrottleDuration);

      expect(events, isNotEmpty);
      expect(events.last.type, equals(TranscriptionType.endpoint));
      expect(events.last.value, equals('endpoint test'));
    });

    test('should block interim transcriptions after endpoint', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      manager.addEndpointTranscription('endpoint test');
      await Future.delayed(endpointThrottleDuration);
      manager.addInterimTranscription('interim test 1');
      manager.addInterimTranscription('interim test 2');
      await waitForThrottle(interimThrottleDuration);

      expect(events.where((e) => e.type == TranscriptionType.interim), isEmpty);
    });

    test('should resume interim transcriptions after cooldown period', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      manager.addEndpointTranscription('endpoint test');
      await Future.delayed(endpointTimeout + Duration(seconds: 1));
      manager.addInterimTranscription('interim test');
      await waitForThrottle(interimThrottleDuration);

      expect(events.last.type, equals(TranscriptionType.interim));
      expect(events.last.value, equals('interim test'));
    });
  });

  group('Throttling', () {
    test('should limit emission frequency for endpoint transcriptions', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      for (int i = 0; i < 10; i++) {
        manager.addEndpointTranscription('test $i');
        await Future.delayed(Duration(milliseconds: endpointThrottleDuration.inMilliseconds ~/ 10));
      }

      expect(events.length, lessThan(10));
    });

    test('should limit emission frequency for interim transcriptions', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      for (int i = 0; i < 10; i++) {
        manager.addInterimTranscription('test $i');
        await Future.delayed(Duration(milliseconds: interimThrottleDuration.inMilliseconds ~/ 10));
      }

      expect(events.length, lessThan(10));
    });

    test('should emit last transcription after throttle period', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      for (int i = 0; i < 5; i++) {
        manager.addEndpointTranscription('test $i');
        await Future.delayed(Duration(milliseconds: endpointThrottleDuration.inMilliseconds ~/ 5));
      }
      await waitForThrottle(endpointThrottleDuration);

      expect(events.last.value, equals('test 4'));
    });
  });

  group('Edge Cases', () {
    test('should handle rapid switching between interim and endpoint transcriptions', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      for (int i = 0; i < 10; i++) {
        if (i.isEven) {
          manager.addInterimTranscription('interim $i');
        } else {
          manager.addEndpointTranscription('endpoint $i');
        }
        await Future.delayed(Duration(milliseconds: totalThrottleDuration.inMilliseconds ~/ 20));
      }
      await waitForThrottle(totalThrottleDuration);

      expect(events, isNotEmpty);
      expect(events.where((e) => e.type == TranscriptionType.endpoint), isNotEmpty);
    });

    test('should handle unicode characters correctly', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      manager.addEndpointTranscription('🚀 Unicode test 🌈');
      await waitForThrottle(endpointThrottleDuration);

      expect(events.last.value, equals('🚀 Unicode test 🌈'));
    });

    test('should handle special characters correctly', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      manager.addEndpointTranscription('Special chars: !@#\$%^&*()_+{}|:"<>?');
      await waitForThrottle(endpointThrottleDuration);

      expect(events.last.value, equals('Special chars: !@#\$%^&*()_+{}|:"<>?'));
    });
  });

  group('Performance', () {
    test('should handle large number of rapid transcriptions', () async {
      final events = <TranscriptionEvent>[];
      manager.listen((event) => events.add(event));

      for (int i = 0; i < 1000; i++) {
        manager.addInterimTranscription('test $i');
      }
      await waitForThrottle(interimThrottleDuration);

      expect(events, isNotEmpty);
      expect(events.length, lessThan(1000)); // Due to throttling
    });
  });
}
name: interview_hammer
description: "<PERSON><PERSON><PERSON>mer"
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# fvm flutter clean && fvm flutter build appbundle && open build/app/outputs/bundle/release
# fvm flutter clean && fvm flutter build apk && open build/app/outputs/apk/release

# fvm flutter clean && fvm flutter build ipa && open build/ios/ipa
# fvm flutter clean && fvm flutter build ipa && xcrun altool --upload-app --type ios -f build/ios/ipa/*.ipa --apiKey 26RYSKN534 --apiIssuer d1553ec9-5b60-4cc5-af3c-ca67208a14c0

# publish to: web.interviewhammer.com (beta)
# fvm flutter clean && peanut -b test && <NAME_EMAIL>-humazed:humazed/interview_hammer.git test
# publish to: app.interviewhammer.com
# fvm flutter clean && peanut -b gh-pages && <NAME_EMAIL>-humazed:humazed/interview_hammer.git gh-pages
# fvm flutter packages pub run sentry_dart_plugin
# publish to ALL in one command
# fvm flutter clean && peanut -b test && <NAME_EMAIL>-humazed:humazed/interview_hammer.git test && fvm flutter clean && peanut -b gh-pages && <NAME_EMAIL>-humazed:humazed/interview_hammer.git gh-pages && fvm flutter packages pub run sentry_dart_plugin
# cd macos && rm -rf Pods Podfile.lock && pod install --repo-update && cd .. && cd ios && rm -rf Pods Podfile.lock && pod install --repo-update && cd ..
version: 3.1.5+162

environment:
  sdk: '>=3.5.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  http: ^1.4.0
  intl: ^0.20.2
  uuid: ^4.5.1
  permission_handler: ^12.0.0+1
  collection: ^1.19.1
  shared_preferences: ^2.5.3
  rxdart: ^0.28.0
  string_similarity: ^2.1.1
  indent: ^2.0.0
  url_launcher: ^6.3.1
  webview_flutter: ^4.13.0
  auto_size_text: ^3.0.0
  package_info_plus: ^8.3.0
  device_info_plus: ^11.5.0
  provider: ^6.1.5
  path_provider: ^2.1.5
  path: ^1.9.1
  image: ^4.5.4
  crypto: ^3.0.6

  flutter_toolbox: ^11.5.1

  flutter_chat_ui: ^2.5.2
  flyer_chat_image_message: ^2.1.10
  flyer_chat_system_message: ^2.1.9
  flutter_chat_core: ^2.6.1
  cross_cache: ^1.0.2
  gpt_markdown: ^1.1.0

  flutter_chat_types: ^3.6.2
  bubble: ^1.2.1

  #  dart_openai: ^5.1.0
  openai_dart: ^0.5.2

  web_socket_channel: ^3.0.3
  #  record: ^5.1.2
  record:
    path: ../record/record

  fonnx:
    git:
      url: https://github.com/humazed/fonnx.git

  firebase_core: ^3.14.0
  firebase_analytics: ^11.5.0
  firebase_auth: ^5.6.0
  cloud_firestore: ^5.6.9
  firebase_crashlytics: ^4.3.7
  firebase_ui_auth: ^1.17.0
  firebase_remote_config: ^5.4.5
  firebase_messaging: ^15.2.7
  firebase_storage: ^12.4.7
  googleapis_auth: ^2.0.0

  onesignal_flutter:
    git:
      url: https://github.com/milansurelia/OneSignal-Flutter-SDK.git
  sentry_flutter: ^9.1.0
  google_fonts: ^6.2.1
  flutter_smartlook: ^4.1.25
  purchases_flutter: ^8.10.3
  font_awesome_flutter: ^10.8.0

  wakelock_plus: ^1.3.2

  window_manager: ^0.5.0
  screen_retriever: ^0.2.0
  screen_capturer: ^0.2.3
  flutter_acrylic: ^1.1.4
  tray_manager: ^0.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.6
  fake_async: ^1.3.3
  sentry_dart_plugin: ^3.0.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  will_it_run: ^1.0.4
  #  dart run change_app_package_name:main com.interview_ai_assistant.android --android
  #  flutterfire configure
  change_app_package_name: ^1.5.0
  patch_package: ^0.0.8

dependency_overrides:
  freezed_annotation: ^3.0.0

flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/google_fonts/
    - assets/icons/
    - assets/models/sileroVad/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

#https://docs.sentry.io/platforms/flutter/upload-debug/
#flutter packages pub run sentry_dart_plugin
sentry:
  upload_debug_symbols: true
  upload_source_maps: true
  upload_sources: true
  project: interviewhammer_flutter
  org: interviewhammer
  auth_token: 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
  wait_for_processing: true
  log_level: warn

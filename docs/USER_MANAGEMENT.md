# User Management

This document details the user management features in InterviewHammer, including profile data, settings, and subscription management.

## Overview

User management in InterviewHammer encompasses:
- User authentication and profile data
- Interview and answer settings preferences
- Subscription plan management and usage tracking
- Personal information for contextual answers

## Components

### 1. User Authentication

InterviewHammer uses Firebase Authentication for user management:
- Email/password authentication is the primary method
- User state is accessible via `getCurrentFirebaseUser()`
- Profile data is stored in Firestore after authentication

**Key Files:**
- `management_api.dart`: Core authentication functions
- `SplashScreen`: Handles login flow and authentication state

### 2. User Profile

The `UserProfile` model contains all user-specific data:
- Basic information (email, uid)
- Settings and preferences
- Subscription details
- Interview-specific data

**Key Properties:**
- `uid` and `email`: Basic identifiers
- `planType` and related fields: Subscription info
- `interviewLanguages` and `answerLanguage`: Language preferences
- `personalInfoData`: Structured personal information
- `answerSettings`: Answer customization options

### 3. Settings Management

Settings are organized into several categories:
- **Interview Settings**: Language, topic, personal info
- **Answer Settings**: Style, length, structure preferences
- **Session Settings**: Automatic answering, transcript visibility
- **Display Settings**: UI customization and window behavior

**Key Classes:**
- `AnswerSettingsScreen`: UI for answer customization
- `PersonalInfoScreen`: UI for personal data management
- `DisplaySettingsScreen`: UI for appearance settings

### 4. Subscription Management

InterviewHammer offers:
- Free plan with limited minutes
- Premium plans with unlimited usage
- Usage tracking and enforcement

**Key Components:**
- `PlanDetailsScreen`: Shows plan info and upgrade options
- `PurchaseService`: Handles payment processing
- `SessionTimer`: Enforces usage limits
- `updateUsedMinutes()`: Increments usage counter

Minutes used are tracked with:
- `updateUsedMinutes()`: Increments usage counter
- Session timers enforce limits on free accounts
- UI shows remaining/used time

## Data Models

### UserProfile

The main user profile data model includes:
- `uid`: Firebase user ID
- `email`: User's email address
- `createdAt` and `lastLogin`: Timestamps
- `planType`: Subscription level (free, monthly unlimited)
- `usedMinutes` and `availableMinutes`: Usage tracking
- `automaticQuestionAnswering`, `showTranscript`: Feature toggles
- `interviewLanguages`, `answerLanguage`: Language preferences
- `interviewTopic`: Current interview focus
- `personalInfoData`: PersonalInfo object
- `answerSettings`: AnswerSettings object

### PersonalInfo

Structured personal data includes:
- `name`: User's name
- `currentRole`: Current job position
- `company`: Company name
- `yearsOfExperience`: Experience duration
- `workHistory`: Previous positions
- `skills`: List of technical skills
- `education`: Educational background
- `additionalInfo`: Any other relevant info

### AnswerSettings

Answer customization options include:
- `behavioralStructure`: STAR, CAR, etc.
- `responseStyle`: Bullet points, conversational, etc.
- `length`: Short, medium, long
- `questionDetectionSensitivity`: Low, medium, high
- `englishForTechnicalTerms`: Whether to use English for tech terms

## Implementation Details

### UserProfileManager

The `UserProfileManager` singleton:
- Provides access to current user data
- Updates the UI reactively when data changes
- Manages initialization and cleanup

Key methods:
- `initialize()`: Sets up Firestore listeners
- `userProfileStream`: Stream of profile updates
- `userProfile`: Current profile snapshot

### Firestore Integration

User data is stored in Firestore with:
- Document path: `users_profiles/{uid}`
- Custom serialization via `toFirestore()` and `fromFirestore()`
- Merge updates to prevent data loss
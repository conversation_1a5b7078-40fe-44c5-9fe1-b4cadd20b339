# Stealth Mode

This document details the Stealth Mode feature in InterviewHammer, which allows for discreet interview assistance when using screen recording or monitoring software.

## Overview

Stealth Mode uses a client-server architecture, separating the interview environment (desktop) from the assistance UI (mobile/other device). This allows users to receive AI assistance even when their screen is being monitored or recorded.

## Components

### 1. Server (Desktop) Side

The desktop application acts as a server that:
- Creates and manages a session in Firestore
- Captures screenshots when requested
- Processes commands from the client
- Automatically hides in system tray for stealth operation

**Key Classes:**
- `SessionManager`: Main controller for session operations
- `ServerInstructionScreen`: UI for initial session setup
- `ServerSessionScreen`: UI for active session management
- `SystemTrayService`: Manages system tray integration

### 2. Client Side

The mobile/web application acts as a client that:
- Connects to an existing server session
- Requests screenshots from the server
- Shows AI-generated answers
- Provides the complete interview assistance UI

**Key Classes:**
- `SessionsWidget`: Shows available sessions to connect to
- `ServerConnectionDialog`: UI for connecting to a session
- `HelperScreen`: Main interview assistance UI 

### 3. Session Management

Sessions are managed through Firestore with:
- Real-time state synchronization
- Command passing between client and server
- Screenshot storage and retrieval
- Built-in timeouts and failure handling

**Key Files:**
- `session_api.dart`: Core session management functions
- `SessionManager` class: Provider for session state

## Session Data Model

The `Session` model contains:
- `sessionId`: Unique identifier
- `deviceName`: Name of the server device
- `userId`: Firebase auth user ID
- `sessionStartTime`: Timestamp of session creation
- `sessionEndTime`: Timestamp (null if active)
- `status`: WAITING, ACTIVE, or ENDED
- `currentCommand`: TAKE_SCREENSHOT, HIDE_APPLICATION, or IDLE
- `commandTimestamp`: When command was issued
- `commandStatus`: PENDING, COMPLETED, or FAILED
- `screenshots`: List of screenshot references

## Commands

The system supports these command types:
- `TAKE_SCREENSHOT`: Request the server to capture the screen
- `HIDE_APPLICATION`: Request the server to hide to system tray
- `IDLE`: No active command

## User Interface

### Core UI Elements
- **Mode Selection**: Normal (blue) vs Stealth (purple) mode options
- **Server States**: Instruction screen (waiting) and Active Session screen (connected)
- **Client UI**: Sessions list widget and Connection dialog
- **System Tray**: Icon with menu options when app is hidden

### Core Flows
- **Session Establishment**: Server creates → Client discovers → Client connects
- **Screenshot Flow**: Client requests → Server captures → Server uploads → Client processes
- **Command Flow**: Client issues → Server executes → Status updates → Client confirms

See [docs/DESKTOP_INTEGRATION.md](DESKTOP_INTEGRATION.md) for system tray implementation details.

## Comprehensive Workflows

### 1. Starting a Server
- User opens app on desktop device
- Selects "Start" then "Stealth Mode"
- System creates session in Firestore with WAITING status
- Instruction screen displays awaiting connection
- Session appears in the Select Mode screen's active sessions list on all user's devices

### 2. Connecting a Client

- User opens app on mobile device or web browser
- Taps "Start" button on home screen
- Views active sessions in the Select Mode screen
- Taps "Connect" for the desired server
- Connection dialog appears with session details
- Upon confirmation:
  - HIDE_APPLICATION command automatically sent
  - Session status updated to ACTIVE
  - Helper Screen opens on client
  - Server transitions to Active Session screen

### 3. Taking Remote Screenshots
- In the Helper Screen, user taps screenshot button
- TAKE_SCREENSHOT command sent to server
- Server captures screen and uploads to Firebase Storage
- URL added to session's screenshots array
- Client downloads and displays the screenshot
- AI processes the screenshot for questions/answers

### 4. Ending a Session
- User can end session from:
  - Client: "End Session" button
  - Server: "End Session" button
  - Server system tray: "End Session" menu item
- Session status updated to ENDED
- sessionEndTime set to current timestamp
- Server stops monitoring for commands
- Session removed from active sessions list

## Security Considerations

- Sessions are user-scoped via Firebase security rules
- Screenshots are stored in user-specific Firebase Storage paths
- Desktop app can run in reduced opacity or hidden mode
- Command validation ensures only authorized clients can control server

## Implementation Tips

- Use `_sessionManager.createServerSession()` to start a new session
- Listen to session updates with `_listenForClientConnection()`
- Handle commands with `_handleTakeScreenshotCommand()` and `_handleHideApplicationCommand()`
- The server should regularly check command status and update Firestore
- The client should handle connection errors gracefully
- Add retry mechanisms for unreliable operations (uploads, commands)
- Implement appropriate error handling throughout the process
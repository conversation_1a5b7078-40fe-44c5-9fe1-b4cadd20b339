# Desktop Integration

This document details the implementation details of desktop integration features in InterviewHammer, focusing on the key aspects necessary for new feature development.

## Overview

InterviewHammer provides enhanced functionality on desktop platforms (macOS, Windows) through:
- Native desktop features via Flutter desktop plugins
- Electron wrapper for additional capabilities on Windows
- Cross-platform abstractions with platform-specific implementations

## Components

### 1. Window Management

InterviewHammer provides fine-grained control over the application window:
- Transparency/opacity adjustment
- Always-on-top setting
- Taskbar visibility toggle
- Multi-workspace visibility (macOS)
- Screen sharing exclusion

**Key Classes:**
- `DisplaySettingsScreen`: UI for window settings
- `ElectronInteractionService`: Handles Electron window operations
- `window_settings_utils.dart`: Cross-platform window operations

### 2. System Tray Integration

The app can minimize to the system tray and be controlled from there:
- Hide/show application
- End active sessions
- Exit application
- Visual indicators of app status

**Key Components:**
- `SystemTrayService`: Singleton service that manages system tray functionality across platforms.
- `tray_manager` plugin: Used for native desktop platforms (macOS, Windows, Linux).
- Electron IPC bridge: Used for Electron platforms, communicating with the main process.

### 3. Screen Capture

Desktop-specific screen capture capabilities:
- High-quality screenshot capture
- Screen sharing exclusion/visibility control
- Image optimization for API transmission

**Key Classes:**
- `ScreenshotProcessor`: Captures and processes screenshots
- `WebScreenshotService`: Platform-agnostic service abstraction
- Platform-specific implementations

### 4. Electron Integration

For Windows support, InterviewHammer uses an Electron wrapper:
- JavaScript interop bridge
- IPC communication
- Custom window controls

**Key Components:**
- `ElectronInteractionService`: Base abstraction layer
- Platform-specific implementations with conditional imports
- JS interop functions for web/Electron version

## Architecture

### Platform Detection

InterviewHammer uses platform detection constants:
- `kIsNativeDesktop`: True for native desktop platforms
- `kIsWebDesktop`: True for Electron/web on desktop
- `kIsNativeMacOS`: Specifically for native macOS

These are used for conditional imports and feature enablement.

### Abstraction Layers

Desktop features use layered abstractions:
1. **Base Interfaces**: Abstract classes defining capabilities
2. **Platform Services**: Concrete implementations for specific platforms
3. **Utility Functions**: Simplified APIs for common operations

### Electron Communication

Communication with Electron happens through:
1. **JS Interop**: Direct JavaScript function calls from Dart
2. **Preload Script**: Exposes Electron APIs to web context
3. **IPC Main/Renderer**: Passes messages between processes

## Implementation Details

### Window Settings

Window settings are persisted in SharedPreferences:
- `KEY_WINDOW_OPACITY`: Window transparency
- `KEY_WINDOW_ALWAYS_ON_TOP`: Always on top state
- `KEY_WINDOW_HIDE_APP_ICON`: Taskbar visibility
- `KEY_WINDOW_VISIBLE_ALL_WORKSPACES`: Multi-workspace visibility (macOS)
- `KEY_WINDOW_HIDE_FROM_SCREEN_SHARING`: Screen sharing exclusion (macOS, Electron)
- `KEY_SCREEN_SHARING`: Screen sharing key

These settings are applied via the appropriate platform-specific APIs, typically through `window_settings_utils.dart`.

### System Tray Management

The `SystemTrayService` singleton manages tray functionality:
- `init()`: Initializes the tray icon and menu (called internally).
- `hideToTray()`: Hides window to tray.
- `showFromTray()`: Restores window from tray.
- `_refreshTrayMenu()`: Updates tray menu based on session state (called internally).
- Platform-specific logic is handled internally via `tray_manager` or Electron IPC.

### Screenshot Processing

Screenshots follow this process:
1. Capture raw screenshot using platform APIs
2. Process and optimize image (resize, compress)
3. Convert to format suitable for API transmission
4. Cache for reuse to prevent duplicate uploads

### Electron Integration

Electron integration follows this pattern:
1. Define abstract `BaseElectronInteractionService`
2. Create concrete implementations for each platform
3. Use conditional imports for platform-specific code
4. Access via `getElectronInteractionService()` factory function

## Architecture

### Abstraction Layers

Desktop features use layered abstractions:
1. **Base Interfaces**: Abstract classes defining capabilities
2. **Platform Services**: Concrete implementations for specific platforms
3. **Utility Functions**: Simplified APIs for common operations

### Electron Communication

Communication with Electron happens through:
1. **JS Interop**: Direct JavaScript function calls from Dart
2. **Preload Script**: Exposes Electron APIs to web context
3. **IPC Main/Renderer**: Passes messages between processes

## Platform Specifics

### macOS

On native macOS:
- Window control via `window_manager` plugin
- Screen capture via `screen_capturer`
- System tray via `tray_manager`
- Special handling for screen sharing exclusion

### Windows (Electron)

On Windows (via Electron):
- Window control via Electron APIs
- Screen capture via Electron desktopCapturer
- System tray via Electron Tray
- IPC communication for cross-process operations

## Implementation Tips

- Use platform detection constants before accessing platform-specific APIs
- Follow the abstraction pattern for new desktop features
- Handle platform differences gracefully with fallbacks
- Test features on all supported platforms
- Use the provided utility functions in `window_settings_utils.dart`
- For Electron features, ensure preload script exposes necessary functions
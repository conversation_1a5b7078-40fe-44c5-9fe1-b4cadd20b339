# AI Processing Pipeline

This document details the core AI processing pipeline used by InterviewHammer to convert audio input into intelligent answers.

## Overview

The AI pipeline consists of four main components:
1. Audio Capture & Transcription
2. Question Detection
3. Answer Generation  
4. Screenshot Processing (alternative input path)

Audio Input → Transcription → Question Detection → Answer Generation → UI Display

## Pipeline Components

### 1. Audio Capture & Transcription
- Audio is captured from the device microphone
- `TranscriptionServiceFactory` routes to the appropriate service based on language:
  - `DeepgramTranscriptionServiceImpl` for most languages
  - `AzureTranscriptionServiceImpl` for specific languages requiring Azure support (Web/Electron only)
- `TranscriptionManager` handles both interim (ongoing) and endpoint (final) transcriptions
- Applies throttling to prevent duplicate processing

**Key Classes:**
- `TranscriptionServiceFactory`: Determines which transcription service to use
- `DeepgramTranscriptionServiceImpl`: Handles most language transcriptions
- `AzureTranscriptionServiceImpl`: Handles languages that require Azure (Web/Electron only)
- `TranscriptionManager`: Manages transcript data flow and throttling

### 2. Question Detection
- `extractQuestion()` sends transcribed text to OpenAI API
- Prompts are selected based on user's sensitivity settings
- Questions are filtered for duplicates and completeness
- Result contains: question text, whether question is final, and if personal info is required

**Key Functions:**
- `extractQuestion()`: Main function for question detection
- `_getQuestionToAnswer()`: Processes OpenAI response and checks for duplicates
- `_isNewQuestion()`: Determines if the question has already been answered

### 3. Answer Generation
- `answerQuestionStream()` builds a context-specific prompt including:
  - User profile information when required
  - Selected answer style, length and behavioral structure
  - Language preferences and special instructions
- Streaming responses update the UI in real-time
- Fallback mechanisms handle API failures by switching to alternate models

**Key Functions:**
- `answerQuestionStream()`: Main function for generating answers
- `_getQuestionAnsweringSystemPrompt()`: Builds the prompt for the LLM
- `_updateChatWithQuestionAndAnswer()`: Updates the UI with streaming responses

### 4. Screenshot Processing
- Alternative input pathway when audio isn't sufficient
- `ScreenshotProcessor` captures and optimizes screen content
- Screenshots are sent to vision-capable models for question extraction
- Follows the same answer generation flow once questions are detected
- Supports both local screenshots (direct capture) and remote screenshots (from Stealth Mode)

**Key Classes & Functions:**
- `Screenshot` (abstract): Base class for different screenshot types
- `LocalScreenshot`: Contains byte data from direct screen captures
- `RemoteScreenshot`: Contains URLs from Stealth Mode captures  
- `ScreenshotProcessor`: Captures and optimizes local screenshots
- `processImagesForAI()`: Prepares all screenshot types for AI processing
- `takeAndProcessScreenshot()`: User-triggered local screenshot capturing

## Parallel Input Pathways

1. **Audio Transcription Path** (primary)
   - Continuous real-time audio processing and question detection
   - Triggered automatically as speech is detected

2. **Screenshot Path**
   - Manual trigger via "Take Screenshot" button
   - Useful for technical interviews with code or diagrams on screen
   - Uses vision-capable models to extract questions from images
   - Supports both local screenshots (normal mode) and remote screenshots (stealth mode)

3. **Manual Input Path**
   - Direct text input from user
   - Used for corrections or custom queries
   - Bypasses transcription but follows same question detection flow

## LLM Models and Selection

The pipeline uses different OpenAI models based on task requirements:

| Purpose | Primary Model | Fallback Model | Notes |
|---------|--------------|----------------|-------|
| Question Detection | gpt-4o-mini | (retries same) | If `gpt-4o-mini` fails, it's retried once. |
| Answer Generation | gpt-4o | gpt-4o-mini | |
| Image Analysis | gpt-4o | gpt-4o-mini | Falls back to text-only model on failure. |

Model selection follows these guidelines:
- Lighter models (gpt-4o-mini) are used for text-only question detection
- Vision models (gpt-4o) are required for screenshot processing
- Fallback mechanisms automatically switch to alternative models on failure (as defined in the table)
- Model fallbacks are configured in `modelFallbacks` in `openai_client_utils.dart` (e.g., `'gpt-4o': 'gpt-4o-mini'`)

## Prompt Engineering

### Question Detection Sensitivity

Three sensitivity levels are available, controlled by `QuestionDetectionSensitivity` in user settings:

1. **Low Sensitivity**:
   - Only detects explicit questions with clear interrogative structure
   - Requires proper question marks
   - Less prone to false positives

2. **Medium Sensitivity**:
   - Detects both explicit questions and clear implicit inquiries
   - More balanced between sensitivity and specificity
   - Works well for conversational interviews

3. **High Sensitivity**:
   - Identifies subtle implied questions and requests for information
   - May interpret some statements as questions
   - Best for catching all possible questions

### Answer Generation Customization

The answer generation prompt is assembled from multiple components:

1. **Base Prompt**: Core instructions for the LLM
2. **Answer Length**: Short, medium, or long responses
3. **Response Style**: Bullet points, conversational, or example-driven
4. **Behavioral Structure**: STAR, CAR, SOAR, etc. (for behavioral questions)
5. **Personal Information**: Added conditionally based on question requirements
6. **Language Instructions**: Determines the output language and handling of technical terms

All prompt components are defined in `prompt.dart` and assembled in `_getQuestionAnsweringSystemPrompt()`.

## Error Handling and Recovery

The pipeline includes robust error handling:

1. **Model Fallbacks**: If a model fails, automatically retry with fallback model
2. **JSON Parsing**: Handles incomplete or malformed JSON from streaming responses
3. **Duplicate Question Detection**: Prevents answering the same question multiple times
4. **Screenshot Processing**: Gracefully handles failed captures or uploads
5. **Throttling**: Prevents excessive API calls with throttleTime on transcription events

## Performance Optimization

Several optimizations are implemented:

1. **Image Caching**: Local screenshots are cached to prevent redundant uploads
2. **Unified Screenshot Handling**: Common abstraction for both local and remote screenshots
3. **Transcript Throttling**: Limits the frequency of transcription processing
4. **Query Deduplication**: Prevents duplicate question processing
5. **Base64 Encoding**: Uses base64 for immediate image usage while uploading in background
6. **Response Streaming**: Displays answers as they're generated rather than waiting for completion

## Implementation Tips

- The `HelperStateManager` controls the UI state during different stages of processing
- Use `_stateManager.startListening()`, `_stateManager.startGenerating()`, etc. to properly update UI
- Be mindful of state transitions in the pipeline to avoid race conditions
- Screenshot processing should be treated as an alternative path, not a replacement for audio
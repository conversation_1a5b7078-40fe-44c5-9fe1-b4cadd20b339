# Project Brain: InterviewHammer

## Overview

InterviewHammer is a Flutter application that helps interview candidates by providing AI-generated answers based on real-time transcription of interviews. The app runs on Web, Android, iOS, macOS, and Windows (via Electron).

## Architecture & Components

The app follows a feature-first organization with these key layers:

1. **UI Layer**
   - **Screens**: `helper/` (main interview UI), `server/` (Stealth Mode UI), `home/` (settings hub), `plan/` (subscriptions)
   - **Widgets**: Screen-specific in respective directories, shared in `widgets/`

2. **State Management**
   - **Providers**: `SessionManager` (Stealth Mode), `ThemeProvider` (UI themes), `UserProfileManager` (user data singleton)
   - **Pattern**: Provider package for simple state, RxDart for complex streams

3. **Business Logic**
   - **AI**: `api/ai/` for OpenAI interaction
   - **Transcription**: `api/transcription/` for Deepgram/Azure services
   - **Server**: `api/server/` for Stealth Mode session management
   - **Storage**: `api/storage/` for Firebase Storage operations
   - **Management**: `api/management/` for user profile and config
   - **Configuration**: Firebase Remote Config (`api/management/remote_config.dart`) used for feature flags, API keys, and settings

4. **Data Models**
   - **User Data**: `model/management/user_profile.dart`, `model/management/answer_settings.dart`
   - **Session**: `model/server/session.dart` for Stealth Mode
   - **Chat**: `model/question.dart`, `model/text_message.dart`
   - **Pattern**: Models implement `toFirestore()` and `fromFirestore()` methods for serialization

5. **Platform Services**
   - **Electron Bridge**: `services/electron_interaction/` for desktop integration
   - **Screenshot**: `services/screenshot/` for cross-platform capture
   - **System Tray**: `services/system_tray/` for desktop system tray

Entry point is `main.dart` which initializes Firebase, Sentry/Crashlytics, and platform-specific features.

## Core Features

### AI Interview Assistant
- Records audio and transcribes using Deepgram/Azure
- Detects questions with OpenAI
- Generates contextual answers based on user profile, transcript, and screenshots
- Supports multiple languages via `Language` enum, including Arabic
- Customizable answer styles, lengths, and behavioral structures
- Uses different models based on tasks: question detection (gpt-4o-mini), answer generation (gpt-4o)
- Supports parallel input pathways: audio transcription (primary), screenshots, and manual text input

For detailed information on the AI processing pipeline, see [docs/AI_PROCESSING_PIPELINE.md](AI_PROCESSING_PIPELINE.md).

### Stealth Mode (Server-Client)
- Desktop creates Firestore session → Client connects → Client requests screenshots → Server captures and uploads
- Desktop app can hide to system tray while maintaining functionality
- Session state and commands flow through `SessionManager` and Firestore
- Supports commands: TAKE_SCREENSHOT, HIDE_APPLICATION, IDLE
- Server session states: WAITING, ACTIVE, ENDED

For detailed information on Stealth Mode implementation, see [docs/STEALTH_MODE.md](STEALTH_MODE.md).

### User Management
- Profile data (personal info, interview topics, language preferences)
- Answer customization settings (style, length, behavioral structure)
- Subscription plans (free/paid) with usage tracking
- The `UserProfileManager` singleton provides access to current user data

For detailed information on user management features, see [docs/USER_MANAGEMENT.md](USER_MANAGEMENT.md).

### Desktop Integration (Electron)
- Window management (opacity, always-on-top, taskbar visibility)
- Screen capture with platform optimizations
- Screen sharing exclusion
- System tray with custom menus
- Multi-workspace visibility (macOS)
- IPC communication via `ElectronInteractionService`
- Platform-specific implementations with conditional imports

For detailed information on desktop integration, see [docs/DESKTOP_INTEGRATION.md](DESKTOP_INTEGRATION.md).

## Implementation Guidelines

### General Principles
- **Platform Handling**: Use constants from `utils/platform_utils.dart` (e.g., `kIsWeb`, `kIsNativeDesktop`, `kIsWebDesktop`, `kIsNativeMacOS`) for conditional logic.
- **Error Handling**: Custom exceptions (e.g., `SessionException`) are defined in specific API modules.

### Adding New Features
1. **UI Changes**: Add to appropriate screen directory or shared widgets
2. **Business Logic**:
   - AI functionality → `api/ai/`
   - Server communication → `api/server/`
   - Transcription → `api/transcription/`
   - User management → `api/management/`
3. **Platform-specific code**: Use conditional imports with platform detection
4. **Firebase Integration**: Follow model serialization patterns in `UserProfile`
5. **Configuration**: Use Remote Config for values that might need remote updates

### Key Design Patterns

1. **Singleton Services**: Global access to services like `UserProfileManager`, `SessionManager`
2. **Factory Functions**: For platform-specific implementations (e.g., `getElectronInteractionService()`)
3. **Model Serialization**: All models implement `toFirestore()` and `fromFirestore()` methods
4. **Stream-Based Updates**: RxDart's `BehaviorSubject` for reactive state changes
5. **Feature-First Organization**: Features own their related components

### Communication Patterns

1. **Flutter ↔ Electron**: 
   Flutter → `ElectronInteractionService` → JS interop → `window.electronAPI` → IPC → main process

2. **Firebase Data Flow**:
   Data model → `toFirestore()` → Firestore → `snapshots().listen()` → `BehaviorSubject` → UI

3. **Client-Server in Stealth Mode**:
   Client → Firestore commands → Server detects → Server executes → Updates status → Client receives

When implementing new code, always follow existing patterns within feature areas.